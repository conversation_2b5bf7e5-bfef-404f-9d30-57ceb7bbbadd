{"timestamp": "2025-09-08T05:20:52.561Z", "analysis": {"totalSize": 1441452, "files": [], "directories": {"components": {"totalSize": 283065, "files": [], "directories": {"bottomsheets": {"totalSize": 60041, "files": [{"name": "TimePeriodBottomSheet.tsx", "path": "components/bottomsheets/TimePeriodBottomSheet.tsx", "size": 16097, "isLarge": false}, {"name": "CashReconciliationBottomSheet.tsx", "path": "components/bottomsheets/CashReconciliationBottomSheet.tsx", "size": 13163, "isLarge": false}, {"name": "ServiceSelectionBottomSheet.tsx", "path": "components/bottomsheets/ServiceSelectionBottomSheet.tsx", "size": 10767, "isLarge": false}, {"name": "FilterSortBottomSheet.tsx", "path": "components/bottomsheets/FilterSortBottomSheet.tsx", "size": 8523, "isLarge": false}, {"name": "CustomerSelectionBottomSheet.tsx", "path": "components/bottomsheets/CustomerSelectionBottomSheet.tsx", "size": 7065, "isLarge": false}, {"name": "BottomSheet.tsx", "path": "components/bottomsheets/BottomSheet.tsx", "size": 4426, "isLarge": false}], "directories": {}}, "cards": {"totalSize": 20941, "files": [{"name": "DataCard.tsx", "path": "components/cards/DataCard.tsx", "size": 8539, "isLarge": false}, {"name": "InfoCard.tsx", "path": "components/cards/InfoCard.tsx", "size": 7012, "isLarge": false}, {"name": "OrderCard.tsx", "path": "components/cards/OrderCard.tsx", "size": 5237, "isLarge": false}, {"name": "index.ts", "path": "components/cards/index.ts", "size": 153, "isLarge": false}], "directories": {}}, "forms": {"totalSize": 8812, "files": [{"name": "ImagePicker.tsx", "path": "components/forms/ImagePicker.tsx", "size": 5979, "isLarge": false}, {"name": "Search.tsx", "path": "components/forms/Search.tsx", "size": 2833, "isLarge": false}], "directories": {}}, "inventory": {"totalSize": 54847, "files": [{"name": "TransactionHistoryList.tsx", "path": "components/inventory/TransactionHistoryList.tsx", "size": 12186, "isLarge": false}, {"name": "InventoryCard.tsx", "path": "components/inventory/InventoryCard.tsx", "size": 9149, "isLarge": false}, {"name": "WarehouseSelector.tsx", "path": "components/inventory/WarehouseSelector.tsx", "size": 8149, "isLarge": false}, {"name": "StockLevelIndicator.tsx", "path": "components/inventory/StockLevelIndicator.tsx", "size": 7095, "isLarge": false}, {"name": "UnitQuantityInput.tsx", "path": "components/inventory/UnitQuantityInput.tsx", "size": 6738, "isLarge": false}, {"name": "ConversionPreview.tsx", "path": "components/inventory/ConversionPreview.tsx", "size": 5536, "isLarge": false}, {"name": "InventorySearch.tsx", "path": "components/inventory/InventorySearch.tsx", "size": 5101, "isLarge": false}, {"name": "index.ts", "path": "components/inventory/index.ts", "size": 893, "isLarge": false}], "directories": {}}, "navigation": {"totalSize": 15002, "files": [{"name": "Header.tsx", "path": "components/navigation/Header.tsx", "size": 9155, "isLarge": false}, {"name": "NavBar.tsx", "path": "components/navigation/NavBar.tsx", "size": 5847, "isLarge": false}], "directories": {}}, "reports": {"totalSize": 2449, "files": [{"name": "MetricCard.tsx", "path": "components/reports/MetricCard.tsx", "size": 2449, "isLarge": false}], "directories": {}}, "scanner": {"totalSize": 3721, "files": [{"name": "QRScanner.tsx", "path": "components/scanner/QRScanner.tsx", "size": 3721, "isLarge": false}], "directories": {}}, "security": {"totalSize": 26112, "files": [{"name": "SecurityMonitor.tsx", "path": "components/security/SecurityMonitor.tsx", "size": 15780, "isLarge": false}, {"name": "SecureForm.tsx", "path": "components/security/SecureForm.tsx", "size": 10332, "isLarge": false}], "directories": {}}, "ui": {"totalSize": 76587, "files": [{"name": "Dropdown.tsx", "path": "components/ui/Dropdown.tsx", "size": 8667, "isLarge": false}, {"name": "ActionSheet.tsx", "path": "components/ui/ActionSheet.tsx", "size": 8180, "isLarge": false}, {"name": "TextInput.tsx", "path": "components/ui/TextInput.tsx", "size": 8109, "isLarge": false}, {"name": "DatePicker.tsx", "path": "components/ui/DatePicker.tsx", "size": 7774, "isLarge": false}, {"name": "Card.tsx", "path": "components/ui/Card.tsx", "size": 7535, "isLarge": false}, {"name": "Chip.tsx", "path": "components/ui/Chip.tsx", "size": 6419, "isLarge": false}, {"name": "Button.tsx", "path": "components/ui/Button.tsx", "size": 5978, "isLarge": false}, {"name": "Toast.tsx", "path": "components/ui/Toast.tsx", "size": 5620, "isLarge": false}, {"name": "EmptyState.tsx", "path": "components/ui/EmptyState.tsx", "size": 4737, "isLarge": false}, {"name": "ChipGroup.tsx", "path": "components/ui/ChipGroup.tsx", "size": 4073, "isLarge": false}, {"name": "Switch.tsx", "path": "components/ui/Switch.tsx", "size": 3350, "isLarge": false}, {"name": "StatCardGroup.tsx", "path": "components/ui/StatCardGroup.tsx", "size": 2323, "isLarge": false}, {"name": "ItemRow.tsx", "path": "components/ui/ItemRow.tsx", "size": 1519, "isLarge": false}, {"name": "NotificationItem.tsx", "path": "components/ui/NotificationItem.tsx", "size": 1512, "isLarge": false}, {"name": "index.ts", "path": "components/ui/index.ts", "size": 791, "isLarge": false}], "directories": {}}, "utils": {"totalSize": 6357, "files": [{"name": "ErrorBoundary.tsx", "path": "components/utils/ErrorBoundary.tsx", "size": 6357, "isLarge": false}], "directories": {}}}}, "config": {"totalSize": 9838, "files": [{"name": "masterDatabase.config.ts", "path": "config/masterDatabase.config.ts", "size": 6107, "isLarge": false}, {"name": "constants.ts", "path": "config/constants.ts", "size": 3731, "isLarge": false}], "directories": {}}, "context": {"totalSize": 108393, "files": [{"name": "DataContext.tsx", "path": "context/DataContext.tsx", "size": 65435, "isLarge": false}, {"name": "FinancialContext.tsx", "path": "context/FinancialContext.tsx", "size": 4996, "isLarge": false}, {"name": "ThemeContext.tsx", "path": "context/ThemeContext.tsx", "size": 4930, "isLarge": false}, {"name": "AuthContext.tsx", "path": "context/AuthContext.tsx", "size": 3583, "isLarge": false}, {"name": "ToastContext.tsx", "path": "context/ToastContext.tsx", "size": 3321, "isLarge": false}], "directories": {"__tests__": {"totalSize": 26128, "files": [{"name": "DataContext.test.tsx", "path": "context/__tests__/DataContext.test.tsx", "size": 17285, "isLarge": false}, {"name": "AuthContext.test.tsx", "path": "context/__tests__/AuthContext.test.tsx", "size": 8843, "isLarge": false}], "directories": {}}}}, "hooks": {"totalSize": 60865, "files": [{"name": "useSecurity.ts", "path": "hooks/useSecurity.ts", "size": 9301, "isLarge": false}, {"name": "useAuthActions.ts", "path": "hooks/useAuthActions.ts", "size": 5092, "isLarge": false}, {"name": "useCashReconciliation.ts", "path": "hooks/useCashReconciliation.ts", "size": 4383, "isLarge": false}, {"name": "useFinancialActions.ts", "path": "hooks/useFinancialActions.ts", "size": 4236, "isLarge": false}, {"name": "useAppInitializer.ts", "path": "hooks/useAppInitializer.ts", "size": 2676, "isLarge": false}, {"name": "useInventoryFilter.ts", "path": "hooks/useInventoryFilter.ts", "size": 2585, "isLarge": false}, {"name": "useImagePicker.ts", "path": "hooks/useImagePicker.ts", "size": 1788, "isLarge": false}, {"name": "useConversion.ts", "path": "hooks/useConversion.ts", "size": 1639, "isLarge": false}, {"name": "usePermissions.ts", "path": "hooks/usePermissions.ts", "size": 847, "isLarge": false}, {"name": "useDebounce.ts", "path": "hooks/useDebounce.ts", "size": 354, "isLarge": false}, {"name": "useSafeInsets.ts", "path": "hooks/useSafeInsets.ts", "size": 278, "isLarge": false}, {"name": "index.ts", "path": "hooks/index.ts", "size": 145, "isLarge": false}], "directories": {"__tests__": {"totalSize": 21393, "files": [{"name": "useAuthActions.test.ts", "path": "hooks/__tests__/useAuthActions.test.ts", "size": 11189, "isLarge": false}, {"name": "usePermissions.test.ts", "path": "hooks/__tests__/usePermissions.test.ts", "size": 10204, "isLarge": false}], "directories": {}}}}, "navigation": {"totalSize": 12553, "files": [{"name": "AppNavigator.tsx", "path": "navigation/AppNavigator.tsx", "size": 6907, "isLarge": false}, {"name": "TabNavigator.tsx", "path": "navigation/TabNavigator.tsx", "size": 3039, "isLarge": false}, {"name": "RootNavigator.tsx", "path": "navigation/RootNavigator.tsx", "size": 1504, "isLarge": false}, {"name": "AuthNavigator.tsx", "path": "navigation/AuthNavigator.tsx", "size": 1103, "isLarge": false}], "directories": {}}, "screens": {"totalSize": 611889, "files": [{"name": "QRScannerScreen.tsx", "path": "screens/QRScannerScreen.tsx", "size": 7290, "isLarge": false}, {"name": "index.ts", "path": "screens/index.ts", "size": 2566, "isLarge": false}], "directories": {"auth": {"totalSize": 9256, "files": [{"name": "LoginScreen.tsx", "path": "screens/auth/LoginScreen.tsx", "size": 9200, "isLarge": false}, {"name": "index.ts", "path": "screens/auth/index.ts", "size": 56, "isLarge": false}], "directories": {}}, "business": {"totalSize": 94304, "files": [{"name": "FinancialScreen.tsx", "path": "screens/business/FinancialScreen.tsx", "size": 23048, "isLarge": false}, {"name": "TaxSummaryScreen.tsx", "path": "screens/business/TaxSummaryScreen.tsx", "size": 15308, "isLarge": false}, {"name": "ProfitLossScreen.tsx", "path": "screens/business/ProfitLossScreen.tsx", "size": 11679, "isLarge": false}, {"name": "DashboardScreen.tsx", "path": "screens/business/DashboardScreen.tsx", "size": 10283, "isLarge": false}, {"name": "AddServiceScreen.tsx", "path": "screens/business/AddServiceScreen.tsx", "size": 9479, "isLarge": false}, {"name": "TransactionHistoryScreen.tsx", "path": "screens/business/TransactionHistoryScreen.tsx", "size": 9232, "isLarge": false}, {"name": "ReportsScreen.tsx", "path": "screens/business/ReportsScreen.tsx", "size": 7568, "isLarge": false}, {"name": "ServiceScreen.tsx", "path": "screens/business/ServiceScreen.tsx", "size": 6343, "isLarge": false}, {"name": "index.ts", "path": "screens/business/index.ts", "size": 1364, "isLarge": false}], "directories": {}}, "customer": {"totalSize": 50244, "files": [{"name": "CustomersScreen.tsx", "path": "screens/customer/CustomersScreen.tsx", "size": 22401, "isLarge": false}, {"name": "CustomerDetailsScreen.tsx", "path": "screens/customer/CustomerDetailsScreen.tsx", "size": 15048, "isLarge": false}, {"name": "AddCustomerScreen.tsx", "path": "screens/customer/AddCustomerScreen.tsx", "size": 12587, "isLarge": false}, {"name": "index.ts", "path": "screens/customer/index.ts", "size": 208, "isLarge": false}], "directories": {}}, "inventory": {"totalSize": 131293, "files": [{"name": "InventoryItemDetailScreen.tsx", "path": "screens/inventory/InventoryItemDetailScreen.tsx", "size": 24727, "isLarge": false}, {"name": "WarehouseManagementScreen.tsx", "path": "screens/inventory/WarehouseManagementScreen.tsx", "size": 23925, "isLarge": false}, {"name": "StockTransferScreen.tsx", "path": "screens/inventory/StockTransferScreen.tsx", "size": 21555, "isLarge": false}, {"name": "AddItemScreen.tsx", "path": "screens/inventory/AddItemScreen.tsx", "size": 18359, "isLarge": false}, {"name": "StockOperationsScreen.tsx", "path": "screens/inventory/StockOperationsScreen.tsx", "size": 17761, "isLarge": false}, {"name": "InventoryDashboardScreen.tsx", "path": "screens/inventory/InventoryDashboardScreen.tsx", "size": 15704, "isLarge": false}, {"name": "InventoryItemsScreen.tsx", "path": "screens/inventory/InventoryItemsScreen.tsx", "size": 8730, "isLarge": false}, {"name": "index.ts", "path": "screens/inventory/index.ts", "size": 532, "isLarge": false}], "directories": {}}, "management": {"totalSize": 71992, "files": [{"name": "StaffManagementScreen.tsx", "path": "screens/management/StaffManagementScreen.tsx", "size": 27367, "isLarge": false}, {"name": "DataScreen.tsx", "path": "screens/management/DataScreen.tsx", "size": 13036, "isLarge": false}, {"name": "PaymentMethodsScreen.tsx", "path": "screens/management/PaymentMethodsScreen.tsx", "size": 12749, "isLarge": false}, {"name": "AddStaffScreen.tsx", "path": "screens/management/AddStaffScreen.tsx", "size": 9222, "isLarge": false}, {"name": "StaffDetailsScreen.tsx", "path": "screens/management/StaffDetailsScreen.tsx", "size": 9188, "isLarge": false}, {"name": "index.ts", "path": "screens/management/index.ts", "size": 430, "isLarge": false}], "directories": {}}, "orders": {"totalSize": 97471, "files": [{"name": "OrderDetailsScreen.tsx", "path": "screens/orders/OrderDetailsScreen.tsx", "size": 39260, "isLarge": false}, {"name": "CreateOrderScreen.tsx", "path": "screens/orders/CreateOrderScreen.tsx", "size": 31032, "isLarge": false}, {"name": "OrderSuccessScreen.tsx", "path": "screens/orders/OrderSuccessScreen.tsx", "size": 14852, "isLarge": false}, {"name": "OrdersScreen.tsx", "path": "screens/orders/OrdersScreen.tsx", "size": 12327, "isLarge": false}], "directories": {}}, "reports": {"totalSize": 20837, "files": [{"name": "ProductsReport.tsx", "path": "screens/reports/ProductsReport.tsx", "size": 5703, "isLarge": false}, {"name": "SalesReport.tsx", "path": "screens/reports/SalesReport.tsx", "size": 5671, "isLarge": false}, {"name": "CustomersReport.tsx", "path": "screens/reports/CustomersReport.tsx", "size": 5593, "isLarge": false}, {"name": "OverviewReport.tsx", "path": "screens/reports/OverviewReport.tsx", "size": 3870, "isLarge": false}], "directories": {}}, "settings": {"totalSize": 68789, "files": [{"name": "SecuritySettingsScreen.tsx", "path": "screens/settings/SecuritySettingsScreen.tsx", "size": 18918, "isLarge": false}, {"name": "NotificationsScreen.tsx", "path": "screens/settings/NotificationsScreen.tsx", "size": 13843, "isLarge": false}, {"name": "EditProfileScreen.tsx", "path": "screens/settings/EditProfileScreen.tsx", "size": 12863, "isLarge": false}, {"name": "ActivityLogScreen.tsx", "path": "screens/settings/ActivityLogScreen.tsx", "size": 12836, "isLarge": false}, {"name": "ProfileScreen.tsx", "path": "screens/settings/ProfileScreen.tsx", "size": 10061, "isLarge": false}, {"name": "index.ts", "path": "screens/settings/index.ts", "size": 268, "isLarge": false}], "directories": {}}, "support": {"totalSize": 49651, "files": [{"name": "SearchScreen.tsx", "path": "screens/support/SearchScreen.tsx", "size": 15890, "isLarge": false}, {"name": "AboutScreen.tsx", "path": "screens/support/AboutScreen.tsx", "size": 9256, "isLarge": false}, {"name": "AppStatusScreen.tsx", "path": "screens/support/AppStatusScreen.tsx", "size": 8736, "isLarge": false}, {"name": "ContactSupportScreen.tsx", "path": "screens/support/ContactSupportScreen.tsx", "size": 8021, "isLarge": false}, {"name": "HelpFAQScreen.tsx", "path": "screens/support/HelpFAQScreen.tsx", "size": 7369, "isLarge": false}, {"name": "index.ts", "path": "screens/support/index.ts", "size": 379, "isLarge": false}], "directories": {}}}}, "services": {"totalSize": 225072, "files": [{"name": "MasterDatabaseService.ts", "path": "services/MasterDatabaseService.ts", "size": 59858, "isLarge": false}, {"name": "AuthService.ts", "path": "services/AuthService.ts", "size": 25238, "isLarge": false}, {"name": "SecurityService.ts", "path": "services/SecurityService.ts", "size": 24066, "isLarge": false}, {"name": "StorageService.ts", "path": "services/StorageService.ts", "size": 13624, "isLarge": false}, {"name": "notificationService.ts", "path": "services/notificationService.ts", "size": 13141, "isLarge": false}, {"name": "DataPersistenceService.ts", "path": "services/DataPersistenceService.ts", "size": 9348, "isLarge": false}, {"name": "financialService.ts", "path": "services/financialService.ts", "size": 7947, "isLarge": false}, {"name": "QRCodeService.ts", "path": "services/QRCodeService.ts", "size": 7411, "isLarge": false}, {"name": "PerformanceMonitoringService.ts", "path": "services/PerformanceMonitoringService.ts", "size": 6277, "isLarge": false}, {"name": "ImageProcessingService.ts", "path": "services/ImageProcessingService.ts", "size": 5411, "isLarge": false}, {"name": "ServiceService.ts", "path": "services/ServiceService.ts", "size": 4246, "isLarge": false}, {"name": "ToastService.ts", "path": "services/ToastService.ts", "size": 1516, "isLarge": false}, {"name": "LoggingService.ts", "path": "services/LoggingService.ts", "size": 1362, "isLarge": false}], "directories": {"__tests__": {"totalSize": 39479, "files": [{"name": "AuthService.test.ts", "path": "services/__tests__/AuthService.test.ts", "size": 14423, "isLarge": false}, {"name": "SecurityService.test.ts", "path": "services/__tests__/SecurityService.test.ts", "size": 13848, "isLarge": false}, {"name": "MasterDatabaseService.test.ts", "path": "services/__tests__/MasterDatabaseService.test.ts", "size": 11208, "isLarge": false}], "directories": {}}}}, "theme": {"totalSize": 18378, "files": [{"name": "theme.ts", "path": "theme/theme.ts", "size": 15233, "isLarge": false}, {"name": "commonStyles.ts", "path": "theme/commonStyles.ts", "size": 2435, "isLarge": false}, {"name": "paperThemeAdapter.ts", "path": "theme/paperThemeAdapter.ts", "size": 710, "isLarge": false}], "directories": {}}, "types": {"totalSize": 38887, "files": [{"name": "index.ts", "path": "types/index.ts", "size": 20343, "isLarge": false}, {"name": "navigation.ts", "path": "types/navigation.ts", "size": 5428, "isLarge": false}, {"name": "inventory.ts", "path": "types/inventory.ts", "size": 5111, "isLarge": false}, {"name": "financial.ts", "path": "types/financial.ts", "size": 3720, "isLarge": false}, {"name": "order.ts", "path": "types/order.ts", "size": 2420, "isLarge": false}, {"name": "data.ts", "path": "types/data.ts", "size": 1570, "isLarge": false}, {"name": "business.ts", "path": "types/business.ts", "size": 175, "isLarge": false}, {"name": "global.d.ts", "path": "types/global.d.ts", "size": 120, "isLarge": false}, {"name": "settings.ts", "path": "types/settings.ts", "size": 0, "isLarge": false}], "directories": {}}, "utils": {"totalSize": 64316, "files": [{"name": "pdfInvoiceGenerator.ts", "path": "utils/pdfInvoiceGenerator.ts", "size": 16054, "isLarge": false}, {"name": "UnitConverter.ts", "path": "utils/UnitConverter.ts", "size": 11165, "isLarge": false}, {"name": "phosphorIconRegistry.tsx", "path": "utils/phosphorIconRegistry.tsx", "size": 11143, "isLarge": false}, {"name": "errorHandler.ts", "path": "utils/errorHandler.ts", "size": 6115, "isLarge": false}, {"name": "storageUtils.ts", "path": "utils/storageUtils.ts", "size": 4360, "isLarge": false}, {"name": "currency.ts", "path": "utils/currency.ts", "size": 2441, "isLarge": false}, {"name": "phoneUtils.ts", "path": "utils/phoneUtils.ts", "size": 2129, "isLarge": false}, {"name": "themeUtils.ts", "path": "utils/themeUtils.ts", "size": 468, "isLarge": false}, {"name": "math.ts", "path": "utils/math.ts", "size": 297, "isLarge": false}], "directories": {"__tests__": {"totalSize": 3996, "files": [{"name": "UnitConverter.test.ts", "path": "utils/__tests__/UnitConverter.test.ts", "size": 2095, "isLarge": false}, {"name": "math.test.ts", "path": "utils/__tests__/math.test.ts", "size": 1162, "isLarge": false}, {"name": "currency.test.ts", "path": "utils/__tests__/currency.test.ts", "size": 739, "isLarge": false}], "directories": {}}}}}}, "summary": {"totalSize": 1441452, "totalSizeFormatted": "1.37 MB", "largeFiles": 0}}