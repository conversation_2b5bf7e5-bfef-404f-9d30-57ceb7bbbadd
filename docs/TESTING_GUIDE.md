# Testing Strategy & Implementation Guide

## Overview

TailorZa implements a comprehensive testing strategy to ensure code quality, reliability, and maintainability. This guide covers testing patterns, best practices, and implementation details for the testing infrastructure.

## Testing Architecture

### Test Pyramid Strategy

```
        ┌─────────────────┐
        │   E2E Tests     │ (10%)
        │  Integration    │
        ├─────────────────┤
        │ Component Tests │ (20%)
        │  React Testing  │
        ├─────────────────┤
        │   Unit Tests    │ (70%)
        │ Business Logic  │
        └─────────────────┘
```

### Coverage Goals

- **Overall Coverage**: 70%+
- **Business Logic**: 90%+
- **Critical Paths**: 100%
- **UI Components**: 60%+

## Testing Framework Configuration

### Jest Configuration

```javascript
// jest.config.js
module.exports = {
  preset: 'react-native',
  testEnvironment: 'node',
  setupFilesAfterEnv: [
    '<rootDir>/src/setupTests.ts',
    '@testing-library/jest-native/extend-expect'
  ],
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|expo|@expo|@react-navigation)/)'
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/**/*.test.{ts,tsx}',
    '!src/setupTests.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
```

### Test Setup

```typescript
// src/setupTests.ts
import 'react-native-gesture-handler/jestSetup';
import { jest } from '@jest/globals';

// Mock React Native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock Expo modules
jest.mock('expo-secure-store', () => ({
  setItemAsync: jest.fn().mockResolvedValue(undefined),
  getItemAsync: jest.fn().mockResolvedValue(null),
  deleteItemAsync: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('expo-local-authentication', () => ({
  authenticateAsync: jest.fn().mockResolvedValue({ success: true }),
  hasHardwareAsync: jest.fn().mockResolvedValue(true),
  isEnrolledAsync: jest.fn().mockResolvedValue(true),
}));

// Global test utilities
global.mockAsyncStorage = {
  getItem: jest.fn().mockResolvedValue(null),
  setItem: jest.fn().mockResolvedValue(undefined),
  removeItem: jest.fn().mockResolvedValue(undefined),
  clear: jest.fn().mockResolvedValue(undefined),
};
```

## Unit Testing Patterns

### Service Layer Testing

```typescript
// src/services/__tests__/AuthService.test.ts
import { AuthService } from '../AuthService';
import { MasterDatabaseService } from '../MasterDatabaseService';

// Mock dependencies
jest.mock('../MasterDatabaseService');
jest.mock('../LoggingService');

describe('AuthService', () => {
  let authService: AuthService;
  let mockDatabase: jest.Mocked<MasterDatabaseService>;

  beforeEach(() => {
    mockDatabase = MasterDatabaseService as jest.Mocked<typeof MasterDatabaseService>;
    authService = new AuthService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication', () => {
    test('should authenticate valid credentials', async () => {
      // Arrange
      const credentials = { username: '<EMAIL>', password: 'password123' };
      const mockUser = { id: '1', username: '<EMAIL>', role: 'user' };
      
      mockDatabase.findUser = jest.fn().mockResolvedValue(mockUser);
      authService.verifyPassword = jest.fn().mockResolvedValue(true);

      // Act
      const result = await authService.authenticate(credentials);

      // Assert
      expect(result.success).toBe(true);
      expect(result.user).toEqual(mockUser);
      expect(mockDatabase.findUser).toHaveBeenCalledWith(credentials.username);
    });

    test('should reject invalid credentials', async () => {
      // Arrange
      const credentials = { username: '<EMAIL>', password: 'wrongpassword' };
      
      mockDatabase.findUser = jest.fn().mockResolvedValue(null);

      // Act
      const result = await authService.authenticate(credentials);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid credentials');
    });
  });

  describe('Session Management', () => {
    test('should create valid session', async () => {
      // Arrange
      const user = { id: '1', username: '<EMAIL>', role: 'user' };

      // Act
      const session = await authService.createSession(user);

      // Assert
      expect(session).toHaveProperty('token');
      expect(session).toHaveProperty('expiresAt');
      expect(session.userId).toBe(user.id);
    });

    test('should validate active session', async () => {
      // Arrange
      const user = { id: '1', username: '<EMAIL>', role: 'user' };
      const session = await authService.createSession(user);

      // Act
      const isValid = await authService.validateSession(session.token);

      // Assert
      expect(isValid).toBe(true);
    });
  });
});
```

### Hook Testing

```typescript
// src/hooks/__tests__/useAuthActions.test.ts
import { renderHook, act } from '@testing-library/react-native';
import { useAuthActions } from '../useAuthActions';
import { AuthContext } from '../../context/AuthContext';

const mockAuthContext = {
  user: null,
  isAuthenticated: false,
  login: jest.fn(),
  logout: jest.fn(),
  loading: false
};

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthContext.Provider value={mockAuthContext}>
    {children}
  </AuthContext.Provider>
);

describe('useAuthActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should handle login successfully', async () => {
    // Arrange
    const { result } = renderHook(() => useAuthActions(), { wrapper });
    const credentials = { username: '<EMAIL>', password: 'password123' };
    
    mockAuthContext.login.mockResolvedValue({ success: true });

    // Act
    await act(async () => {
      await result.current.handleLogin(credentials);
    });

    // Assert
    expect(mockAuthContext.login).toHaveBeenCalledWith(credentials);
  });

  test('should handle logout', async () => {
    // Arrange
    const { result } = renderHook(() => useAuthActions(), { wrapper });

    // Act
    await act(async () => {
      await result.current.handleLogout();
    });

    // Assert
    expect(mockAuthContext.logout).toHaveBeenCalled();
  });
});
```

### Context Testing

```typescript
// src/context/__tests__/DataContext.test.tsx
import React from 'react';
import { render, act } from '@testing-library/react-native';
import { DataProvider, useData } from '../DataContext';
import { Text, Button } from 'react-native';

const TestComponent = () => {
  const { state, actions } = useData();
  
  return (
    <>
      <Text testID="customer-count">{state.customers.length}</Text>
      <Button
        title="Add Customer"
        onPress={() => actions.addCustomer({
          id: '1',
          name: 'Test Customer',
          phone: '************'
        })}
      />
    </>
  );
};

describe('DataContext', () => {
  test('should provide initial state', () => {
    const { getByTestId } = render(
      <DataProvider>
        <TestComponent />
      </DataProvider>
    );

    expect(getByTestId('customer-count')).toHaveTextContent('0');
  });

  test('should handle adding customer', async () => {
    const { getByTestId, getByText } = render(
      <DataProvider>
        <TestComponent />
      </DataProvider>
    );

    const addButton = getByText('Add Customer');

    await act(async () => {
      addButton.props.onPress();
    });

    expect(getByTestId('customer-count')).toHaveTextContent('1');
  });
});
```

## Component Testing

### Screen Component Testing

```typescript
// src/screens/__tests__/DashboardScreen.test.tsx
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { DashboardScreen } from '../DashboardScreen';
import { useData } from '../../context/DataContext';
import { useNavigation } from '@react-navigation/native';

// Mock dependencies
jest.mock('../../context/DataContext');
jest.mock('@react-navigation/native');
jest.mock('../../services/PerformanceMonitoringService');

const mockUseData = useData as jest.MockedFunction<typeof useData>;
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
};

describe('DashboardScreen', () => {
  beforeEach(() => {
    mockUseData.mockReturnValue({
      state: {
        customers: [],
        orders: [],
        products: [],
        loading: false
      },
      actions: {
        reloadData: jest.fn().mockResolvedValue(undefined)
      }
    });

    (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
  });

  test('should render dashboard stats', () => {
    const { getByText } = render(<DashboardScreen />);

    expect(getByText('Dashboard')).toBeTruthy();
    expect(getByText('Total Customers')).toBeTruthy();
    expect(getByText('Total Orders')).toBeTruthy();
  });

  test('should handle refresh action', async () => {
    const mockReloadData = jest.fn().mockResolvedValue(undefined);
    mockUseData.mockReturnValue({
      state: { customers: [], orders: [], products: [], loading: false },
      actions: { reloadData: mockReloadData }
    });

    const { getByTestId } = render(<DashboardScreen />);
    const refreshControl = getByTestId('refresh-control');

    fireEvent(refreshControl, 'refresh');

    await waitFor(() => {
      expect(mockReloadData).toHaveBeenCalled();
    });
  });
});
```

### UI Component Testing

```typescript
// src/components/__tests__/CustomerCard.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { CustomerCard } from '../CustomerCard';

const mockCustomer = {
  id: '1',
  name: 'John Doe',
  phone: '************',
  email: '<EMAIL>',
  address: '123 Main St',
  createdAt: '2023-01-01'
};

describe('CustomerCard', () => {
  test('should render customer information', () => {
    const { getByText } = render(
      <CustomerCard customer={mockCustomer} onPress={jest.fn()} />
    );

    expect(getByText('John Doe')).toBeTruthy();
    expect(getByText('************')).toBeTruthy();
    expect(getByText('<EMAIL>')).toBeTruthy();
  });

  test('should handle press events', () => {
    const mockOnPress = jest.fn();
    const { getByTestId } = render(
      <CustomerCard customer={mockCustomer} onPress={mockOnPress} />
    );

    const card = getByTestId('customer-card');
    fireEvent.press(card);

    expect(mockOnPress).toHaveBeenCalledWith(mockCustomer);
  });

  test('should handle missing optional fields', () => {
    const customerWithoutEmail = { ...mockCustomer, email: undefined };
    
    const { queryByText } = render(
      <CustomerCard customer={customerWithoutEmail} onPress={jest.fn()} />
    );

    expect(queryByText('<EMAIL>')).toBeNull();
  });
});
```

## Integration Testing

### Database Integration Tests

```typescript
// src/services/__tests__/integration/DatabaseIntegration.test.ts
import { MasterDatabaseService } from '../../MasterDatabaseService';

describe('Database Integration', () => {
  let database: MasterDatabaseService;

  beforeAll(async () => {
    database = MasterDatabaseService.getInstance();
    await database.initializeDatabase();
  });

  afterAll(async () => {
    await database.close();
  });

  beforeEach(async () => {
    await database.clearAllTables();
  });

  test('should perform complete customer workflow', async () => {
    // Create customer
    const customer = {
      id: '1',
      name: 'Integration Test Customer',
      phone: '************',
      email: '<EMAIL>'
    };

    await database.addCustomer(customer);

    // Retrieve customer
    const retrievedCustomer = await database.getCustomer('1');
    expect(retrievedCustomer).toMatchObject(customer);

    // Update customer
    const updatedCustomer = { ...customer, phone: '************' };
    await database.updateCustomer('1', updatedCustomer);

    const updatedRetrieved = await database.getCustomer('1');
    expect(updatedRetrieved.phone).toBe('************');

    // Delete customer
    await database.deleteCustomer('1');
    const deletedCustomer = await database.getCustomer('1');
    expect(deletedCustomer).toBeNull();
  });
});
```

### Context Integration Tests

```typescript
// src/context/__tests__/integration/AuthDataIntegration.test.tsx
import React from 'react';
import { render, act } from '@testing-library/react-native';
import { AuthProvider } from '../../AuthContext';
import { DataProvider } from '../../DataContext';
import { useAuth } from '../../AuthContext';
import { useData } from '../../DataContext';

const IntegrationTestComponent = () => {
  const { user, login } = useAuth();
  const { state, actions } = useData();

  React.useEffect(() => {
    if (user) {
      actions.reloadData();
    }
  }, [user]);

  return (
    <Text testID="user-data">
      {user ? `${user.name}: ${state.customers.length} customers` : 'Not logged in'}
    </Text>
  );
};

describe('Auth-Data Integration', () => {
  test('should load user data after authentication', async () => {
    const { getByTestId } = render(
      <AuthProvider>
        <DataProvider>
          <IntegrationTestComponent />
        </DataProvider>
      </AuthProvider>
    );

    // Initially not logged in
    expect(getByTestId('user-data')).toHaveTextContent('Not logged in');

    // Simulate login
    await act(async () => {
      // Login logic
    });

    // Should show user data
    expect(getByTestId('user-data')).toHaveTextContent('customers');
  });
});
```

## Test Utilities

### Custom Render Function

```typescript
// src/utils/test-utils.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react-native';
import { ThemeProvider } from '../context/ThemeContext';
import { ToastProvider } from '../context/ToastContext';

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider>
      <ToastProvider>
        {children}
      </ToastProvider>
    </ThemeProvider>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react-native';
export { customRender as render };
```

### Mock Factories

```typescript
// src/utils/test-factories.ts
export const createMockCustomer = (overrides = {}) => ({
  id: '1',
  name: 'Test Customer',
  phone: '************',
  email: '<EMAIL>',
  address: '123 Test St',
  createdAt: '2023-01-01',
  ...overrides
});

export const createMockOrder = (overrides = {}) => ({
  id: '1',
  customerId: '1',
  totalAmount: 100.00,
  status: 'pending',
  items: [],
  createdAt: '2023-01-01',
  ...overrides
});

export const createMockProduct = (overrides = {}) => ({
  id: '1',
  name: 'Test Product',
  category: 'clothing',
  price: 50.00,
  stock: 10,
  itemType: 'product',
  ...overrides
});
```

## Performance Testing

### Render Performance Tests

```typescript
// src/__tests__/performance/RenderPerformance.test.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import { CustomersList } from '../../components/CustomersList';
import { createMockCustomer } from '../../utils/test-factories';

describe('Render Performance', () => {
  test('should render large customer list efficiently', () => {
    const largeCustomerList = Array.from({ length: 1000 }, (_, index) =>
      createMockCustomer({ id: index.toString(), name: `Customer ${index}` })
    );

    const startTime = performance.now();
    
    render(<CustomersList customers={largeCustomerList} />);
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render within reasonable time
    expect(renderTime).toBeLessThan(100); // 100ms threshold
  });
});
```

## Test Automation

### Pre-commit Hooks

```bash
#!/bin/sh
# .husky/pre-commit

echo "Running pre-commit checks..."

# Run tests
npm test -- --passWithNoTests --watchAll=false

# Check test coverage
npm run test:coverage

# Lint checks
npm run lint

echo "Pre-commit checks completed!"
```

### CI/CD Test Pipeline

```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

## Testing Best Practices

### 1. Test Structure (AAA Pattern)

```typescript
test('should handle user authentication', async () => {
  // Arrange
  const credentials = { username: 'test', password: 'pass' };
  const mockUser = { id: '1', username: 'test' };
  mockAuthService.authenticate.mockResolvedValue(mockUser);

  // Act
  const result = await authService.login(credentials);

  // Assert
  expect(result.success).toBe(true);
  expect(result.user).toEqual(mockUser);
});
```

### 2. Descriptive Test Names

```typescript
describe('Customer Management', () => {
  test('should create customer with valid data');
  test('should reject customer creation with invalid phone number');
  test('should update existing customer information');
  test('should prevent deletion of customer with active orders');
});
```

### 3. Test Data Management

```typescript
// Use factories for consistent test data
const validCustomer = createMockCustomer();
const invalidCustomer = createMockCustomer({ phone: 'invalid' });

// Test edge cases
const customerWithLongName = createMockCustomer({ 
  name: 'A'.repeat(256) 
});
```

### 4. Mock Management

```typescript
describe('OrderService', () => {
  let mockDatabase: jest.Mocked<MasterDatabaseService>;
  let orderService: OrderService;

  beforeEach(() => {
    // Fresh mocks for each test
    mockDatabase = createMockDatabase();
    orderService = new OrderService(mockDatabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
```

## Coverage Reporting

### Coverage Configuration

```javascript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/types/**'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    },
    // Critical modules require higher coverage
    'src/services/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

### Coverage Analysis

```bash
# Generate detailed coverage report
npm run test:coverage

# View coverage in browser
open coverage/lcov-report/index.html

# Coverage summary
npm run test:coverage:summary
```

---

*This testing guide ensures comprehensive coverage and quality assurance for the TailorZa application across all layers of the architecture.*