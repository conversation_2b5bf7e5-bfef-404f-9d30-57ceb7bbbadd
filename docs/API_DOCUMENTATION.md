# 📚 TailorZap API Documentation

## 🎯 Overview

This document provides comprehensive API documentation for the TailorZap React Native application, covering all services, components, and utilities.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────┐
│        Presentation Layer           │
│   (29 Screens + 41 Components)      │
├─────────────────────────────────────┤
│         Business Logic Layer        │
│   (5 Contexts + 19 Services)        │
├─────────────────────────────────────┤
│       Authentication Layer          │
│  (AuthService + SessionManager)     │
├─────────────────────────────────────┤
│         Security Layer              │
│  (SecurityService + Validation)     │
├─────────────────────────────────────┤
│          Data Layer                 │
│ (SQLite + AsyncStorage + Cache)     │
└─────────────────────────────────────┘
```

## 🔐 Authentication API

### AuthService

The central authentication service providing session management and security.

#### Methods

##### `login(credentials: LoginCredentials): Promise<UserSession>`

Authenticates a user and creates a new session.

**Parameters:**
- `credentials`: Object containing username and password
  - `username: string` - User's login name
  - `password: string` - User's password
  - `rememberMe?: boolean` - Optional flag to remember login

**Returns:** Promise resolving to UserSession object

**Example:**
```typescript
import AuthService from '../services/AuthService';

const handleLogin = async () => {
  try {
    const session = await AuthService.login({
      username: 'demo',
      password: 'demo123',
      rememberMe: true
    });
    console.log('Login successful:', session.username);
  } catch (error) {
    console.error('Login failed:', error.message);
  }
};
```

##### `logout(): Promise<void>`

Logs out the current user and clears all session data.

**Example:**
```typescript
const handleLogout = async () => {
  try {
    await AuthService.logout();
    console.log('Logout successful');
  } catch (error) {
    console.error('Logout failed:', error.message);
  }
};
```

##### `getCurrentSession(): Promise<UserSession | null>`

Retrieves the current user session if valid.

**Returns:** Promise resolving to UserSession or null

##### `isAuthenticated(): Promise<boolean>`

Checks if the user is currently authenticated.

**Returns:** Promise resolving to boolean

##### `getSessionInfo(): Promise<SessionInfo>`

Gets detailed session information for debugging and monitoring.

**Returns:** Promise resolving to SessionInfo object

## 🗄️ Data Management API

### DataContext

React context providing centralized data management.

#### Hook: `useData()`

**Returns:** Object containing:
- `state`: Current data state
- `actions`: Available data actions

**Example:**
```typescript
import { useData } from '../context/DataContext';

const MyComponent = () => {
  const { state, actions } = useData();
  
  useEffect(() => {
    actions.loadCustomers();
  }, []);

  return (
    <View>
      {state.customers.map(customer => (
        <Text key={customer.id}>{customer.name}</Text>
      ))}
    </View>
  );
};
```

### SQLite Services

#### CustomerSQLiteService

##### `getAll(): Promise<Customer[]>`

Retrieves all customers from the database.

##### `getById(id: string): Promise<Customer | null>`

Retrieves a specific customer by ID.

##### `create(customer: Omit<Customer, 'id'>): Promise<Customer>`

Creates a new customer record.

##### `update(id: string, updates: Partial<Customer>): Promise<Customer>`

Updates an existing customer record.

##### `delete(id: string): Promise<void>`

Deletes a customer record.

**Example:**
```typescript
import CustomerSQLiteService from '../services/database/CustomerSQLiteService';

const addCustomer = async () => {
  try {
    const newCustomer = await CustomerSQLiteService.create({
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Main St'
    });
    console.log('Customer created:', newCustomer.id);
  } catch (error) {
    console.error('Failed to create customer:', error);
  }
};
```

## 🎨 UI Components API

### Button Component

A customizable button component with multiple variants.

#### Props

```typescript
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  style?: ViewStyle;
}
```

#### Example

```typescript
import Button from '../components/ui/Button';

<Button
  title="Save Changes"
  onPress={handleSave}
  variant="primary"
  size="large"
  icon="check"
  loading={isSaving}
/>
```

### Card Component

A flexible card component for displaying content.

#### Props

```typescript
interface CardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  style?: ViewStyle;
  elevation?: number;
}
```

## 🔒 Security API

### SecurityService

Provides encryption, validation, and security utilities.

#### Methods

##### `encrypt(data: string): string`

Encrypts sensitive data using AES encryption.

##### `decrypt(encryptedData: string): string`

Decrypts previously encrypted data.

##### `validateInput(input: string, type: InputType): boolean`

Validates user input against security patterns.

##### `sanitizeInput(input: string): string`

Sanitizes input to prevent XSS attacks.

**Example:**
```typescript
import SecurityService from '../services/SecurityService';

const secureData = SecurityService.encrypt('sensitive information');
const validated = SecurityService.validateInput(email, 'email');
const clean = SecurityService.sanitizeInput(userInput);
```

## 📊 Performance API

### PerformanceMonitor

Tracks application performance metrics.

#### Methods

##### `startMeasure(name: string, metadata?: Record<string, unknown>): void`

Starts measuring a performance metric.

##### `endMeasure(name: string): number | null`

Ends measurement and returns duration in milliseconds.

##### `measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T>`

Measures async function execution time.

**Example:**
```typescript
import PerformanceMonitor from '../utils/performanceMonitor';

// Measure async operation
const data = await PerformanceMonitor.measureAsync(
  'loadCustomers',
  () => CustomerService.getAll()
);

// Manual measurement
PerformanceMonitor.startMeasure('renderComponent');
// ... component rendering
PerformanceMonitor.endMeasure('renderComponent');
```

## 🧪 Testing API

### TestUtils

Provides testing utilities and mocks.

#### Functions

##### `render(component: ReactElement, options?: CustomRenderOptions)`

Renders components with all necessary providers.

##### `mockNavigation`

Mock navigation object for testing.

##### `mockUser`, `mockCustomer`, `mockOrder`

Pre-defined mock data for testing.

**Example:**
```typescript
import { render, mockNavigation } from '../utils/testUtils';
import MyScreen from '../screens/MyScreen';

test('renders correctly', () => {
  const { getByText } = render(
    <MyScreen navigation={mockNavigation} />
  );
  
  expect(getByText('Welcome')).toBeTruthy();
});
```

## 🎨 Theme API

### ThemeContext

Provides theme management and dark/light mode switching.

#### Hook: `useTheme()`

**Returns:** Theme object with colors, spacing, and typography.

**Example:**
```typescript
import { useTheme } from '../context/ThemeContext';

const MyComponent = () => {
  const theme = useTheme();
  
  return (
    <View style={{ backgroundColor: theme.colors.background }}>
      <Text style={{ color: theme.colors.onSurface }}>
        Hello World
      </Text>
    </View>
  );
};
```

## 📱 Navigation API

### Navigation Types

```typescript
type RootStackParamList = {
  Main: undefined;
  CustomerDetails: { customerId: string };
  AddOrder: { customerId?: string };
  // ... other routes
};
```

### Navigation Hooks

```typescript
import { useNavigation } from '@react-navigation/native';

const navigation = useNavigation<RootStackNavigationProp>();

// Navigate to screen
navigation.navigate('CustomerDetails', { customerId: '123' });

// Go back
navigation.goBack();
```

## 🔧 Utility APIs

### Validation Utils

```typescript
import { validateEmail, validatePhone } from '../utils/validation';

const isValidEmail = validateEmail('<EMAIL>');
const isValidPhone = validatePhone('+1234567890');
```

### Cache Utils

```typescript
import CacheService from '../services/CacheService';

// Store data
await CacheService.set('key', data, 3600); // 1 hour TTL

// Retrieve data
const cachedData = await CacheService.get('key');

// Clear cache
await CacheService.clear();
```

## 📝 Error Handling

### Error Types

```typescript
interface AppError {
  code: string;
  message: string;
  details?: string;
  timestamp: number;
}
```

### Error Boundary

```typescript
import ErrorBoundary from '../components/utils/ErrorBoundary';

<ErrorBoundary onRetry={handleRetry}>
  <MyComponent />
</ErrorBoundary>
```

## 🚀 Best Practices

### 1. Authentication

- Always check authentication status before accessing protected resources
- Use the `useAuth` hook for authentication state
- Handle session expiry gracefully

### 2. Data Management

- Use the `useData` hook for centralized data access
- Implement proper loading and error states
- Cache frequently accessed data

### 3. Performance

- Use `React.memo` for expensive components
- Implement proper list virtualization
- Monitor performance with PerformanceMonitor

### 4. Security

- Always validate and sanitize user input
- Use SecurityService for encryption
- Follow secure coding practices

### 5. Testing

- Use TestUtils for consistent testing setup
- Mock external dependencies
- Test both happy path and error scenarios

## 📞 Support

For additional help or questions about the API:

1. Check the inline code documentation
2. Review the example implementations
3. Consult the troubleshooting guide
4. Contact the development team

---

**Last Updated:** January 2025  
**Version:** 1.0.0