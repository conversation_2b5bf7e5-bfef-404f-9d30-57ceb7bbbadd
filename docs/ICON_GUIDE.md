# Icon System Guide for TailorZap

This guide explains how to use and manage icons in the TailorZap app.

## Overview

TailorZap uses **Phosphor Icons** through the `phosphor-react-native` package.
All icons are centrally managed through the `phosphorIconRegistry.tsx` file to
ensure consistency and type safety.

## Using Icons

### Basic Usage

```tsx
import { PhosphorIcon } from '../utils/phosphorIconRegistry';

// Basic icon usage
<PhosphorIcon name="heart" size={24} color="#FF0000" />

// With different weights
<PhosphorIcon name="star" size={20} color="#FFD700" weight="fill" />
```

### Available Props

- `name`: Icon name (kebab-case, e.g., "magnifying-glass")
- `size`: Icon size in pixels (default: 24)
- `color`: Icon color (default: "#000000")
- `weight`: Icon weight - "thin", "light", "regular", "bold", "fill" (default:
  "regular")
- `style`: Additional styles

### Common Icon Names

```tsx
// Navigation
<PhosphorIcon name="house" />           // Home
<PhosphorIcon name="arrow-left" />      // Back
<PhosphorIcon name="arrow-right" />     // Forward
<PhosphorIcon name="gear" />            // Settings

// Actions
<PhosphorIcon name="plus" />            // Add
<PhosphorIcon name="minus" />           // Remove
<PhosphorIcon name="edit" />            // Edit
<PhosphorIcon name="trash" />           // Delete
<PhosphorIcon name="save" />            // Save

// Business
<PhosphorIcon name="storefront" />      // Store
<PhosphorIcon name="receipt" />         // Orders
<PhosphorIcon name="package" />         // Products
<PhosphorIcon name="users" />           // Customers
<PhosphorIcon name="credit-card" />     // Payments

// Status
<PhosphorIcon name="check-circle" />    // Success
<PhosphorIcon name="warning" />         // Warning
<PhosphorIcon name="info" />            // Information
<PhosphorIcon name="question" />        // Help
```

## Adding New Icons

### Method 1: Using the Add Icon Script (Recommended)

```bash
npm run add-icon <icon-name>
```

Example:

```bash
npm run add-icon heart
npm run add-icon magnifying-glass
npm run add-icon user-circle
```

### Method 2: Manual Addition

1. **Import the icon** in `phosphorIconRegistry.tsx`:

   ```tsx
   import { Heart } from 'phosphor-react-native';
   ```

2. **Add to type definition**:

   ```tsx
   export type PhosphorIconName = 'existing-icons' | 'heart'; // Add here
   ```

3. **Add to icon mapping**:

   ```tsx
   const PHOSPHOR_ICONS: Record<PhosphorIconName, React.ComponentType<any>> = {
     // ... existing icons
     heart: Heart, // Add here
   };
   ```

4. **Add to validator** (optional):
   ```tsx
   export const COMMON_ICONS = [
     // ... existing icons
     'heart', // Add here
   ];
   ```

## Icon Validation

The app automatically validates all icons on startup in development mode. You'll
see validation reports in the console:

```
=== Icon Validation Report ===
Total icons: 150
Valid icons: 150
Invalid icons: 0
✅ All icons are valid!
=============================
```

If there are missing icons, you'll see warnings:

```
⚠️ Found 2 missing icons: heart, star
```

## Troubleshooting

### Icon Not Found Warning

If you see a warning like:

```
WARN  Phosphor Icon "grid-four" not found
```

1. Check if the icon name is correct (kebab-case)
2. Verify the icon exists in `phosphor-react-native`
3. Add the icon using `npm run add-icon grid-four`

### Icon Not Displaying

1. Check the icon name spelling
2. Verify the icon is properly imported
3. Check the console for warnings
4. Ensure the icon is added to the registry

### TypeScript Errors

If you get TypeScript errors about icon names:

1. Make sure the icon is added to the `PhosphorIconName` type
2. Check that the icon mapping includes the icon
3. Restart your TypeScript server

## Best Practices

### Icon Naming

- Use kebab-case for icon names: `"magnifying-glass"` not `"magnifyingGlass"`
- Be descriptive: `"user-circle"` not `"user"`
- Use consistent naming across the app

### Icon Sizes

- Use standard sizes: 16, 20, 24, 32, 48
- 16px: Small UI elements
- 20px: Buttons and actions
- 24px: Default size
- 32px: Large buttons
- 48px: Feature icons

### Icon Colors

- Use theme colors when possible: `theme.colors.primary`
- Use semantic colors: `theme.colors.error` for warnings
- Ensure good contrast with backgrounds

### Performance

- Icons are cached and optimized
- Use consistent icon names to avoid duplicates
- The registry prevents duplicate imports

## Available Icons

The complete list of available icons can be found in:

- `src/utils/phosphorIconRegistry.tsx` - Icon registry
- `src/utils/iconValidator.ts` - Common icons list

## Phosphor Icons Documentation

For the complete list of available Phosphor icons, visit:
https://phosphoricons.com/

## Migration from Other Icon Libraries

If migrating from other icon libraries:

1. Replace icon imports with `PhosphorIcon`
2. Convert icon names to kebab-case
3. Update icon props to match PhosphorIcon interface
4. Add missing icons to the registry

Example migration:

```tsx
// Before (Material Icons)
import { MaterialIcons } from '@expo/vector-icons';
<MaterialIcons name='home' size={24} color='black' />;

// After (Phosphor Icons)
import { PhosphorIcon } from '../utils/phosphorIconRegistry';
<PhosphorIcon name='house' size={24} color='black' />;
```

## Support

If you encounter issues with icons:

1. Check the console for validation reports
2. Verify icon names on phosphoricons.com
3. Use the add-icon script for new icons
4. Check the icon registry for existing icons
