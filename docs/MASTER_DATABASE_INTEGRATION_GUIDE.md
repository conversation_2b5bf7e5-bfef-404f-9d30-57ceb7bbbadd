# Master Database Integration Guide

## Overview

This guide helps you migrate from the fragmented database system to the new **Master Database Service** - a single, unified database solution that consolidates all database operations, storage, caching, and sync functionality.

## 🎯 Benefits of Master Database Service

- **Single Service**: One service handles everything (database, cache, storage, sync)
- **Better Performance**: Built-in caching and query optimization
- **Offline Support**: Automatic offline sync with conflict resolution
- **Health Monitoring**: Real-time database health and performance tracking
- **Auto Backup**: Automatic backups with restore functionality
- **Fallback Support**: Automatic AsyncStorage fallback when SQLite fails
- **Type Safety**: Full TypeScript support with proper interfaces

## 🚀 Quick Migration Steps

### 1. Run the Migration Script

```typescript
import { executeMasterDatabaseMigration } from './src/scripts/migrateToMasterDatabase';

// Execute migration
const result = await executeMasterDatabaseMigration();
console.log('Migration result:', result);
```

### 2. Update Your App Initialization

**Before (App.tsx):**
```typescript
import UnifiedDatabaseService from './src/services/UnifiedDatabaseService';
import DatabaseIntegrationHelper from './src/services/DatabaseIntegrationHelper';

// Old initialization
const dbHelper = DatabaseIntegrationHelper.getInstance();
await dbHelper.initialize();
```

**After (App.tsx):**
```typescript
import MasterDatabaseService from './src/services/MasterDatabaseService';
import { getConfig } from './src/config/masterDatabase.config';

// New initialization
const masterDb = MasterDatabaseService.getInstance();
const config = getConfig('production'); // or 'development'
await masterDb.initialize({
  databaseName: config.database.name,
  enableCache: config.cache.enabled,
  enableOfflineSync: config.sync.enabled,
  enableMonitoring: config.monitoring.enabled,
  enableAutoBackup: config.backup.enabled,
});
```

### 3. Update Your Component Imports

**Before:**
```typescript
import UnifiedDatabaseService from '../services/UnifiedDatabaseService';
import { StorageService } from '../services/storageService';
import CacheService from '../services/CacheService';
import { OfflineSyncService } from '../services/OfflineSyncService';
```

**After:**
```typescript
import MasterDatabaseService from '../services/MasterDatabaseService';

// That's it! Everything is in one service
```

### 4. Update Your Database Operations

**Before:**
```typescript
// Multiple services for different operations
const customers = await UnifiedDatabaseService.getInstance().getCustomers();
const cachedData = await CacheService.get('customers');
await StorageService.set('app_settings', settings);
await OfflineSyncService.addToQueue('customers', 'create', customerData);
```

**After:**
```typescript
// Single service for everything
const masterDb = MasterDatabaseService.getInstance();

// Database operations (with built-in caching)
const customers = await masterDb.getCustomers();

// Settings (built-in)
await masterDb.setSetting('app', 'theme', 'dark');
const theme = await masterDb.getSetting('app', 'theme');

// Offline sync (automatic)
const customer = await masterDb.createCustomer(customerData); // Automatically queued if offline
```

## 📋 API Reference

### Core Operations

```typescript
const masterDb = MasterDatabaseService.getInstance();

// Generic CRUD operations
await masterDb.create<Customer>('customers', customerData);
const customer = await masterDb.findById<Customer>('customers', id);
const customers = await masterDb.findAll<Customer>('customers', { outlet_id: 'outlet1' });
await masterDb.update<Customer>('customers', id, updateData);
await masterDb.delete('customers', id);
await masterDb.search<Customer>('customers', 'john', ['name', 'email']);
```

### Entity-Specific Methods

```typescript
// Customer operations
await masterDb.createCustomer(customerData);
const customers = await masterDb.getCustomers({ outlet_id: 'outlet1' });
const customer = await masterDb.getCustomerById(id);
await masterDb.updateCustomer(id, updateData);
const results = await masterDb.searchCustomers('john doe');

// Product operations
await masterDb.createProduct(productData);
const products = await masterDb.getProducts({ category: 'shirts' });
const product = await masterDb.getProductById(id);

// Order operations
await masterDb.createOrder(orderData);
const orders = await masterDb.getOrders({ status: 'pending' });
const { order, items } = await masterDb.getOrderWithItems(orderId);

// And many more...
```

### Settings Management

```typescript
// Set/get settings
await masterDb.setSetting('app', 'theme', 'dark');
const theme = await masterDb.getSetting('app', 'theme');

// Get all settings in a category
const appSettings = await masterDb.getSettingsByCategory('app');
```

### Health & Monitoring

```typescript
// Check database health
const health = masterDb.getHealth();
console.log('Connected:', health.isConnected);
console.log('Healthy:', health.isHealthy);
console.log('Avg Query Time:', health.performance.avgQueryTime);

// Get cache statistics
const cacheStats = masterDb.getCacheStats();
console.log('Cache Hit Rate:', cacheStats.hitRate);

// Get table statistics
const tableStats = await masterDb.getTableStats();
console.log('Customer count:', tableStats.customers);
```

### Backup & Restore

```typescript
// Create backup
const backupId = await masterDb.createBackup();

// Restore backup
await masterDb.restoreBackup(backupId);
```

### Offline Sync

```typescript
// Set online/offline status
masterDb.setOnlineStatus(false); // App goes offline
masterDb.setOnlineStatus(true);  // App comes back online (auto-sync starts)

// All database operations automatically queue when offline
// and sync when back online
```

## 🔧 Configuration

The Master Database Service uses a single configuration file:

```typescript
// src/config/masterDatabase.config.ts
import { getConfig } from '../config/masterDatabase.config';

// Get environment-specific config
const config = getConfig('production'); // 'development', 'production', or 'test'

// Initialize with custom config
await masterDb.initialize({
  databaseName: 'my_custom.db',
  enableCache: true,
  cacheSize: 2000,
  enableOfflineSync: true,
  syncInterval: 60000, // 1 minute
  enableAutoBackup: true,
  backupInterval: 1800000, // 30 minutes
});
```

## 🧹 Cleanup Old Files

After migration, run the cleanup script to remove redundant files:

```typescript
import { executeCleanup } from './src/scripts/cleanupRedundantFiles';

// This will backup old files before removing them
const result = await executeCleanup(true);
console.log('Cleanup result:', result);
```

## 🔄 Migration Checklist

- [ ] Run migration script: `executeMasterDatabaseMigration()`
- [ ] Update App.tsx initialization
- [ ] Update all component imports
- [ ] Replace old service calls with MasterDatabaseService calls
- [ ] Test all functionality
- [ ] Run cleanup script: `executeCleanup()`
- [ ] Remove backup files after confirming everything works

## 🚨 Common Issues & Solutions

### Issue: "Database not initialized"
**Solution:** Make sure to call `await masterDb.initialize()` in your App.tsx before using the service.

### Issue: "Cache not working"
**Solution:** Ensure `enableCache: true` in your configuration.

### Issue: "Offline sync not working"
**Solution:** 
1. Ensure `enableOfflineSync: true` in configuration
2. Call `masterDb.setOnlineStatus(false/true)` to control sync

### Issue: "Migration failed"
**Solution:** Check the migration logs and ensure all old data is accessible in AsyncStorage.

## 📞 Support

If you encounter issues during migration:

1. Check the migration logs for detailed error information
2. Ensure all old services are properly backed up
3. Test the new system thoroughly before removing old files
4. Keep backups until you're confident everything works

## 🎉 Congratulations!

You've successfully migrated to the Master Database System! Your app now has:

- ✅ Single unified database service
- ✅ Built-in caching and performance optimization
- ✅ Automatic offline sync
- ✅ Health monitoring and auto-recovery
- ✅ Automatic backups
- ✅ Clean, maintainable codebase

Your database architecture is now much simpler and more powerful!
