# Email-Based Authentication System

## Overview

The TailorZap authentication system has been consolidated into a single, intelligent login screen that automatically detects user roles based on email address patterns. This eliminates the need for separate login screens and role selection.

## How It Works

### Email Pattern Detection

The system automatically determines user type based on email patterns:

#### Business Owner (Admin Role)
- `<EMAIL>`
- `<EMAIL>`
- Any email containing "owner" or "business"

#### Manager/Admin (Manager Role)
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- Any email containing "admin" or "manager"
- **Requires admin access code for security**

#### Staff (User Role)
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- Any email containing "staff" or "employee"

### Demo Accounts

For testing purposes, the following demo accounts are available:

| Email | Password | Role | Admin Code (if required) |
|-------|----------|------|-------------------------|
| `<EMAIL>` | `owner123` | Business Owner | - |
| `<EMAIL>` | `admin123` | Manager | `ADMIN2024` |
| `<EMAIL>` | `staff123` | Staff | - |

## Features

### Smart Role Detection
- Automatically detects user role from email pattern
- Visual indicator shows detected role
- Dynamic UI adapts to user type (colors, icons, features)

### Security Features
- Admin accounts require additional access code
- Email validation
- Password strength requirements
- Session management with secure tokens

### User Experience
- Single login screen for all user types
- Real-time role detection as user types email
- Contextual features display based on detected role
- Demo login buttons for easy testing

## Implementation Details

### Email Validation Patterns

```typescript
// Admin patterns
if (emailLower.includes('admin') || 
    emailLower.includes('manager') || 
    emailLower.startsWith('admin@') ||
    emailLower.includes('.admin@')) {
  return 'admin';
}

// Staff patterns
if (emailLower.includes('staff') || 
    emailLower.includes('employee') ||
    emailLower.startsWith('staff@') ||
    emailLower.includes('.staff@') ||
    /staff\d+@/.test(emailLower)) {
  return 'staff';
}

// Default to business owner
return 'user';
```

### Role-Based Features

#### Business Owner
- Full Dashboard
- Financial Reports
- Staff Management
- Analytics
- System Settings

#### Manager
- Staff Management
- Advanced Reports
- User Permissions
- Audit Logs

#### Staff
- Order Management
- Customer Service
- Task Management
- Time Tracking

## Migration Benefits

1. **Simplified UX**: Single login screen instead of 4 separate screens
2. **Intelligent Detection**: No manual role selection needed
3. **Better Security**: Email-based authentication is more secure
4. **Easier Maintenance**: One screen to maintain instead of multiple
5. **Consistent Branding**: Unified visual experience

## Future Enhancements

- Integration with company email domains
- LDAP/Active Directory support
- Multi-factor authentication
- Social login options
- Biometric authentication