{"name": "tailor", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "quality:check": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run lint:fix && npm run format", "lint-staged": "lint-staged", "prepare": "husky install", "quality": "npm run type-check && npm run lint && npm run test", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build:analyze": "npx expo export --platform web && npx webpack-bundle-analyzer web-build/static/js/*.js", "bundle:analyze": "node scripts/bundle-analyzer.js", "setup-phosphor": "node scripts/setup-phosphor-icons.js", "add-icon": "node scripts/add-icon.js", "android:clean": "cd android && ./gradlew clean", "android:build": "cd android && ./gradlew assembleRelease", "android:build-debug": "cd android && ./gradlew assembleDebug", "android:install": "cd android && ./gradlew installRelease", "android:bundle": "cd android && ./gradlew bundleRelease"}, "dependencies": {"@gorhom/bottom-sheet": "^4.6.4", "@gorhom/portal": "^1.0.14", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "dayjs": "^1.11.18", "expo": "^53.0.22", "expo-camera": "^16.1.11", "expo-crypto": "^14.1.5", "expo-device": "^7.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-local-authentication": "^16.0.5", "expo-secure-store": "~14.2.4", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "phosphor-react-native": "^3.0.0", "prop-types": "15.8.1", "react": "19.0.0", "react-native": "0.79.5", "react-native-calendars": "^1.1313.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^1.3.0", "react-native-pager-view": "6.7.1", "react-native-paper": "^5.14.5", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.3"}, "devDependencies": {"@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/preset-env": "^7.28.3", "@babel/preset-typescript": "^7.27.1", "@jest/types": "^30.0.5", "@react-native-community/cli": "^20.0.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.3.3", "@tsconfig/react-native": "^3.0.6", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@types/react": "~19.0.14", "@types/react-native-calendars": "^1.1264.7", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "babel-jest": "^30.1.2", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.35.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "expo-module-scripts": "^4.1.10", "jest": "^29.7.0", "jest-expo": "^53.0.10", "jimp": "^1.6.0", "prettier": "^3.6.2", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "resolutions": {"use-latest-callback": "0.2.4"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["prop-types", "@gorhom/portal", "react-native-chart-kit"]}, "appConfigFieldsNotSyncedCheck": {"enabled": false}}}}