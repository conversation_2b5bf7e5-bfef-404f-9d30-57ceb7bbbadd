# TailorZa Master

A comprehensive React Native application for tailoring business management, built with Expo and TypeScript.

## Overview

TailorZa is a modern, feature-rich mobile application designed specifically for tailoring businesses. It provides complete business management capabilities including customer management, order tracking, inventory control, and financial reporting, all within an intuitive Material Design 3 interface.

## Features

### Core Business Features
- **Customer Management**: Add, edit, and manage customer information with comprehensive profiles
- **Order Management**: Create and track orders with detailed item lists and status tracking
- **Inventory Management**: Track products and materials with real-time stock levels and low-stock alerts
- **Financial Tracking**: Monitor revenue, expenses, and profit margins with detailed reporting
- **Measurement System**: Store and manage customer measurements with precision
- **Material Calculator**: Calculate fabric and material requirements with waste factors

### Technical Features
- **Security**: Enterprise-grade security with encryption, biometric authentication, and input validation
- **Performance Monitoring**: Real-time performance tracking and optimization
- **Offline-First**: Complete offline functionality with local SQLite database
- **Modern UI**: Material Design 3 interface with comprehensive dark/light theme support
- **Testing**: 70%+ test coverage with comprehensive unit, integration, and component tests
- **Code Quality**: Automated ESLint, TypeScript checking, and CI/CD pipeline

## Technology Stack

- **Framework**: React Native 0.79.5 with Expo 53.0.22
- **Language**: TypeScript 5.8.3
- **Database**: Expo SQLite for local data storage
- **UI Library**: React Native Paper (Material Design 3)
- **Navigation**: React Navigation v6
- **State Management**: React Context API with custom hooks
- **Testing**: Jest with React Native Testing Library
- **Security**: Expo SecureStore, Local Authentication, Custom Security Service
- **Performance**: Custom Performance Monitoring Service

## Architecture

TailorZa follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│     (Screens, Components, UI)       │
├─────────────────────────────────────┤
│          Business Logic Layer       │
│     (Hooks, Context, Services)      │
├─────────────────────────────────────┤
│           Data Access Layer         │
│    (Database, API, Storage)         │
└─────────────────────────────────────┘
```

### Key Components

- **Services**: Business logic and data operations
- **Contexts**: Global state management
- **Hooks**: Reusable business logic
- **Components**: UI components and screens
- **Utils**: Helper functions and utilities

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Expo CLI
- iOS Simulator / Android Emulator or physical device

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd "TailorZa Master"
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install additional security dependencies**
   ```bash
   npm install expo-local-authentication expo-device
   ```

4. **Start the development server**
   ```bash
   npx expo start
   ```

5. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app on physical device

### Development Setup

1. **Configure ESLint and TypeScript**
   ```bash
   npm run lint
   npm run type-check
   ```

2. **Run tests**
   ```bash
   npm test
   npm run test:coverage
   ```

3. **Set up git hooks**
   ```bash
   npx husky install
   ```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Generic UI components
│   ├── forms/           # Form-specific components
│   ├── navigation/      # Navigation components
│   └── security/        # Security-related components
├── context/             # React Context providers
│   ├── AuthContext.tsx
│   ├── DataContext.tsx
│   ├── ThemeContext.tsx
│   └── ToastContext.tsx
├── hooks/               # Custom React hooks
│   ├── useAuth.ts
│   ├── useSecurity.ts
│   └── usePerformance.ts
├── screens/             # Application screens
│   ├── auth/           # Authentication screens
│   ├── business/       # Business management screens
│   ├── customer/       # Customer management screens
│   ├── orders/         # Order management screens
│   └── settings/       # Settings and configuration
├── services/            # Business services
│   ├── MasterDatabaseService.ts
│   ├── AuthService.ts
│   ├── SecurityService.ts
│   └── PerformanceMonitoringService.ts
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
└── theme/               # Theme configuration
```

## Core Services

### MasterDatabaseService
Handles all database operations including:
- SQLite database initialization and migrations
- CRUD operations for all entities
- Data validation and integrity checks
- Backup and restore functionality

### SecurityService
Provides enterprise-grade security features:
- AES-256 data encryption/decryption
- Secure storage management
- Input validation and sanitization
- Biometric authentication
- Security audit logging
- Anomaly detection

### PerformanceMonitoringService
Monitors application performance:
- Component render time tracking
- Network request monitoring
- Memory usage analysis
- Performance metrics collection

## Security Features

TailorZa implements comprehensive security measures:

### Data Protection
- **Encryption**: All sensitive data encrypted with AES-256
- **Secure Storage**: Credentials stored in device keychain/keystore
- **Input Validation**: XSS and SQL injection prevention
- **Biometric Auth**: Fingerprint/Face ID for sensitive operations

### Security Monitoring
- **Device Fingerprinting**: Unique device identification
- **Anomaly Detection**: Suspicious activity monitoring
- **Audit Logging**: Comprehensive security event logging
- **Real-time Alerts**: Immediate notification of security events

### Usage Example
```typescript
import { useSecurity } from '@/hooks/useSecurity';

const MyComponent = () => {
  const { validateSecureInput, encryptSensitiveData } = useSecurity();
  
  const handleFormSubmit = async (data) => {
    const validation = await validateSecureInput(data.email, 'email');
    if (validation.isValid) {
      const encrypted = await encryptSensitiveData(data.password);
      // Process secure data
    }
  };
};
```

## Performance Optimization

### Automatic Monitoring
```typescript
// HOC for automatic component monitoring
export default withPerformanceTracking(MyScreen, 'MyScreen');

// Manual operation tracking
const { startTracking, endTracking } = usePerformanceTracking('operations');
```

### Optimization Techniques
- Component memoization with React.memo
- FlatList optimization for large datasets
- Image optimization and caching
- Bundle size optimization
- Memory leak prevention

## Testing

TailorZa maintains high test coverage across all layers:

### Test Types
- **Unit Tests**: Service and utility functions (90%+ coverage)
- **Component Tests**: UI component behavior (60%+ coverage)
- **Integration Tests**: Cross-service functionality
- **Hook Tests**: Custom hook behavior

### Running Tests
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test file
npm test -- CustomerService.test.ts

# Watch mode
npm test -- --watch
```

### Test Structure
```typescript
describe('CustomerService', () => {
  beforeEach(() => {
    // Test setup
  });

  describe('Customer Creation', () => {
    test('should create customer with valid data', () => {
      // Test implementation
    });
  });
});
```

## Code Quality

### ESLint Configuration
Comprehensive linting rules for:
- TypeScript best practices
- React/React Native patterns
- Import organization
- Code consistency

### CI/CD Pipeline
Automated checks for:
- Code quality (ESLint)
- Type checking (TypeScript)
- Test execution (Jest)
- Security scanning
- Build verification

### Git Hooks
Pre-commit hooks ensure:
- All tests pass
- Code passes linting
- TypeScript compiles
- Proper commit message format

## Documentation

Comprehensive documentation is available in the `docs/` directory:

- **[Architecture Guide](docs/ARCHITECTURE.md)**: Detailed architectural overview
- **[Security Guide](docs/SECURITY_GUIDE.md)**: Security implementation and best practices  
- **[Performance Guide](docs/PERFORMANCE_GUIDE.md)**: Performance monitoring and optimization
- **[Testing Guide](docs/TESTING_GUIDE.md)**: Testing strategies and implementation
- **[Code Quality Guide](docs/CODE_QUALITY.md)**: Development standards and workflows

## Development Workflow

1. **Feature Development**
   ```bash
   git checkout -b feature/new-feature
   # Implement feature
   npm test
   npm run lint
   git commit -m "feat: add new feature"
   ```

2. **Code Review**
   - All code requires peer review
   - Automated checks must pass
   - Test coverage requirements must be met

3. **Deployment**
   - Staging deployment for testing
   - Production deployment after approval

## Contributing

### Development Guidelines

1. **Code Standards**
   - Follow TypeScript strict mode
   - Use ESLint configuration
   - Write comprehensive tests
   - Document public APIs

2. **Commit Convention**
   ```
   feat: add new feature
   fix: bug fix
   docs: documentation update
   style: formatting changes
   refactor: code refactoring
   test: add tests
   chore: maintenance tasks
   ```

3. **Testing Requirements**
   - Minimum 70% overall coverage
   - 90% coverage for business logic
   - All new features must include tests

## Troubleshooting

### Common Issues

1. **Metro bundler issues**
   ```bash
   npx expo start --clear
   ```

2. **TypeScript errors**
   ```bash
   npm run type-check
   ```

3. **Test failures**
   ```bash
   npm test -- --verbose
   ```

4. **Performance issues**
   - Check performance monitoring dashboard
   - Review component render times
   - Analyze memory usage patterns

### Database Issues
```bash
# Reset database
rm -rf node_modules/.expo/
npx expo start --clear
```

### Security Issues
- Review security audit logs
- Check biometric authentication setup
- Verify encryption key generation

## Deployment

### Build Configuration

1. **Development Build**
   ```bash
   npx expo build:android --type development
   npx expo build:ios --type development
   ```

2. **Production Build**
   ```bash
   npx expo build:android --type app-bundle
   npx expo build:ios --type archive
   ```

### Environment Configuration
- Development: Local debugging enabled
- Staging: Performance monitoring enabled
- Production: Optimized and secure build

## Support

For technical support or questions:

1. Check the documentation in `docs/`
2. Review existing issues and discussions
3. Contact the development team

## License

This project is proprietary software. All rights reserved.

---

*Last updated: 2025-01-07*