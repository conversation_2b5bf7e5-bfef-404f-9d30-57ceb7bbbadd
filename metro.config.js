const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Recommended SVG transformer setup for Expo/Metro
config.transformer.babelTransformerPath = require.resolve('react-native-svg-transformer');
config.resolver.assetExts = config.resolver.assetExts.filter((ext) => ext !== 'svg');
config.resolver.sourceExts = [...config.resolver.sourceExts, 'svg'];

// Configure module resolution alias
config.resolver.alias = {
  '@': __dirname + '/src',
};

module.exports = config;
