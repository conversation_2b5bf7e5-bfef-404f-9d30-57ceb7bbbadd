#!/usr/bin/env bash
set -euo pipefail

# Build a production (release) APK for 64-bit Android devices only (arm64-v8a)
# without changing any package versions. This does NOT modify your project files.
# Usage: ./scripts/build-apk-arm64.sh

ROOT_DIR="$(cd "$(dirname "$0")/.." && pwd)"
ANDROID_DIR="$ROOT_DIR/android"

# Ensure Java 17 is used when available (macOS). Ignore if not present.
if command -v /usr/libexec/java_home >/dev/null 2>&1; then
  if /usr/libexec/java_home -v 17 >/dev/null 2>&1; then
    export JAVA_HOME="$([[ -n "${JAVA_HOME-}" ]] && echo "$JAVA_HOME" || /usr/libexec/java_home -v 17)"
  fi
fi

pushd "$ANDROID_DIR" >/dev/null

# Clean and assemble release for arm64 only. Architecture is configured in app/build.gradle.
./gradlew \
  :app:clean \
  :app:assembleRelease \
  -x lint \
  --no-daemon \
  --stacktrace

# The output APK will be architecture-specific.
APK_PATH="app/build/outputs/apk/release/app-arm64-v8a-release.apk"
FINAL_APK_NAME="TailorZa-arm64-v8a-release.apk"
DEST_PATH="$ROOT_DIR/$FINAL_APK_NAME"

if [[ -f "$APK_PATH" ]]; then
  echo "\n✅ Build complete. Moving APK to root folder..."
  mv "$APK_PATH" "$DEST_PATH"
  echo "✅ APK moved to: $DEST_PATH"
  ls -lah "$DEST_PATH"
else
  echo "\n❌ Build did not produce an APK at $APK_PATH" >&2
  exit 1
fi

popd >/dev/null
