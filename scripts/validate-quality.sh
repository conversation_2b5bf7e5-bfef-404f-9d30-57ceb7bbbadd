#!/bin/bash

# Code Quality Validation Script
# Validates that all code quality tools are properly configured and working

echo "🔍 TailorZa Master - Code Quality Validation"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check command result
check_result() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1 - PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 - FAILED${NC}"
        return 1
    fi
}

# Function to check if file exists
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 missing${NC}"
        return 1
    fi
}

echo ""
echo "📁 Checking configuration files..."
check_file ".eslintrc.json"
check_file ".lintstagedrc.json"
check_file ".commitlintrc.json"
check_file "jest.config.js"
check_file ".github/workflows/ci.yml"

echo ""
echo "🔧 Checking installed tools..."

# Check if required dependencies are installed
echo "📦 Checking ESLint..."
npx eslint --version > /dev/null 2>&1
check_result "ESLint installation"

echo "📦 Checking TypeScript..."
npx tsc --version > /dev/null 2>&1
check_result "TypeScript installation"

echo "📦 Checking Jest..."
npx jest --version > /dev/null 2>&1
check_result "Jest installation"

echo "📦 Checking Prettier..."
npx prettier --version > /dev/null 2>&1
check_result "Prettier installation"

echo ""
echo "🧪 Running quality checks..."

# TypeScript type checking
echo "🔍 Running TypeScript type check..."
npm run type-check > /dev/null 2>&1
check_result "TypeScript compilation"

# ESLint check (ignore exit code for now)
echo "🔍 Running ESLint check..."
npm run lint > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ ESLint - No errors found${NC}"
else
    echo -e "${YELLOW}⚠️ ESLint - Some issues found (run 'npm run lint' for details)${NC}"
fi

# Test execution
echo "🧪 Running test suite..."
npm run test > /dev/null 2>&1
check_result "Jest test execution"

echo ""
echo "📊 Generating coverage report..."
npm run test:coverage > /dev/null 2>&1
check_result "Test coverage generation"

echo ""
echo "🎯 Quality Summary:"
echo "==================="

# Count test files
test_files=$(find src -name "*.test.ts" -o -name "*.test.tsx" | wc -l)
echo "📝 Test Files: $test_files"

# Check if coverage directory exists
if [ -d "coverage" ]; then
    echo -e "${GREEN}📊 Coverage Report: Generated in coverage/ directory${NC}"
else
    echo -e "${YELLOW}📊 Coverage Report: Not generated${NC}"
fi

# Security check
echo ""
echo "🔒 Security audit..."
npm audit --audit-level=moderate > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Security Audit - No vulnerabilities found${NC}"
else
    echo -e "${YELLOW}⚠️ Security Audit - Some vulnerabilities found (run 'npm audit' for details)${NC}"
fi

echo ""
echo "🎉 Code Quality Validation Complete!"
echo ""
echo "💡 Next steps:"
echo "   - Run 'npm run quality' for full quality check"
echo "   - Run 'npm run quality:fix' to auto-fix issues"
echo "   - Check docs/CODE_QUALITY.md for detailed guidelines"
echo ""