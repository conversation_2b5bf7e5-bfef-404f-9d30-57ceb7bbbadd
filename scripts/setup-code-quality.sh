#!/bin/bash

# Code Quality Automation Setup Script
# This script sets up automated code quality checks for the TailorZa Master project

echo "🚀 Setting up Code Quality Automation for TailorZa Master..."

# 1. Install additional development dependencies
echo "📦 Installing code quality tools..."
npm install --save-dev \
  @typescript-eslint/eslint-plugin@^8.0.0 \
  @typescript-eslint/parser@^8.0.0 \
  eslint-plugin-react@^7.37.0 \
  eslint-plugin-react-hooks@^5.0.0 \
  eslint-plugin-react-native@^4.1.0 \
  eslint-plugin-import@^2.31.0 \
  husky@^9.0.0 \
  lint-staged@^15.0.0 \
  commitizen@^4.3.0 \
  @commitlint/cli@^19.0.0 \
  @commitlint/config-conventional@^19.0.0

# 2. Initialize Husky for Git hooks
echo "🪝 Setting up Git hooks..."
npx husky install
npm pkg set scripts.prepare="husky install"

# 3. Create Git hooks
npx husky add .husky/pre-commit "npm run lint-staged"
npx husky add .husky/commit-msg "npx --no-install commitlint --edit \$1"

echo "✅ Code Quality Automation setup complete!"
echo ""
echo "🎯 Available commands:"
echo "  npm run lint        - Run ESLint"
echo "  npm run lint:fix    - Fix auto-fixable ESLint issues"
echo "  npm run type-check  - Run TypeScript compiler checks"
echo "  npm run test        - Run Jest tests"
echo "  npm run format      - Format code with Prettier"
echo "  npm run quality     - Run all quality checks"
echo ""
echo "🔧 Git hooks enabled:"
echo "  pre-commit  - Runs linting and type checking on staged files"
echo "  commit-msg  - Validates commit message format"
echo ""