import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer, createNavigationContainerRef } from '@react-navigation/native';
import { PaperProvider, Portal } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { createPaperTheme } from './src/theme/paperThemeAdapter';
import RootNavigator from './src/navigation/RootNavigator';
import { DataProvider } from './src/context/DataContext';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { FinancialProvider } from './src/context/FinancialContext';
import { AuthProvider } from './src/context/AuthContext';
import { ToastProvider, useToast } from './src/context/ToastContext';
import ErrorBoundary from './src/components/utils/ErrorBoundary';
import { useAppInitializer } from './src/hooks/useAppInitializer';
import ToastService, { toastRef, ToastActions } from './src/services/ToastService';

const navigationRef = createNavigationContainerRef();

const App: React.FC = () => {
  useAppInitializer();

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ThemeProvider>
            <AuthProvider>
              <DataProvider>
                <FinancialProvider>
                  <BottomSheetModalProvider>
                    <AppContent />
                  </BottomSheetModalProvider>
                </FinancialProvider>
              </DataProvider>
            </AuthProvider>
          </ThemeProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
};

const AppContent: React.FC = () => {
  const { isDarkMode, isLoading } = useTheme();

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  const paperTheme = createPaperTheme(isDarkMode);

  return (
    <PaperProvider theme={paperTheme}>
      {/* FIX: ToastProvider is moved here, inside PaperProvider */}
      <ToastProvider>
        <ToastWrapper>
          <NavigationContainer ref={navigationRef}>
            <RootNavigator />
            <StatusBar style={isDarkMode ? 'light' : 'dark'} />
          </NavigationContainer>
        </ToastWrapper>
      </ToastProvider>
    </PaperProvider>
  );
};

const ToastWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const toast = useToast();
  React.useEffect(() => {
    (toastRef as React.MutableRefObject<ToastActions | null>).current = toast;
  }, [toast]);

  return <>{children}</>;
}


export default App;