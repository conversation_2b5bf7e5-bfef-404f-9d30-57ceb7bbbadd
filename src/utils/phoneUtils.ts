/**
 * Phone Number Utilities
 * Handles phone number formatting, cleaning, and validation
 */

/**
 * Cleans a phone number by removing all non-digit characters
 * @param phoneNumber - The phone number to clean
 * @returns Cleaned phone number with only digits
 */
export const cleanPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) {return '';}
  return phoneNumber.replace(/[^0-9]/g, '');
};

/**
 * Formats a phone number for display (adds dashes and plus sign)
 * @param phoneNumber - The clean phone number (digits only)
 * @returns Formatted phone number for display
 */
export const formatPhoneNumberForDisplay = (phoneNumber: string): string => {
  if (!phoneNumber) {return '';}

  const cleaned = cleanPhoneNumber(phoneNumber);

  // Format based on length
  if (cleaned.length === 11) {
    // Format: +880-1XXX-XXX-XXX
    return `+${cleaned.substring(0, 3)}-${cleaned.substring(3, 7)}-${cleaned.substring(7, 10)}-${cleaned.substring(10)}`;
  } else if (cleaned.length === 10) {
    // Format: +880-1XXX-XXX-XXX (assuming Bangladesh number)
    return `+880-${cleaned.substring(0, 1)}-${cleaned.substring(1, 5)}-${cleaned.substring(5, 8)}-${cleaned.substring(8)}`;
  } else if (cleaned.length === 13) {
    // Format: +880-1XXX-XXX-XXX (already has country code)
    return `+${cleaned.substring(0, 3)}-${cleaned.substring(3, 7)}-${cleaned.substring(7, 10)}-${cleaned.substring(10)}`;
  }

  // Default formatting for other lengths
  return cleaned;
};

/**
 * Validates if a phone number is valid (8-15 digits after cleaning)
 * @param phoneNumber - The phone number to validate
 * @returns True if valid, false otherwise
 */
export const isValidPhoneNumber = (phoneNumber: string): boolean => {
  const cleaned = cleanPhoneNumber(phoneNumber);
  return cleaned.length >= 8 && cleaned.length <= 11;
};

/**
 * Prepares phone number for storage (cleans and validates)
 * @param phoneNumber - The phone number to prepare
 * @returns Cleaned phone number ready for storage
 */
export const preparePhoneNumberForStorage = (phoneNumber: string): string => {
  return cleanPhoneNumber(phoneNumber);
};
