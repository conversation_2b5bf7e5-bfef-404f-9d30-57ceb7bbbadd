import { FINANCIAL_CONFIG } from '@/config/constants';

interface FormatCurrencyOptions {
  decimals?: number;
  fallback?: string;
}

/**
 * Format a numeric amount as a currency string using global FINANCIAL_CONFIG.
 * Ensures the ৳ symbol is used consistently across the app.
 */
export function formatCurrency(
  amount: number | null | undefined,
  options: FormatCurrencyOptions = {}
): string {
  const symbol = FINANCIAL_CONFIG.CURRENCY.SYMBOL;
  const decimals =
    typeof options.decimals === 'number'
      ? options.decimals
      : FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES;

  if (amount === null || amount === undefined || isNaN(Number(amount))) {
    return options.fallback ?? `${symbol}0`;
  }

  const isNegative = amount < 0;
  const absoluteAmount = Math.abs(amount);

  // Use toLocaleString for thousands separators without relying on Intl.NumberFormat configuration
  const fixed = absoluteAmount.toFixed(decimals);
  const [intPart, fracPart] = fixed.split('.');
  const withSeparators = Number(intPart).toLocaleString();

  const formattedAmount = decimals > 0 ? `${withSeparators}.${fracPart}` : withSeparators;
  
  // Handle negative formatting: -৳100.50 instead of ৳-100.50
  return isNegative ? `-${symbol}${formattedAmount}` : `${symbol}${formattedAmount}`;
}

/**
 * Parse a currency string back to a number. Strips symbols and separators.
 */
export function parseCurrency(input: string | number): number {
  if (typeof input === 'number') {return input;}
  if (!input) {return 0;}

  // Remove currency symbol and whitespace without using \s in a character class
  let cleaned = input.split(FINANCIAL_CONFIG.CURRENCY.SYMBOL).join('');
  cleaned = cleaned.replace(/\s/g, '');

  // Normalize commas used as thousands separators, keep last decimal point
  // Replace commas with nothing, keep dot as decimal
  cleaned = cleaned.replace(/,/g, '');

  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Format a plain number with grouping (no currency symbol).
 */
export function formatNumber(amount: number | null | undefined, decimals = 0): string {
  if (amount === null || amount === undefined || isNaN(Number(amount))) {return '0';}
  const fixed = Number(amount).toFixed(decimals);
  const [intPart, fracPart] = fixed.split('.');
  const withSeparators = Number(intPart).toLocaleString();
  return decimals > 0 ? `${withSeparators}.${fracPart}` : withSeparators;
}
