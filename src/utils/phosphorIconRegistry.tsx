/**
 * Phosphor Icons Registry for TailorZap
 *
 * This file contains Phosphor Icons integration for the TailorZap app.
 * Icons are imported from phosphor-react-native for React Native compatibility.
 */

import {
  House,
  HouseSimple,
  Gear,
  GearSix,
  User,
  UserCircle,
  UserPlus,
  UserPlus as AccountPlus,
  MagnifyingGlass,
  Plus,
  Minus,
  X,
  Check,
  Storefront,
  ShoppingCart,
  ShoppingBag,
  CreditCard,
  Money,
  Coins,
  Bank,
  Receipt,
  Calculator,
  Scissors,
  Ruler,
  TShirt,
  Dress,
  Pants,
  ChartLine,
  ChartBar,
  ChartPie,
  TrendUp,
  TrendDown,
  Database,
  Cloud,
  Phone,
  Envelope,
  ChatCircle,
  Headset,
  Microphone,
  VideoCamera,
  CheckCircle,
  Warning,
  Info,
  Question,
  Clock,
  Calendar,
  Timer,
  Bell,
  BellRinging,
  Moon,
  Sun,
  Lightbulb,
  Lock,
  Shield,
  Key,
  File,
  Folder,
  Image,
  Camera,
  Upload,
  Download,
  Users,
  Package,
  Truck,
  CaretRight,
  CaretDown,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Star,
  Heart,
  Eye,
  EyeSlash,
  Trash,
  <PERSON>cilSimple,
  <PERSON>loppyDisk,
  XCircle,
  Funnel,
  ArrowsDownUp,
  Share,
  Printer,
  ArrowUpRight,
  ArrowDownLeft,
  ArrowsClockwise,
  List,
  MapPin,
  MapTrifold,
  Globe,
  Flag,
  Bookmark,
  BookmarkSimple,
  // Additional icons for missing names
  DotsThreeVertical,
  SignOut,
  ShieldWarning,
  UserGear,
  ShieldCheck,
  IdentificationCard,
  MinusCircle,
  PlusCircle,
  Clipboard,
  ClipboardText,
  // ClipboardPlus, // Not available in this version
  // ClipboardTextMultiple, // Not available in this version
  Lightning,
  CashRegister,
  FileText,
  Trophy,
  Pencil,
  Square,
  Triangle,
  Diamond,
  Hexagon,
  Octagon,
  ThumbsUp,
  ThumbsDown,
  Smiley,
  SmileySad,
  SmileyMeh,
  SmileyWink,
  SmileySticker,
  SmileyBlank,
  SmileyNervous,
  SmileyXEyes,
  SmileyAngry,
  // Additional icons for staff management
  User as AccountGroup,
  UserCircle as AccountCheck,
  Clock as AccountClock,
  Scissors as ScissorsCutting,
  User as AccountTie,
  Palette,
  CheckCircle as CheckDecagram,
  Wrench as AutoFix,
  Code as CodeJson,
  FileText as FileDelimited,
  MicrosoftExcelLogo as FileExcel,
  Files,
  ArrowsLeftRight,
  Warehouse,
  // Missing icons that need to be added
  Wrench,
  WifiSlash,
  Crown,
  Wallet,
  Gift,
  ArrowDown as ArrowDownBold,
  ArrowUp as ArrowUpBold,
  ArrowsLeftRight as SwapHorizontal,
  GridFour,
  QrCode,
  CameraSlash,
  // Payment method icons
  Bank as BankTransfer,
} from 'phosphor-react-native';
import * as React from 'react';

import LoggingService from '../services/LoggingService';

// Type-safe icon names
export type PhosphorIconName =
  | 'house'
  | 'house-simple'
  | 'gear'
  | 'gear-six'
  | 'user'
  | 'user-circle'
  | 'user-plus'
  | 'account-plus'
  | 'magnifying-glass'
  | 'plus'
  | 'minus'
  | 'x'
  | 'check'
  | 'storefront'
  | 'shopping-cart'
  | 'shopping-bag'
  | 'credit-card'
  | 'money'
  | 'coins'
  | 'bank'
  | 'receipt'
  | 'calculator'
  | 'scissors'
  | 'ruler'
  | 'tshirt'
  | 'dress'
  | 'pants'
  | 'chart-line'
  | 'chart-bar'
  | 'chart-pie'
  | 'trend-up'
  | 'trend-down'
  | 'database'
  | 'cloud'
  | 'phone'
  | 'envelope'
  | 'chat-circle'
  | 'headset'
  | 'microphone'
  | 'video-camera'
  | 'check-circle'
  | 'warning'
  | 'info'
  | 'question'
  | 'clock'
  | 'calendar'
  | 'timer'
  | 'bell'
  | 'bell-ringing'
  | 'moon'
  | 'sun'
  | 'lightbulb'
  | 'lock'
  | 'shield'
  | 'key'
  | 'file'
  | 'folder'
  | 'image'
  | 'camera'
  | 'upload'
  | 'download'
  | 'users'
  | 'package'
  | 'truck'
  | 'chevron-right'
  | 'chevron-down'
  | 'arrow-right'
  | 'arrow-left'
  | 'arrow-up'
  | 'arrow-down'
  | 'star'
  | 'heart'
  | 'eye'
  | 'eye-slash'
  | 'trash'
  | 'edit'
  | 'save'
  | 'cancel'
  | 'search'
  | 'filter'
  | 'sort'
  | 'share'
  | 'print'
  | 'export'
  | 'import'
  | 'refresh'
  | 'settings'
  | 'home'
  | 'menu'
  | 'close'
  | 'back'
  | 'forward'
  | 'next'
  | 'previous'
  | 'location'
  | 'map'
  | 'globe'
  | 'world'
  | 'flag'
  | 'bookmark'
  | 'bookmark-simple'
  | 'notification'
  | 'alert'
  | 'alert-circle'
  | 'checkmark'
  | 'checkmark-circle'
  | 'x-circle'
  | 'warning-circle'
  | 'info-circle'
  | 'question-circle'
  | 'exclamation-circle'
  | 'minus-circle'
  | 'plus-circle'
  | 'clipboard'
  | 'clipboard-text'
  | 'clipboard-plus'
  | 'clipboard-text-multiple'
  | 'lightning'
  | 'cash-register'
  | 'file-text'
  | 'trophy'
  | 'map-pin'
  | 'pencil'
  | 'square'
  | 'triangle'
  | 'diamond'
  | 'hexagon'
  | 'octagon'
  | 'thumbs-up'
  | 'thumbs-down'
  | 'smiley'
  | 'smiley-sad'
  | 'smiley-meh'
  | 'smiley-wink'
  | 'smiley-sticker'
  | 'smiley-blank'
  | 'smiley-nervous'
  | 'smiley-x-eyes'
  | 'smiley-angry'
  // Staff management icons
  | 'account-group'
  | 'account-check'
  | 'account-clock'
  | 'scissors-cutting'
  | 'account-tie'
  | 'palette'
  | 'check-decagram'
  // Data backup icons
  | 'auto-fix'
  | 'code-json'
  | 'file-delimited'
  | 'file-excel'
  | 'files'
  // Additional icons for missing names
  | 'dots-three-vertical'
  | 'sign-out'
  | 'shield-warning'
  | 'user-gear'
  | 'shield-check'
  | 'identification-card'
  // New icons for inventory
  | 'arrows-left-right'
  | 'warehouse'
  | 'caret-down'
  | 'caret-right'
  // Missing icons that were causing warnings
  | 'wrench'
  | 'wifi-off'
  | 'crown'
  | 'cash'
  | 'wallet'
  | 'bank-transfer'
  | 'gift'
  | 'arrow-down-bold'
  | 'arrow-up-bold'
  | 'swap-horizontal'
  | 'grid-four'
  | 'qr-code'
  | 'camera-slash'
  // Additional aliases for common icon names
  | 't-shirt'
  | 'circle'
  | 'list'
  | 'file-pdf'
  | 'microsoft-excel-logo';

// Icon mapping
const PHOSPHOR_ICONS: Record<PhosphorIconName, React.ComponentType<any>> = {
  house: House,
  'house-simple': HouseSimple,
  gear: Gear,
  'gear-six': GearSix,
  user: User,
  'user-circle': UserCircle,
  'user-plus': UserPlus,
  'account-plus': AccountPlus,
  'magnifying-glass': MagnifyingGlass,
  plus: Plus,
  minus: Minus,
  x: X,
  check: Check,
  storefront: Storefront,
  'shopping-cart': ShoppingCart,
  'shopping-bag': ShoppingBag,
  'credit-card': CreditCard,
  money: Money,
  coins: Coins,
  bank: Bank,
  receipt: Receipt,
  calculator: Calculator,
  scissors: Scissors,
  ruler: Ruler,
  tshirt: TShirt,
  dress: Dress,
  pants: Pants,
  'chart-line': ChartLine,
  'chart-bar': ChartBar,
  'chart-pie': ChartPie,
  'trend-up': TrendUp,
  'trend-down': TrendDown,
  database: Database,
  cloud: Cloud,
  phone: Phone,
  envelope: Envelope,
  'chat-circle': ChatCircle,
  headset: Headset,
  microphone: Microphone,
  'video-camera': VideoCamera,
  'check-circle': CheckCircle,
  warning: Warning,
  info: Info,
  question: Question,
  clock: Clock,
  calendar: Calendar,
  timer: Timer,
  bell: Bell,
  'bell-ringing': BellRinging,
  moon: Moon,
  sun: Sun,
  lightbulb: Lightbulb,
  lock: Lock,
  shield: Shield,
  key: Key,
  file: File,
  folder: Folder,
  image: Image,
  camera: Camera,
  upload: Upload,
  download: Download,
  users: Users,
  package: Package,
  truck: Truck,
  'chevron-right': CaretRight,
  'chevron-down': CaretDown,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  star: Star,
  heart: Heart,
  eye: Eye,
  'eye-slash': EyeSlash,
  trash: Trash,
  edit: PencilSimple,
  save: FloppyDisk,
  cancel: XCircle,
  search: MagnifyingGlass,
  filter: Funnel,
  sort: ArrowsDownUp,
  share: Share,
  print: Printer,
  export: ArrowUpRight,
  import: ArrowDownLeft,
  refresh: ArrowsClockwise,
  settings: Gear,
  home: House,
  menu: List,
  close: X,
  back: ArrowLeft,
  forward: ArrowRight,
  next: CaretRight,
  previous: CaretRight,
  location: MapPin,
  map: MapTrifold,
  globe: Globe,
  world: Globe,
  flag: Flag,
  bookmark: Bookmark,
  'bookmark-simple': BookmarkSimple,
  notification: Bell,
  alert: Warning,
  'alert-circle': Warning,
  checkmark: Check,
  'checkmark-circle': CheckCircle,
  'x-circle': XCircle,
  'warning-circle': Warning,
  'info-circle': Info,
  'question-circle': Question,
  'exclamation-circle': Warning,
  'minus-circle': MinusCircle,
  'plus-circle': PlusCircle,
  clipboard: Clipboard,
  'clipboard-text': ClipboardText,
  'clipboard-plus': Clipboard,
  'clipboard-text-multiple': ClipboardText,
  lightning: Lightning,
  'cash-register': CashRegister,
  'file-text': FileText,
  trophy: Trophy,
  'map-pin': MapPin,
  pencil: Pencil,
  square: Square,
  triangle: Triangle,
  diamond: Diamond,
  hexagon: Hexagon,
  octagon: Octagon,
  'thumbs-up': ThumbsUp,
  'thumbs-down': ThumbsDown,
  smiley: Smiley,
  'smiley-sad': SmileySad,
  'smiley-meh': SmileyMeh,
  'smiley-wink': SmileyWink,
  'smiley-sticker': SmileySticker,
  'smiley-blank': SmileyBlank,
  'smiley-nervous': SmileyNervous,
  'smiley-x-eyes': SmileyXEyes,
  'smiley-angry': SmileyAngry,
  // Staff management icons
  'account-group': AccountGroup,
  'account-check': AccountCheck,
  'account-clock': AccountClock,
  'scissors-cutting': ScissorsCutting,
  'account-tie': AccountTie,
  palette: Palette,
  'check-decagram': CheckDecagram,
  // Data backup icons
  'auto-fix': AutoFix,
  'code-json': CodeJson,
  'file-delimited': FileDelimited,
  'file-excel': FileExcel,
  files: Files,
  // Additional icons for missing names
  'dots-three-vertical': DotsThreeVertical,
  'sign-out': SignOut,
  'shield-warning': ShieldWarning,
  'user-gear': UserGear,
  'shield-check': ShieldCheck,
  'identification-card': IdentificationCard,
  // New icons for inventory
  'arrows-left-right': ArrowsLeftRight,
  warehouse: Warehouse,
  'caret-down': CaretDown,
  'caret-right': CaretRight,
  // Missing icons that were causing warnings
  wrench: Wrench,
  'wifi-off': WifiSlash,
  crown: Crown,
  cash: Money,
  wallet: Wallet,
  'bank-transfer': BankTransfer,
  gift: Gift,
  'arrow-down-bold': ArrowDownBold,
  'arrow-up-bold': ArrowUpBold,
  'swap-horizontal': SwapHorizontal,
  'grid-four': GridFour,
  'qr-code': QrCode,
  'camera-slash': CameraSlash,
  't-shirt': TShirt,
  circle: XCircle,
  list: List,
  'file-pdf': File,
  'microsoft-excel-logo': File,
};

// Phosphor Icon Component
interface PhosphorIconProps {
  name: PhosphorIconName;
  size?: number;
  color?: string;
  weight?: 'thin' | 'light' | 'regular' | 'bold' | 'fill';
  style?: any;
}

export const PhosphorIcon: React.FC<PhosphorIconProps> = ({
  name,
  size = 24,
  color = '#000000',
  weight = 'regular',
  style,
}) => {
  const IconComponent = PHOSPHOR_ICONS[name];

  if (IconComponent) {
    return <IconComponent size={size} color={color} weight={weight} style={style} />;
  }

  // Log warning for missing icons
  LoggingService.warn(`Phosphor Icon "${name}" not found`, 'PHOSPHOR_ICON_REGISTRY');

  // Return a fallback icon (X icon)
  return <X size={size} color={color} weight={weight} style={style} />;
};

export const hasPhosphorIcon = (name: string): name is PhosphorIconName => {
  return name in PHOSPHOR_ICONS;
};

export const getAvailablePhosphorIcons = (): PhosphorIconName[] => {
  return Object.keys(PHOSPHOR_ICONS) as PhosphorIconName[];
};

export default PhosphorIcon;
