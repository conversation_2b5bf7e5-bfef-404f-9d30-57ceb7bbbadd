import { Alert } from 'react-native';

import { ERROR_MESSAGES } from '../config/constants';
import LoggingService from '../services/LoggingService';

/**
 * Error types for better categorization
 */
export const ErrorTypes = {
  NETWORK: 'NETWORK',
  VALIDATION: 'VALIDATION',
  STORAGE: 'STORAGE',
  PERMISSION: 'PERMISSION',
  BUSINESS_LOGIC: 'BUSINESS_LOGIC',
  UNKNOWN: 'UNKNOWN',
} as const;

export type ErrorType = (typeof ErrorTypes)[keyof typeof ErrorTypes];

/**
 * Logger utility for development and production
 */
export const handleError = (error: unknown, context: string, data?: unknown): void => {
  const logMessage = `[${context}] Error occurred`;

  if (error instanceof Error) {
    LoggingService.error(logMessage, 'ERROR', { error, data });
  } else {
    LoggingService.error(logMessage, 'ERROR', { error, data });
  }
};

export const handleWarning = (message: string, context: string, data?: unknown): void => {
  const logMessage = `[${context}] Warning: ${message}`;
  LoggingService.warn(logMessage, 'WARNING', data);
};

export const handleInfo = (message: string, context: string, data?: unknown): void => {
  const logMessage = `[${context}] Info: ${message}`;
  LoggingService.info(logMessage, 'INFO', data);
};

export const handleDebug = (message: string, context: string, data?: unknown): void => {
  const logMessage = `[${context}] Debug: ${message}`;
  LoggingService.debug(logMessage, 'DEBUG', data);
};

/**
 * Custom Error classes for better error handling
 */
export class AppError extends Error {
  public type: ErrorType;
  public originalError: Error | null;
  public timestamp: string;

  constructor(
    message: string,
    type: ErrorType = ErrorTypes.UNKNOWN,
    originalError: Error | null = null
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();
  }
}

export class NetworkError extends AppError {
  constructor(message: string, originalError: Error | null = null) {
    super(message, ErrorTypes.NETWORK, originalError);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends AppError {
  public field: string | null;

  constructor(message: string, field: string | null = null, originalError: Error | null = null) {
    super(message, ErrorTypes.VALIDATION, originalError);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class StorageError extends AppError {
  constructor(message: string, originalError: Error | null = null) {
    super(message, ErrorTypes.STORAGE, originalError);
    this.name = 'StorageError';
  }
}

/**
 * Global error handler
 */
export class ErrorHandler {
  static handle(error: Error | AppError, context: string = 'Unknown'): string {
    handleError(error, context);

    // Determine user-friendly message
    let userMessage: string = ERROR_MESSAGES.GENERIC_ERROR;

    if (error instanceof AppError) {
      switch (error.type) {
        case ErrorTypes.NETWORK:
          userMessage = ERROR_MESSAGES.NETWORK_ERROR;
          break;
        case ErrorTypes.VALIDATION:
          userMessage = ERROR_MESSAGES.VALIDATION_ERROR;
          break;
        case ErrorTypes.STORAGE:
          userMessage = ERROR_MESSAGES.SAVE_ERROR;
          break;
        case ErrorTypes.PERMISSION:
          userMessage = ERROR_MESSAGES.PERMISSION_ERROR;
          break;
        default:
          userMessage = error.message || ERROR_MESSAGES.GENERIC_ERROR;
      }
    }

    return userMessage;
  }

  static showAlert(
    error: Error | AppError,
    context: string = 'Error',
    onPress: (() => void) | null = null
  ): void {
    const message = this.handle(error, context);

    Alert.alert('Error', message, [
      {
        text: 'OK',
        onPress: onPress || (() => {}),
      },
    ]);
  }

  static async handleAsync<T>(
    asyncFunction: () => Promise<T>,
    context: string = 'Async Operation'
  ): Promise<T> {
    try {
      return await asyncFunction();
    } catch (error) {
      this.handle(error as Error, context);
      throw error;
    }
  }
}

export interface UseErrorHandlerReturn {
  handleError: (error: Error | AppError, context?: string) => string;
  showErrorAlert: (
    error: Error | AppError,
    context?: string,
    onPress?: (() => void) | null
  ) => void;
  handleAsyncError: <T>(asyncFunction: () => Promise<T>, context?: string) => Promise<T>;
}

/**
 * Error boundary hook for React components
 */
export const useErrorHandler = (): UseErrorHandlerReturn => {
  const handleError = (error: Error | AppError, context: string = 'Component'): string => {
    return ErrorHandler.handle(error, context);
  };

  const showErrorAlert = (
    error: Error | AppError,
    context: string = 'Error',
    onPress: (() => void) | null = null
  ): void => {
    ErrorHandler.showAlert(error, context, onPress);
  };

  const handleAsyncError = async <T>(
    asyncFunction: () => Promise<T>,
    context: string = 'Async Operation'
  ): Promise<T> => {
    return ErrorHandler.handleAsync(asyncFunction, context);
  };

  return {
    handleError,
    showErrorAlert,
    handleAsyncError,
  };
};

interface NetworkErrorResponse {
  response?: {
    status: number;
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Network error handler
 */
export const handleNetworkError = (error: NetworkErrorResponse): never => {
  if (!error.response) {
    // Network error
    throw new NetworkError(ERROR_MESSAGES.NETWORK_ERROR, error as Error);
  } else if (error.response.status >= 400 && error.response.status < 500) {
    // Client error
    throw new AppError(
      error.response.data?.message || 'Client error occurred',
      ErrorTypes.VALIDATION,
      error as Error
    );
  } else if (error.response.status >= 500) {
    // Server error
    throw new AppError(
      'Server error occurred. Please try again later.',
      ErrorTypes.NETWORK,
      error as Error
    );
  }

  throw new AppError(
    error.message || ERROR_MESSAGES.GENERIC_ERROR,
    ErrorTypes.UNKNOWN,
    error as Error
  );
};
