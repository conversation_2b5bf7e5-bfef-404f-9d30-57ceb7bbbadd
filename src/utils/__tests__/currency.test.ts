import { formatCurrency } from '../currency';

describe('Currency Utils', () => {
  it('should format positive numbers correctly', () => {
    expect(formatCurrency(1234.56)).toBe('৳1,234.56');
    expect(formatCurrency(0)).toBe('৳0.00');
    expect(formatCurrency(100)).toBe('৳100.00');
  });

  it('should handle negative numbers', () => {
    expect(formatCurrency(-100.50)).toBe('-৳100.50');
  });

  it('should handle large numbers', () => {
    expect(formatCurrency(1000000)).toBe('৳1,000,000.00');
  });

  it('should handle decimal precision', () => {
    expect(formatCurrency(100.123)).toBe('৳100.12'); // Should round to 2 decimals
    expect(formatCurrency(100.999)).toBe('৳101.00'); // Should round up
  });
});