import UnitConverter from '../UnitConverter';

describe('UnitConverter', () => {
  describe('basic conversions', () => {
    it('should convert between same units', () => {
      expect(UnitConverter.convertBetweenUnits(5, 'meters', 'meters')).toBe(5);
      expect(UnitConverter.convertBetweenUnits(10, 'pieces', 'pieces')).toBe(10);
    });

    it('should convert meters to yards', () => {
      const result = UnitConverter.convertBetweenUnits(1, 'meters', 'yards');
      expect(result).toBeCloseTo(1.094, 2); // 1 meter ≈ 1.094 yards
    });

    it('should convert yards to meters', () => {
      const result = UnitConverter.convertBetweenUnits(1, 'yards', 'meters');
      expect(result).toBeCloseTo(0.914, 2); // 1 yard ≈ 0.914 meters
    });
  });

  describe('utility methods', () => {
    it('should get unit display name', () => {
      expect(UnitConverter.getUnitDisplayName('meters')).toBe('Meters');
      expect(UnitConverter.getUnitDisplayName('yards')).toBe('Yards');
      expect(UnitConverter.getUnitDisplayName('pieces')).toBe('Pieces');
    });

    it('should get unit abbreviation', () => {
      expect(UnitConverter.getUnitAbbreviation('meters')).toBe('m');
      expect(UnitConverter.getUnitAbbreviation('yards')).toBe('yd');
      expect(UnitConverter.getUnitAbbreviation('pieces')).toBe('pcs');
    });

    it('should format quantity with units', () => {
      expect(UnitConverter.formatQuantity(5.5, 'meters')).toBe('5.50 m');
      expect(UnitConverter.formatQuantity(100, 'pieces')).toBe('100.00 pcs');
    });
  });

  describe('error handling', () => {
    it('should handle invalid units gracefully', () => {
      expect(() => UnitConverter.convertBetweenUnits(5, 'invalid', 'meters')).not.toThrow();
      expect(() => UnitConverter.convertBetweenUnits(5, 'meters', 'invalid')).not.toThrow();
    });

    it('should handle zero and negative values', () => {
      expect(UnitConverter.convertBetweenUnits(0, 'meters', 'yards')).toBe(0);
      expect(UnitConverter.convertBetweenUnits(-5, 'meters', 'yards')).toBeCloseTo(-5.47, 2);
    });
  });
});