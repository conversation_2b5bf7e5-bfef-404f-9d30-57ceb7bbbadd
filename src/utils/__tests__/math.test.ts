import { add, multiply, formatNumber } from '../math';

describe('Math Utils', () => {
  describe('add function', () => {
    it('should add two positive numbers', () => {
      expect(add(2, 3)).toBe(5);
    });

    it('should add negative numbers', () => {
      expect(add(-2, -3)).toBe(-5);
    });

    it('should handle zero', () => {
      expect(add(0, 5)).toBe(5);
      expect(add(5, 0)).toBe(5);
    });
  });

  describe('multiply function', () => {
    it('should multiply two positive numbers', () => {
      expect(multiply(3, 4)).toBe(12);
    });

    it('should handle zero multiplication', () => {
      expect(multiply(5, 0)).toBe(0);
    });

    it('should handle negative numbers', () => {
      expect(multiply(-2, 3)).toBe(-6);
    });
  });

  describe('formatNumber function', () => {
    it('should format numbers with default decimals', () => {
      expect(formatNumber(3.14159)).toBe('3.14');
    });

    it('should format numbers with custom decimals', () => {
      expect(formatNumber(3.14159, 3)).toBe('3.142');
    });

    it('should handle integers', () => {
      expect(formatNumber(42)).toBe('42.00');
    });
  });
});