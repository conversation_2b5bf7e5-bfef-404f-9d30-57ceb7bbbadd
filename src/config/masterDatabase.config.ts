// --- UTILITY TYPES ---
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends (infer U)[]
    ? DeepPartial<U>[]
    : T[P] extends object
    ? DeepPartial<T[P]>
    : T[P];
};

// --- READABILITY CONSTANTS ---
const SECOND = 1000;
const MINUTE = 60 * SECOND;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;

// --- FIX 1: Provide the full, complete interface definition ---
export interface MasterDatabaseConfig {
  database: { name: string; location: string; version: number; enableWAL: boolean; enableForeignKeys: boolean; busyTimeout: number; };
  cache: { enabled: boolean; maxSize: number; defaultTTL: number; persistToDisk: boolean; cleanupInterval: number; };
  sync: { enabled: boolean; syncInterval: number; maxRetries: number; retryDelay: number; batchSize: number; conflictResolution: 'client' | 'server' | 'manual'; };
  monitoring: { enabled: boolean; healthCheckInterval: number; performanceTracking: boolean; maxAvgQueryTime: number; maxErrorRate: number; alertThresholds: { slowQuery: number; highErrorRate: number; lowCacheHitRate: number; }; };
  backup: { enabled: boolean; autoBackupInterval: number; maxBackups: number; compressionEnabled: boolean; includeSettings: boolean; excludeTables: string[]; };
  security: { enableEncryption: boolean; encryptionKey?: string; enableAuditLog: boolean; sensitiveFields: string[]; dataRetentionDays: number; };
  performance: { enableQueryOptimization: boolean; enableIndexHints: boolean; connectionPoolSize: number; queryTimeout: number; transactionTimeout: number; enablePreparedStatements: boolean; };
  development: { enableDebugLogging: boolean; enableQueryLogging: boolean; enablePerformanceLogging: boolean; seedDataOnInit: boolean; resetDatabaseOnInit: boolean; };
}

// --- CONFIGURATIONS ---
export const DEFAULT_CONFIG: MasterDatabaseConfig = {
  database: { name: 'tailorza_master.db', location: 'default', version: 1, enableWAL: true, enableForeignKeys: true, busyTimeout: 30 * SECOND },
  cache: { enabled: true, maxSize: 1000, defaultTTL: 5 * MINUTE, persistToDisk: true, cleanupInterval: 10 * MINUTE },
  sync: { enabled: true, syncInterval: 30 * SECOND, maxRetries: 3, retryDelay: 5 * SECOND, batchSize: 50, conflictResolution: 'client' },
  monitoring: { enabled: true, healthCheckInterval: 1 * MINUTE, performanceTracking: true, maxAvgQueryTime: 1 * SECOND, maxErrorRate: 0.05, alertThresholds: { slowQuery: 2 * SECOND, highErrorRate: 0.1, lowCacheHitRate: 0.7 } },
  backup: { enabled: true, autoBackupInterval: 1 * HOUR, maxBackups: 10, compressionEnabled: true, includeSettings: true, excludeTables: ['audit_logs'] },
  security: { enableEncryption: false, enableAuditLog: true, sensitiveFields: ['password', 'ssn', 'credit_card'], dataRetentionDays: 365 },
  performance: { enableQueryOptimization: true, enableIndexHints: true, connectionPoolSize: 5, queryTimeout: 30 * SECOND, transactionTimeout: 1 * MINUTE, enablePreparedStatements: true },
  development: { enableDebugLogging: __DEV__ || false, enableQueryLogging: __DEV__ || false, enablePerformanceLogging: __DEV__ || false, seedDataOnInit: __DEV__ || false, resetDatabaseOnInit: false },
};

export const PRODUCTION_CONFIG: DeepPartial<MasterDatabaseConfig> = {
  security: { enableEncryption: true },
  sync: { conflictResolution: 'server' },
  backup: { autoBackupInterval: 1 * DAY, maxBackups: 7 },
  development: { enableDebugLogging: false, enableQueryLogging: false, enablePerformanceLogging: false },
};

export const DEVELOPMENT_CONFIG: DeepPartial<MasterDatabaseConfig> = {
  database: { name: 'tailorza_master_dev.db' },
  cache: { enabled: false },
  sync: { enabled: false },
  backup: { enabled: false },
  development: { enableDebugLogging: true, enableQueryLogging: true, enablePerformanceLogging: true, seedDataOnInit: true, resetDatabaseOnInit: true },
};

export const TEST_CONFIG: DeepPartial<MasterDatabaseConfig> = {
  database: { name: 'tailorza_master_test.db', enableWAL: false },
  cache: { enabled: false },
  sync: { enabled: false },
  monitoring: { enabled: false },
  backup: { enabled: false },
  security: { enableAuditLog: false },
  development: { seedDataOnInit: true, resetDatabaseOnInit: true },
};


// --- UTILITY FUNCTIONS ---
const isObject = (item: any): item is object => (item && typeof item === 'object' && !Array.isArray(item));

// --- FIX 2: Create a generic, type-safe deep merge function ---
function mergeDeep<T extends object>(target: T, source: DeepPartial<T>): T {
    const output = { ...target } as T;
    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!(key in target)) {
                    Object.assign(output, { [key]: source[key] });
                } else {
                    (output as any)[key] = mergeDeep((target as any)[key], source[key] as object);
                }
            } else {
                Object.assign(output, { [key]: source[key] });
            }
        }
    }
    return output;
}

export function getConfig(environment: 'development' | 'production' | 'test' = 'development'): MasterDatabaseConfig {
  let envConfig: DeepPartial<MasterDatabaseConfig>;
  switch (environment) {
    case 'production': envConfig = PRODUCTION_CONFIG; break;
    case 'test': envConfig = TEST_CONFIG; break;
    default: envConfig = DEVELOPMENT_CONFIG;
  }
  return mergeDeep(DEFAULT_CONFIG, envConfig);
}

export function validateConfig(config: MasterDatabaseConfig, environment: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  if (!config.database.name) {errors.push('Database name is required');}
  if (config.cache.enabled && config.cache.maxSize <= 0) {errors.push('Cache max size must be > 0');}
  if (config.sync.enabled && config.sync.syncInterval <= 0) {errors.push('Sync interval must be > 0');}
  if (environment === 'production' && config.security.enableEncryption && !config.security.encryptionKey) {
    errors.push('Encryption key is required for production when encryption is enabled.');
  }
  return { isValid: errors.length === 0, errors };
}