import { createStackNavigator } from '@react-navigation/stack';
import React, { useEffect } from 'react';

import { useTheme } from '../context/ThemeContext';
import LoginScreen from '../screens/auth/LoginScreen';
import LoggingService from '../services/LoggingService';

export type AuthStackParamList = {
  Login: undefined;
};

const Stack = createStackNavigator<AuthStackParamList>();

const AuthNavigator: React.FC = () => {
  const theme = useTheme();

  // IMPROVEMENT: Logging moved to useEffect to run only once on mount.
  useEffect(() => {
    LoggingService.debug('AuthNavigator rendered', 'NAV');
  }, []);

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        // IMPROVEMENT: Simpler and more consistent way to disable animations.
        animation: 'none',
      }}
      initialRouteName='Login'
    >
      {/* IMPROVEMENT: Redundant 'options' prop removed. */}
      <Stack.Screen name='Login' component={LoginScreen} />
    </Stack.Navigator>
  );
};

export default AuthNavigator;