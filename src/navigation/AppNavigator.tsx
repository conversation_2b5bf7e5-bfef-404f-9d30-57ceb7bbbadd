import { createStackNavigator } from '@react-navigation/stack';
import React, { useMemo } from 'react';

import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import {
  AddCustomerScreen, CustomerDetailsScreen,
  CustomersScreen, FinancialScreen, AddServiceScreen, ServiceScreen,
  ProfitLossScreen, ReportsScreen, TaxSummaryScreen, TransactionHistoryScreen,
} from '../screens/business';
import {
  AddItemScreen, InventoryDashboardScreen, InventoryItemDetailScreen,
  InventoryItemsScreen, StockOperationsScreen, StockTransferScreen, WarehouseManagementScreen
} from '../screens/inventory';
import { PaymentMethodsScreen, StaffManagementScreen } from '../screens/management';
import AddStaffScreen from '../screens/management/AddStaffScreen';
import DataScreen from '../screens/management/DataScreen';
import StaffDetailsScreen from '../screens/management/StaffDetailsScreen';
import CreateOrderScreen from '../screens/orders/CreateOrderScreen';
import OrderDetailsScreen from '../screens/orders/OrderDetailsScreen';
import OrdersScreen from '../screens/orders/OrdersScreen';
import OrderSuccessScreen from '../screens/orders/OrderSuccessScreen';
import QRScannerScreen from '../screens/QRScannerScreen';
import { ActivityLogScreen, EditProfileScreen, ProfileScreen as MyProfileScreen, NotificationsScreen } from '../screens/settings';
import { AboutScreen, AppStatusScreen, ContactSupportScreen, HelpFAQScreen, SearchScreen } from '../screens/support';
import { RootStackParamList } from '../types/navigation';

import TabNavigator from './TabNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const theme = useTheme();
  const { state } = useAuth();

  const screenOptions = useMemo(() => ({
    headerShown: false,
    cardStyle: { backgroundColor: theme.colors.background },
    animationEnabled: true,
    animationTypeForReplace: 'push' as const,
    gestureEnabled: true,
    gestureDirection: 'horizontal' as const,
    // IMPORTANT: Do not render a persistent overlay that can intercept touches.
    // We only animate the incoming card; omit overlayStyle entirely.
    cardStyleInterpolator: ({ current, layouts }: any) => ({
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    }),
    transitionSpec: {
      open: {
        animation: 'timing' as const,
        config: {
          duration: 300,
          useNativeDriver: true,
        },
      },
      close: {
        animation: 'timing' as const,
        config: {
          duration: 250,
          useNativeDriver: true,
        },
      },
    },
  }), [theme.colors.background]);

  if (!state.isAuthenticated || !state.user) {return null;}

  return (
    <Stack.Navigator screenOptions={screenOptions} initialRouteName="Main">
      <Stack.Screen name="Main" component={TabNavigator} />

      {/* Customers */}
      <Stack.Screen name="Customers" component={CustomersScreen} />
      <Stack.Screen name="CustomerDetails" component={CustomerDetailsScreen} />
      <Stack.Screen name="AddCustomer" component={AddCustomerScreen} />

      {/* Reports & Search */}
      <Stack.Screen 
        name="Reports" 
        component={ReportsScreen}
        options={{
          cardStyleInterpolator: ({ current }: any) => ({
            cardStyle: {
              opacity: current.progress,
            },
          }),
        }}
      />
      <Stack.Screen name="Search" component={SearchScreen} />

      {/* Profile & Settings */}
      <Stack.Screen name="Profile" component={MyProfileScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="Notifications" component={NotificationsScreen} />
      <Stack.Screen name="ActivityLog" component={ActivityLogScreen} />

      {/* Add Items */}
      <Stack.Screen name="AddItem" component={AddItemScreen} />

      {/* Orders */}
      <Stack.Screen name="Orders" component={OrdersScreen} />
      <Stack.Screen name="OrderDetails" component={OrderDetailsScreen} />
      <Stack.Screen name="AddOrder" component={CreateOrderScreen} />
      <Stack.Screen 
        name="CreateOrder" 
        component={CreateOrderScreen}
        options={{
          cardStyleInterpolator: ({ current }: any) => ({
            cardStyle: {
              opacity: current.progress,
            },
          }),
        }}
      />
      <Stack.Screen name="OrderSuccess" component={OrderSuccessScreen} />
      <Stack.Screen name="QRScanner" component={QRScannerScreen} />

      {/* Service Types */}
      <Stack.Screen name="ServiceTypes" component={ServiceScreen} />
      <Stack.Screen name="AddService" component={AddServiceScreen} />

      {/* Financial */}
      <Stack.Screen 
        name="Financial" 
        component={FinancialScreen}
        options={{
          cardStyleInterpolator: ({ current }: any) => ({
            cardStyle: {
              opacity: current.progress,
            },
          }),
        }}
      />
      <Stack.Screen name="ProfitLoss" component={ProfitLossScreen} />
      <Stack.Screen name="TaxSummary" component={TaxSummaryScreen} />

      {/* Inventory */}
      <Stack.Screen name="InventoryDashboard" component={InventoryDashboardScreen} />
      <Stack.Screen name="AddEditInventoryItem" component={AddItemScreen} />
      <Stack.Screen name="InventoryItemDetail" component={InventoryItemDetailScreen} />
      <Stack.Screen name="StockOperations" component={StockOperationsScreen} />
      <Stack.Screen name="StockTransfer" component={StockTransferScreen} />
      <Stack.Screen name="InventoryItems" component={InventoryItemsScreen} />
      <Stack.Screen name="TransactionHistory" component={TransactionHistoryScreen} />
      <Stack.Screen name="WarehouseManagement" component={WarehouseManagementScreen} />

      {/* Management */}
      <Stack.Screen name="PaymentMethods" component={PaymentMethodsScreen} />
      <Stack.Screen name="StaffManagement" component={StaffManagementScreen} />
      <Stack.Screen name="AddStaff" component={AddStaffScreen} />
      <Stack.Screen name="StaffDetails" component={StaffDetailsScreen} />
      <Stack.Screen name="DataManagement" component={DataScreen} />
      

      {/* Support */}
      <Stack.Screen name="HelpFAQ" component={HelpFAQScreen} />
      <Stack.Screen name="ContactSupport" component={ContactSupportScreen} />
      <Stack.Screen name="About" component={AboutScreen} />
      <Stack.Screen name="AppStatus" component={AppStatusScreen} />
    </Stack.Navigator>
  );
};

export default React.memo(AppNavigator);