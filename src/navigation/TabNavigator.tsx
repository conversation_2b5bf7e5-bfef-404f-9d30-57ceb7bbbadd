import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo } from 'react';

import NavBar from '../components/navigation/NavBar';
import DashboardScreen from '../screens/business/DashboardScreen';
import InventoryDashboardScreen from '../screens/inventory/InventoryDashboardScreen';
import OrdersScreen from '../screens/orders/OrdersScreen';
import ProfileScreen from '../screens/settings/ProfileScreen';
import LoggingService from '../services/LoggingService';

const Tab = createBottomTabNavigator();

interface TabNavigatorProps {
  navigation: any;
  route: any;
}

// Optimized dashboard wrapper with memoized navigation handler
const DashboardWrapper = React.memo(() => {
  const navigation = useNavigation();
  
  const navigateToTab = useCallback((tabName: string) => {
    navigation.navigate(tabName as never);
  }, [navigation]);

  return <DashboardScreen navigation={navigation} navigateToTab={navigateToTab} />;
});

const TabNavigator: React.FC<TabNavigatorProps> = ({ navigation }) => {
  // Simplified action mapping with better error handling
  const handleQuickAction = useCallback((action: string) => {
    const actions = {
      'add-product': 'AddProduct',
      'add-order': 'AddOrder', 
      'add-customer': 'AddCustomer',
      'add-inventory': 'AddEditInventoryItem'
    };

    const screen = actions[action as keyof typeof actions];
    
    if (!screen) {
      LoggingService.warn('Unknown quick action', 'TAB_NAVIGATOR', { action });
      return;
    }

    try {
      navigation.navigate(screen);
      LoggingService.info(`Navigated to ${screen}`, 'TAB_NAVIGATOR');
    } catch (error) {
      LoggingService.error(`Navigation failed for ${action}`, 'TAB_NAVIGATOR', error as Error);
    }
  }, [navigation]);

  // Memoized tab bar to prevent unnecessary re-renders
  const tabBar = useCallback((props: any) => (
    <NavBar {...props} onQuickAction={handleQuickAction} />
  ), [handleQuickAction]);

  // Static screen options
  const screenOptions = useMemo(() => ({ 
    headerShown: false,
    lazy: true 
  }), []);

  return (
    <Tab.Navigator tabBar={tabBar} screenOptions={screenOptions}>
      <Tab.Screen 
        name="Home" 
        component={DashboardWrapper}
        options={{ tabBarLabel: 'Home' }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersScreen}
        options={{ tabBarLabel: 'Orders' }}
      />
      <Tab.Screen
        name="Add"
        component={DashboardWrapper}
        options={{ tabBarLabel: 'Add' }}
        listeners={{ tabPress: (e: any) => e.preventDefault() }}
      />
      <Tab.Screen 
        name="Inventory" 
        component={InventoryDashboardScreen}
        options={{ tabBarLabel: 'Inventory' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

export default React.memo(TabNavigator);