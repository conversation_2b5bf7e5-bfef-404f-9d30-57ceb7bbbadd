import React, { useMemo } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';

import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import LoggingService from '../services/LoggingService';
import { SPACING, TYPOGRAPHY } from '../theme/theme';

import AppNavigator from './AppNavigator';
import AuthNavigator from './AuthNavigator';

const RootNavigator: React.FC = () => {
  const { state } = useAuth();
  const theme = useTheme();

  const content = useMemo(() => {
    LoggingService.debug(`Auth state: loading=${state.isLoading}, auth=${state.isAuthenticated}, user=${state.user?.username}`, 'NAV');

    if (state.isLoading) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background, gap: SPACING.lg }}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={{ fontSize: TYPOGRAPHY.fontSize.lg, color: theme.colors.onSurface, fontWeight: TYPOGRAPHY.fontWeight.medium }}>
            Checking authentication...
          </Text>
        </View>
      );
    }

    return state.isAuthenticated && state.user ? <AppNavigator /> : <AuthNavigator />;
  }, [state.isLoading, state.isAuthenticated, state.user, theme]);

  return content;
};

export default React.memo(RootNavigator);