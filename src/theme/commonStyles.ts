import { StyleSheet } from 'react-native';

export const commonStyles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  marginTop8: {
    marginTop: 8,
  },
  borderRadius8: {
    borderRadius: 8,
  },
  padding16: {
    padding: 16,
  },
  margin16: {
    margin: 16,
  },
  paddingHorizontal16: {
    paddingHorizontal: 16,
  },
  paddingVertical8: {
    paddingVertical: 8,
  },
  marginBottom16: {
    marginBottom: 16,
  },
  marginTop16: {
    marginTop: 16,
  },
  textCenter: {
    textAlign: 'center',
  },
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
  },
  column: {
    flexDirection: 'column',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  spaceEvenly: {
    justifyContent: 'space-evenly',
  },
  alignStart: {
    alignItems: 'flex-start',
  },
  alignEnd: {
    alignItems: 'flex-end',
  },
  justifyStart: {
    justifyContent: 'flex-start',
  },
  justifyEnd: {
    justifyContent: 'flex-end',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  borderRadius4: {
    borderRadius: 4,
  },
  borderRadius12: {
    borderRadius: 12,
  },
  borderRadius16: {
    borderRadius: 16,
  },
  padding8: {
    padding: 8,
  },
  padding12: {
    padding: 12,
  },
  margin8: {
    margin: 8,
  },
  margin12: {
    margin: 12,
  },
  marginHorizontal8: {
    marginHorizontal: 8,
  },
  marginHorizontal16: {
    marginHorizontal: 16,
  },
  marginVertical8: {
    marginVertical: 8,
  },
  marginVertical16: {
    marginVertical: 16,
  },
  paddingTop16: {
    paddingTop: 16,
  },
  paddingBottom16: {
    paddingBottom: 16,
  },
  marginLeft8: {
    marginLeft: 8,
  },
  marginRight8: {
    marginRight: 8,
  },
  marginLeft16: {
    marginLeft: 16,
  },
  marginRight16: {
    marginRight: 16,
  },
  width100: {
    width: '100%',
  },
  height100: {
    height: '100%',
  },
  absolute: {
    position: 'absolute',
  },
  relative: {
    position: 'relative',
  },
  zIndex1: {
    zIndex: 1,
  },
  zIndex2: {
    zIndex: 2,
  },
  zIndex3: {
    zIndex: 3,
  },
  opacity50: {
    opacity: 0.5,
  },
  opacity75: {
    opacity: 0.75,
  },
  hidden: {
    display: 'none',
  },
  visible: {
    display: 'flex',
  },
});
