/**
 * React Native Paper Theme Adapter
 * Uses Paper's built-in themes and overrides colors as needed
 */

import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

export const createPaperTheme = (isDarkMode: boolean) => {
  // Use Paper's built-in theme as the base
  const baseTheme = isDarkMode ? MD3DarkTheme : MD3LightTheme;

  // Return the base theme with minimal overrides
  return {
    ...baseTheme,
    // Only override essential colors that we need
    colors: {
      ...baseTheme.colors,
      // Add any custom color overrides here if needed
    },
  };
};

// Fallback theme for when the main theme is not available
export const getFallbackTheme = () => {
  return MD3LightTheme;
};
