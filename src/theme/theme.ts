/**
 * Unified Theme System - Colors, Design Tokens, and Theme Configuration
 * All theme-related values including colors, spacing, typography, and design tokens
 */

// ============================================================================
// THEME COLOR INTERFACES
// ============================================================================

interface ThemeColors {
  primary: string;
  primaryContainer: string;
  secondary: string;
  secondaryContainer: string;
  tertiary: string;
  tertiaryContainer: string;
  surface: string;
  surfaceVariant: string;
  background: string;
  error: string;
  errorContainer: string;
  onPrimary: string;
  onPrimaryContainer: string;
  onSecondary: string;
  onSecondaryContainer: string;
  onTertiary: string;
  onTertiaryContainer: string;
  onSurface: string;
  onSurfaceVariant: string;
  on: string;
  onVariant: string;
  onError: string;
  onErrorContainer: string;
  onBackground: string;
  outline: string;
  outlineVariant: string;
  inverse: string;
  inverseOn: string;
  inversePrimary: string;
  // Additional colors needed by components
  primaryDark: string;
  errorDark: string;
  warning: string;
  warningDark: string;
  success: string;
  successDark: string;
  info: string;
  infoDark: string;
  // Toast-specific colors
  toast: {
    surface: string;
    surfaceVariant: string;
    success: string;
    error: string;
    warning: string;
    info: string;
    successText: string;
    errorText: string;
    warningText: string;
    infoText: string;
    defaultText: string;
  };
}

interface Theme {
  colors: ThemeColors;
  fonts: {
    regular: {
      fontFamily: string;
      fontWeight: string;
    };
    medium: {
      fontFamily: string;
      fontWeight: string;
    };
    light: {
      fontFamily: string;
      fontWeight: string;
    };
    thin: {
      fontFamily: string;
      fontWeight: string;
    };
    //variants for react-native-paper
    titleLarge: {
      fontFamily: string;
      fontWeight: string;
    };
    titleMedium: {
      fontFamily: string;
      fontWeight: string;
    };
    titleSmall: {
      fontFamily: string;
      fontWeight: string;
    };
    bodyLarge: {
      fontFamily: string;
      fontWeight: string;
    };
    bodyMedium: {
      fontFamily: string;
      fontWeight: string;
    };
    bodySmall: {
      fontFamily: string;
      fontWeight: string;
    };
    labelLarge: {
      fontFamily: string;
      fontWeight: string;
    };
    labelMedium: {
      fontFamily: string;
      fontWeight: string;
    };
    labelSmall: {
      fontFamily: string;
      fontWeight: string;
    };
    headlineLarge: {
      fontFamily: string;
      fontWeight: string;
    };
    headlineMedium: {
      fontFamily: string;
      fontWeight: string;
    };
    headlineSmall: {
      fontFamily: string;
      fontWeight: string;
    };
  };
  spacing: typeof SPACING;
  borderRadius: typeof BORDER_RADIUS;
  typography: typeof TYPOGRAPHY;
  shadows: typeof SHADOWS;
  componentSizes: typeof COMPONENT_SIZES;
  layout: typeof LAYOUT;
  animations: typeof ANIMATIONS;
  opacity: typeof OPACITY;
  zIndex: typeof Z_INDEX;
  breakpoints: typeof BREAKPOINTS;
  commonStyles: typeof COMMON_STYLES;
  mode?: 'light' | 'dark';
  roundness: number;
  animation: {
    scale: number;
  };
  isV3: boolean;
}

// ============================================================================
// DESIGN TOKENS - SPACING, TYPOGRAPHY, SHADOWS, ETC.
// ============================================================================

// Spacing System (8pt grid)
export const SPACING = {
  xxs: 2,
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
  xxxxxl: 48,
  xxxxxxl: 64,
} as const;

// Border Radius System
export const BORDER_RADIUS = {
  xs: 4,
  sm: 6,
  md: 8,
  lg: 10,
  xl: 12,
  xxl: 16,
  xxxl: 20,
  round: 25,
  circle: 50,
  full: 9999,
} as const;

// Typography System
export const TYPOGRAPHY = {
  fontSize: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    xxxxl: 28,
    xxxxxl: 32,
    xxxxxxl: 36,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
} as const;

// Shadow System
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  md: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  lg: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xl: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
} as const;

// Component Sizes
export const COMPONENT_SIZES = {
  button: {
    sm: { height: 32, paddingHorizontal: 12 },
    md: { height: 40, paddingHorizontal: 16 },
    lg: { height: 48, paddingHorizontal: 20 },
  },
  input: {
    sm: { height: 32 },
    md: { height: 40 },
    lg: { height: 48 },
  },
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 28,
    xxl: 32,
  },
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
    xxl: 64,
  },
} as const;

// Layout Constants
export const LAYOUT = {
  headerHeight: 56,
  tabBarHeight: 60,
  bottomSheetHeaderHeight: 60,
  listItemHeight: 56,
  cardMinHeight: 80,
  screenPadding: SPACING.lg,
  sectionSpacing: SPACING.xl,
} as const;

// Animation Durations
export const ANIMATIONS = {
  fast: 150,
  normal: 250,
  slow: 350,
  verySlow: 500,
} as const;

// Opacity Values
export const OPACITY = {
  disabled: 0.4,
  pressed: 0.7,
  overlay: 0.5,
  subtle: 0.6,
  medium: 0.8,
  high: 0.9,
} as const;

// Z-Index Values
export const Z_INDEX = {
  background: -1,
  normal: 0,
  elevated: 1,
  sticky: 10,
  overlay: 100,
  modal: 1000,
  popover: 1100,
  tooltip: 1200,
  notification: 1300,
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
} as const;

// Common Styles
export const COMMON_STYLES = {
  flexCenter: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexColumn: {
    flexDirection: 'column',
  },
  flexBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  absoluteFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
} as const;

// ============================================================================
// THEME OBJECTS (LIGHT & DARK)
// ============================================================================

const lightTheme: Theme = {
  colors: {
    primary: '#1877F2', // Facebook Blue
    primaryContainer: '#E3F2FD',
    secondary: '#42A5F5', // Lighter Facebook Blue
    secondaryContainer: '#E1F5FE',
    tertiary: '#FF6B35', // Facebook Orange accent
    tertiaryContainer: '#FFE0B2',
    surface: '#FFFFFF',
    surfaceVariant: '#F0F2F5', // FacebookGray
    background: '#F8F9FA', // Facebook Light Gray
    error: '#F44336',
    errorContainer: '#FFEBEE',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#0D47A1',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#01579B',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#E65100',
    onSurface: '#1C1E21', // Facebook Dark
    onSurfaceVariant: '#65676B', // Facebook Secondary
    on: '#1C1E21', // Facebook Dark
    onVariant: '#65676B', // Facebook Secondary
    onError: '#FFFFFF',
    onErrorContainer: '#B71C1C',
    onBackground: '#1C1E21',
    outline: '#CED0D4', // Facebook Border Gray
    outlineVariant: '#E4E6EA',
    inverse: '#242526', // Facebook Dark Mode
    inverseOn: '#E4E6EA',
    inversePrimary: '#4FC3F7',
    // Additional colors
    primaryDark: '#0D47A1',
    errorDark: '#D32F2F',
    warning: '#FF9800',
    warningDark: '#F57C00',
    success: '#4CAF50',
    successDark: '#388E3C',
    info: '#2196F3',
    infoDark: '#1976D2',
    // Toast-specific colors
    toast: {
      surface: '#FFFFFF',
      surfaceVariant: '#F8F9FA',
      success: '#10B981',
      error: '#EF4444',
      warning: '#F59E0B',
      info: '#3B82F6',
      successText: '#065F46',
      errorText: '#991B1B',
      warningText: '#92400E',
      infoText: '#1E40AF',
      defaultText: '#374151',
    },
  },
  fonts: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
    //variants for react-native-paper
    titleLarge: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    titleMedium: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    titleSmall: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    bodyLarge: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    bodyMedium: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    bodySmall: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    labelLarge: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    labelMedium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    labelSmall: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    headlineLarge: {
      fontFamily: 'System',
      fontWeight: '700',
    },
    headlineMedium: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    headlineSmall: {
      fontFamily: 'System',
      fontWeight: '600',
    },
  },
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  typography: TYPOGRAPHY,
  shadows: SHADOWS,
  componentSizes: COMPONENT_SIZES,
  layout: LAYOUT,
  animations: ANIMATIONS,
  opacity: OPACITY,
  zIndex: Z_INDEX,
  breakpoints: BREAKPOINTS,
  commonStyles: COMMON_STYLES,
  mode: 'light',
  roundness: 4,
  animation: {
    scale: 0.0,
  },
  isV3: true,
};

const darkTheme: Theme = {
  colors: {
    primary: '#4FC3F7', // Light Facebook Blue for dark mode
    primaryContainer: '#0D47A1',
    secondary: '#42A5F5', // Light green accent
    secondaryContainer: '#2E7D32',
    tertiary: '#FFB74D', // Light orange accent
    tertiaryContainer: '#F57C00',
    surface: '#242526', // Facebook Dark
    surfaceVariant: '#3A3B3C', // Facebook DarkVariant
    background: '#18191A', // Facebook Dark Background
    error: '#EF5350',
    errorContainer: '#C62828',
    onPrimary: '#0D47A1',
    onPrimaryContainer: '#E3F2FD',
    onSecondary: '#2E7D32',
    onSecondaryContainer: '#E8F5E8',
    onTertiary: '#F57C00',
    onTertiaryContainer: '#FFF3E0',
    onSurface: '#E4E6EA', // Facebook Dark
    onSurfaceVariant: '#B0B3B8', // Facebook Dark Secondary
    on: '#E4E6EA', // Facebook Dark
    onVariant: '#B0B3B8', // Facebook Dark Secondary
    onError: '#C62828',
    onErrorContainer: '#FFEBEE',
    onBackground: '#E4E6EA',
    outline: '#5A5C5E', // Facebook Dark Border
    outlineVariant: '#3A3B3C',
    inverse: '#F8F9FA',
    inverseOn: '#242526',
    inversePrimary: '#1877F2',
    // Additional colors
    primaryDark: '#0D47A1',
    errorDark: '#D32F2F',
    warning: '#FF9800',
    warningDark: '#F57C00',
    success: '#4CAF50',
    successDark: '#388E3C',
    info: '#2196F3',
    infoDark: '#1976D2',
    // Toast-specific colors
    toast: {
      surface: '#1F2937',
      surfaceVariant: '#374151',
      success: '#10B981',
      error: '#EF4444',
      warning: '#F59E0B',
      info: '#3B82F6',
      successText: '#D1FAE5',
      errorText: '#FEE2E2',
      warningText: '#FEF3C7',
      infoText: '#DBEAFE',
      defaultText: '#F9FAFB',
    },
  },
  fonts: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
    //variants for react-native-paper
    titleLarge: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    titleMedium: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    titleSmall: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    bodyLarge: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    bodyMedium: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    bodySmall: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    labelLarge: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    labelMedium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    labelSmall: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    headlineLarge: {
      fontFamily: 'System',
      fontWeight: '700',
    },
    headlineMedium: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    headlineSmall: {
      fontFamily: 'System',
      fontWeight: '600',
    },
  },
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  typography: TYPOGRAPHY,
  shadows: SHADOWS,
  componentSizes: COMPONENT_SIZES,
  layout: LAYOUT,
  animations: ANIMATIONS,
  opacity: OPACITY,
  zIndex: Z_INDEX,
  breakpoints: BREAKPOINTS,
  commonStyles: COMMON_STYLES,
  mode: 'dark',
  roundness: 4,
  animation: {
    scale: 0.0,
  },
  isV3: true,
};

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

export const getBorderColor = (theme: any): string => {
  return theme.colors.outline;
};

export const getThemedShadow = (theme: any, shadowKey: keyof typeof SHADOWS = 'md') => {
  const shadow = SHADOWS[shadowKey];
  return {
    ...shadow,
    shadowColor: theme.colors.on,
  };
};

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type SpacingKey = keyof typeof SPACING;
export type BorderRadiusKey = keyof typeof BORDER_RADIUS;
export type TypographyFontSizeKey = keyof typeof TYPOGRAPHY.fontSize;
export type TypographyFontWeightKey = keyof typeof TYPOGRAPHY.fontWeight;
export type ShadowKey = keyof typeof SHADOWS;
export type ComponentSizeKey = keyof typeof COMPONENT_SIZES;
export type SizeKey = keyof typeof COMPONENT_SIZES.icon;
export type AvatarSizeKey = keyof typeof COMPONENT_SIZES.avatar;

// ============================================================================
// EXPORTS
// ============================================================================

export { lightTheme, darkTheme };
export type { Theme, ThemeColors };
