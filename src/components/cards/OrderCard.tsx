import React, { useMemo, useCallback, memo } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '@/context/ThemeContext';
import { Order, OrderStatus } from '@/types/order'; // Import Order and OrderStatus from types/order
import { formatCurrency } from '@/utils/currency';

// --- Constants & Types ---
type Theme = ReturnType<typeof useTheme>;

const statusColors: { [key in OrderStatus]: { bg: string; text: string } } = {
  'Pending': { bg: '#a16207', text: '#fff' }, // Amber-700
  'Ready': { bg: '#15803d', text: '#fff' }, // Green-700
  'Cancelled': { bg: '#991b1b', text: '#fff' }, // Red-800
  'In Progress': { bg: '#1e40af', text: '#fff' }, // Blue-800
  'Completed': { bg: '#059669', text: '#fff' }, // Emerald-600
  'Delivered': { bg: '#059669', text: '#fff' }, // Emerald-600
  'On Hold': { bg: '#F59E0B', text: '#fff' }, // Amber-500
};



// --- Custom Hook for Business Logic ---

const useOrderDetails = (order: Order, theme: Theme) => {
  return useMemo(() => {
    const status = order.status; // Directly use the PascalCase status
    const totalAmount = order.totalAmount ?? order.total ?? 0; // Prioritize totalAmount
    const paidAmount = order.paidAmount ?? 0;
    const dueAmountRaw = Math.max(0, totalAmount - paidAmount);
    const isPaid = dueAmountRaw <= 0 || order.paymentStatus === 'paid'; // Use paymentStatus

    const dueAmount = isPaid ? 0 : dueAmountRaw;

    return {
      status,
      statusColor: statusColors[status] || { bg: theme.colors.primary, text: theme.colors.onPrimary },
      itemCount: order.items?.length || 0,
      isPaid,
      dueAmount,
      totalAmount,
      formattedDate: (() => {
        const ds = order.dueDate ?? order.date ?? order.orderDate ?? order.createdAt;
        return ds ? new Date(ds).toLocaleDateString() : 'No date';
      })(),
      customerName: order.customerName || order.customer || 'Walk-in Customer',
    };
  }, [order, theme]);
};

// --- Main Component ---

const OrderCard: React.FC<{ order: Order; onPress: (order: Order) => void; }> = ({ order, onPress }) => {
  const theme = useTheme();
  const styles = useMemo(() => createCardStyles(theme), [theme]);
  const details = useOrderDetails(order, theme);

  const handlePress = useCallback(() => onPress(order), [onPress, order]);

  return (
    <TouchableOpacity style={styles.card} activeOpacity={0.85} onPress={handlePress}>
      <View style={styles.headerRow}>
        <Text style={styles.orderNumber}>ORD-{order.id}</Text>
        <View style={[styles.statusBadge, { backgroundColor: details.statusColor.bg }]}>
          <Text style={[styles.statusText, { color: details.statusColor.text }]}>
            {details.status}
          </Text>
        </View>
      </View>

      <Text style={styles.itemsLink}>{details.itemCount} Item{details.itemCount !== 1 ? 's' : ''}</Text>
      <Text style={styles.customerName}>{details.customerName}{(order.phone || order.customer?.phone || order.customer_phone) ? ` • ${order.phone || order.customer?.phone || order.customer_phone}` : ''}</Text>

      <View style={styles.footerRow}>
        <Text style={styles.dateText}>Due {details.formattedDate}</Text>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>{formatCurrency(details.totalAmount)}</Text>
          {details.dueAmount <= 0 ? (
            <Text style={styles.paidText}>Paid</Text>
          ) : (
            <Text style={styles.dueText}>
              Due {formatCurrency(details.dueAmount)}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

// --- Styles ---

const createCardStyles = (theme: Theme) => StyleSheet.create({
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.outlineVariant,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  orderNumber: {
    color: theme.colors.onSurface,
    fontWeight: 'bold',
    fontSize: 16,
  },
  statusBadge: {
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  statusText: {
    fontWeight: 'bold',
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  itemsLink: {
    color: theme.colors.primary,
    fontWeight: '500',
    fontSize: 14,
    marginBottom: 2,
  },
  customerName: {
    color: theme.colors.onSurface,
    fontSize: 14,
    marginBottom: 8,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  dateText: {
    color: theme.colors.onSurfaceVariant,
    fontSize: 12,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    color: theme.colors.onSurface,
    fontWeight: 'bold',
    fontSize: 16,
  },
  dueText: {
    color: theme.colors.error, // Theme-aware color
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 2,
  },
  paidText: {
    color: theme.colors.success, // Theme-aware color
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 2,
  },
});

export default memo(OrderCard);