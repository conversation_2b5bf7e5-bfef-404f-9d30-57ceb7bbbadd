import { useRoute } from '@react-navigation/native';
import React, { useMemo, useCallback, memo, ReactElement } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { Avatar } from 'react-native-paper';

import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPE DERIVATION ---
type AppTheme = ReturnType<typeof useTheme>;

// --- CONFIGURATION ---
const TAB_CONFIG = [
  { name: 'Home', icon: 'house', label: 'Home', route: 'Home', isAddButton: false },
  { name: 'Orders', icon: 'receipt', label: 'Orders', route: 'Orders', isAddButton: false },
  { name: 'Add', icon: 'plus', label: '', route: 'AddOrder', isAddButton: true },
  { name: 'Inventory', icon: 'package', label: 'Inventory', route: 'Inventory', isAddButton: false },
  { name: 'Profile', icon: 'user', label: 'Profile', route: 'Profile', isAddButton: false },
] as const;

type TabItem = (typeof TAB_CONFIG)[number];

// --- SUB-COMPONENT: NavItem ---
interface NavItemProps {
  tab: TabItem;
  isActive: boolean;
  onPress: (tab: TabItem) => void;
  profileAvatar?: ReactElement;
}

const NavItem = memo(({ tab, isActive, onPress, profileAvatar }: NavItemProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  // Restored original icon size logic
  const iconSize = isActive ? 28 : 24;

  const iconColor = tab.isAddButton
    ? theme.colors.onPrimary // Adjusted for '+' button on primary background
    : isActive ? theme.colors.primary : theme.colors.onSurface;

  const textColor = isActive ? theme.colors.primary : theme.colors.onSurface;

  const containerStyle = [
    styles.iconContainer,
    tab.isAddButton && styles.addButtonContainer,
    isActive && !tab.isAddButton && styles.activeIconContainer,
  ];

  return (
    <TouchableOpacity
      style={[styles.tab, tab.isAddButton && styles.addTab]}
      onPress={() => onPress(tab)}
      activeOpacity={0.7}
      accessibilityLabel={tab.label || tab.name}
    >
      <View style={containerStyle}>
        {tab.name === 'Profile' && profileAvatar ? profileAvatar : <PhosphorIcon name={tab.icon as PhosphorIconName} size={iconSize} color={iconColor} />}
      </View>
      {tab.label && <Text style={[styles.label, { color: textColor }]}>{tab.label}</Text>}
    </TouchableOpacity>
  );
});
NavItem.displayName = 'NavItem';

// --- MAIN NAVBAR COMPONENT ---
interface NavBarProps {
  navigation?: any;
}

const NavBar: React.FC<NavBarProps> = ({ navigation }) => {
  const route = useRoute();
  const theme = useTheme();
  const { state: dataState } = useData();
  const currentRoute = route.name;
  const styles = createStyles(theme);

  const profileInfo = useMemo(() => {
    const profileImage = dataState.settings?.profileImage;
    const storeName = dataState.settings?.storeName;
    const initials = typeof storeName === 'string' && storeName ? storeName.split(' ').map((w: string) => w[0]).join('').substring(0, 2).toUpperCase() : 'ME';
    return { profileImage, initials };
  }, [dataState.settings]);

  const handleTabPress = useCallback((tab: TabItem) => {
    if (!navigation) {return;}
    if (currentRoute !== tab.route) {
      navigation.navigate(tab.route);
    }
  }, [navigation, currentRoute]);

  const ProfileAvatar = useMemo(() => {
    return profileInfo.profileImage ? (
      <Avatar.Image size={28} source={{ uri: profileInfo.profileImage }} style={styles.profileAvatar} />
    ) : (
      <Avatar.Text size={28} label={profileInfo.initials} style={[styles.profileAvatar, { backgroundColor: theme.colors.outline }]} labelStyle={styles.avatarLabel}/>
    );
  }, [profileInfo, theme.colors, styles]);

  return (
    <View style={styles.container}>
      {TAB_CONFIG.map(tab => (
        <NavItem
          key={tab.name}
          tab={tab}
          isActive={currentRoute === tab.route && !tab.isAddButton}
          onPress={handleTabPress}
          profileAvatar={tab.name === 'Profile' ? ProfileAvatar : undefined}
        />
      ))}
    </View>
  );
};

// --- STYLES (Restored to original visual specifications) ---
const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      height: 60,
      paddingVertical: 8,
      paddingHorizontal: 12,
      backgroundColor: theme.colors.surface,
      elevation: 8,
      shadowColor: theme.colors.onSurface,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    tab: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addTab: {
      justifyContent: 'flex-start',
      paddingBottom: 0,
    },
    iconContainer: {
      width: 28,
      height: 28,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 0,
    },
    addButtonContainer: {
      width: 40,
      height: 40,
      borderRadius: 28,
      backgroundColor: theme.colors.primary, // Set background for '+' button
      elevation: 2,
      shadowColor: theme.colors.onSurface,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    activeIconContainer: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.primaryContainer, // Use themed background for active state
    },
    label: {
      fontSize: TYPOGRAPHY.fontSize.xs,
      fontWeight: TYPOGRAPHY.fontWeight.normal,
      textAlign: 'center',
      marginTop: 2,
    },
    profileAvatar: {
      width: 28,
      height: 28,
      borderRadius: 14,
    },
    avatarLabel: {
        fontSize: 12,
        lineHeight: 14,
    }
  });

export default memo(NavBar);