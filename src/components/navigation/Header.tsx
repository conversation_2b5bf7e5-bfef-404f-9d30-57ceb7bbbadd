import { useNavigation } from '@react-navigation/native';
import React, { useState, useRef, useEffect, useCallback, memo, ReactElement, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, BackHandler, ViewStyle, Text } from 'react-native';
import { Badge } from 'react-native-paper';
import Animated, { useSharedValue, withTiming, useAnimatedStyle, interpolate } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import Search, { SearchRef } from '../forms/Search';

// --- Type Definitions ---
interface HeaderAction {
  icon?: PhosphorIconName;
  text?: string;
  onPress: () => void;
  disabled?: boolean;
  color?: string;
}

interface HeaderProps {
  title?: string;
  onBackPress?: () => void;
  showBack?: boolean;
  actions?: HeaderAction[];
  showSearch?: boolean;
  searchPlaceholder?: string;
  onSearchChange?: (query: string) => void;
  onSearchSubmit?: (query: string) => void;
  onSearchIconPress?: () => void;
  showNotifications?: boolean;
  notificationCount?: number;
  onNotificationPress?: () => void;
  backgroundColor?: string;
  style?: ViewStyle;
  rightComponent?: ReactElement;
}

const HEADER_HEIGHT = 56;

// --- SUB-COMPONENT: HeaderActions ---
const HeaderActions = memo(({ showSearch, showNotifications, notificationCount, onNotificationPress, onSearchIconPress, onSearchPress, actions = [], rightComponent }: any) => {
    const theme = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const handleSearchPress = onSearchIconPress ? onSearchIconPress : onSearchPress;

    if (rightComponent) {return rightComponent;}

    return (
        <>
            {showSearch && (
                <TouchableOpacity onPress={handleSearchPress} style={styles.button}>
                    <PhosphorIcon name='magnifying-glass' size={24} color={theme.colors.onSurface} />
                </TouchableOpacity>
            )}
            {showNotifications && (
                <TouchableOpacity onPress={onNotificationPress} style={styles.notificationButton}>
                    <PhosphorIcon name='bell' size={24} color={theme.colors.onSurface} />
                    {notificationCount != null && notificationCount > 0 && <Badge size={16} style={styles.badge}>{notificationCount > 99 ? '99+' : notificationCount}</Badge>}
                </TouchableOpacity>
            )}
            {actions.map((action: HeaderAction, index: number) => (
                <TouchableOpacity key={index} onPress={action.onPress} disabled={action.disabled} style={styles.button}>
                    {action.icon ? <PhosphorIcon name={action.icon} size={24} color={action.color || theme.colors.onSurface} /> : (action.text ? <Text style={{ color: action.color || theme.colors.primary }}>{action.text}</Text> : null)}
                </TouchableOpacity>
            ))}
        </>
    );
});
HeaderActions.displayName = 'HeaderActions';

// --- SUB-COMPONENT: DefaultHeaderContent ---
const DefaultHeaderContent = memo(({ title, showBack, onBackPress, ...actionProps }: any) => {
    const theme = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    return (
        <>
            <View style={styles.leftSection}>
                {showBack && (
                    <TouchableOpacity onPress={onBackPress} style={styles.button}>
                        <PhosphorIcon name='arrow-left' size={24} color={theme.colors.onSurface} />
                    </TouchableOpacity>
                )}
            </View>
            <View style={styles.centerSection}>
                <Text style={[styles.title, { color: theme.colors.onSurface }]} numberOfLines={1}>{title}</Text>
            </View>
            <View style={styles.rightSection}>
                <HeaderActions {...actionProps} />
            </View>
        </>
    );
});
DefaultHeaderContent.displayName = 'DefaultHeaderContent';

// --- SUB-COMPONENT: SearchHeaderContent ---
const SearchHeaderContent = memo(({ searchRef, onSearchChange, onSearchSubmit, placeholder, onClose }: any) => {
    const theme = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    return (
        <>
            <TouchableOpacity onPress={onClose} style={styles.button}>
                <PhosphorIcon name='arrow-left' size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>
            <Search 
              ref={searchRef} 
              placeholder={placeholder} 
              onSearchChange={onSearchChange} 
              onSearchSubmit={onSearchSubmit} 
              style={styles.searchBar} 
            />
        </>
    );
});
SearchHeaderContent.displayName = 'SearchHeaderContent';

// --- MAIN CONTROLLER COMPONENT: Header ---
const Header: React.FC<HeaderProps> = (props) => {
    const { onBackPress, searchPlaceholder, onSearchChange, onSearchSubmit, backgroundColor, style } = props;
    const theme = useTheme();
    const styles = useMemo(() => createStyles(theme), [theme]);
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();
    const searchInputRef = useRef<SearchRef>(null);
    const [isSearchVisible, setIsSearchVisible] = useState(false);
    const searchProgress = useSharedValue(0);
    const barBackgroundColor = backgroundColor || theme.colors.surface;

    const toggleSearch = useCallback(() => {
        setIsSearchVisible(current => {
            const nextState = !current;
            searchProgress.value = withTiming(nextState ? 1 : 0, { duration: 250 });
            if (nextState) {
                setTimeout(() => searchInputRef.current?.focus(), 200);
            } else {
                searchInputRef.current?.blur();
            }
            return nextState;
        });
    }, [searchProgress]);

    useEffect(() => {
        const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
            if (isSearchVisible) {
                toggleSearch();
                return true; 
            }
            return false;
        });
        return () => backHandler.remove();
    }, [isSearchVisible, toggleSearch]);

    const handleBackPress = useCallback(() => {
        onBackPress ? onBackPress() : navigation.goBack();
    }, [onBackPress, navigation]);

    const headerContentStyle = useAnimatedStyle(() => ({
        opacity: interpolate(searchProgress.value, [0, 0.5], [1, 0]),
        transform: [{ translateY: interpolate(searchProgress.value, [0, 1], [0, -20]) }],
        display: searchProgress.value < 0.5 ? 'flex' : 'none',
    }));

    const searchContentStyle = useAnimatedStyle(() => ({
        opacity: searchProgress.value,
        transform: [{ translateY: interpolate(searchProgress.value, [0, 1], [20, 0]) }],
        display: searchProgress.value > 0.5 ? 'flex' : 'none',
    }));

    return (
        <View style={[styles.container, { backgroundColor: barBackgroundColor, paddingTop: insets.top }, style]}>
            <View style={styles.content}>
                <Animated.View
                  style={[styles.headerContentContainer, headerContentStyle]}
                  pointerEvents={isSearchVisible ? 'none' : 'auto'}
                >
                    <DefaultHeaderContent {...props} onBackPress={handleBackPress} onSearchPress={toggleSearch} />
                </Animated.View>

                <Animated.View
                  style={[styles.searchContentContainer, searchContentStyle]}
                  pointerEvents={isSearchVisible ? 'auto' : 'none'}
                >
                    <SearchHeaderContent
                        searchRef={searchInputRef}
                        placeholder={searchPlaceholder || 'Search...'}
                        onSearchChange={onSearchChange}
                        onSearchSubmit={onSearchSubmit}
                        onClose={toggleSearch}
                    />
                </Animated.View>
            </View>
        </View>
    );
};

// FIX: Moved styles into a creator function to access the theme
const createStyles = (theme: any) => StyleSheet.create({
    container: { },
    content: { height: HEADER_HEIGHT, justifyContent: 'center' },
    headerContentContainer: { flex: 1, flexDirection: 'row', alignItems: 'center', paddingHorizontal: SPACING.sm },
    searchContentContainer: { ...StyleSheet.absoluteFillObject, flexDirection: 'row', alignItems: 'center', paddingHorizontal: SPACING.sm },
    leftSection: { justifyContent: 'center', alignItems: 'flex-start' },
    centerSection: { flex: 1, justifyContent: 'center', alignItems: 'flex-start', marginHorizontal: SPACING.sm },
    rightSection: { flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center' },
    title: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.bold },
    button: { padding: SPACING.sm, borderRadius: BORDER_RADIUS.round },
    searchBar: { flex: 1, marginLeft: SPACING.sm },
    notificationButton: { position: 'relative', padding: SPACING.sm },
    badge: { position: 'absolute', top: 6, right: 6, fontSize: TYPOGRAPHY.fontSize.xs, backgroundColor: theme.colors.error },
});

export default memo(Header);