import React, { Component, ErrorInfo, ReactNode, useCallback, useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Card, Button } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// --- Type Definitions ---
interface ErrorBoundaryProps {
  children: ReactNode;
  onRetry?: () => void;
  onReload?: () => void;
  fallback?: (error: Error | null, retry: () => void) => ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorId: string;
}

// --- OPTIMIZATION 1: Extracted Fallback UI Component ---
interface DefaultFallbackProps {
  error: Error | null;
  errorId: string;
  onRetry: () => void;
  onReload: () => void;
}

const DefaultErrorFallback: React.FC<DefaultFallbackProps> = ({ error, errorId, onRetry, onReload }) => {
  const theme = useTheme();
  // Use theme-aware styles
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <Card style={styles.errorCard}>
        <Card.Content>
          <View style={styles.iconContainer}>
            <PhosphorIcon name="warning-circle" size={48} color={theme.colors.error} />
          </View>
          <Text style={styles.errorTitle}>Oops! Something Went Wrong</Text>
          <Text style={styles.errorMessage}>
            An unexpected error occurred. Please try again. If the problem persists, you can reload the application.
          </Text>

          {__DEV__ && error && (
            <ScrollView style={styles.debugInfo} contentContainerStyle={{ padding: 12 }}>
              <Text style={styles.debugTitle}>Error Details (Development Only)</Text>
              <Text style={styles.debugText} selectable>{error.toString()}</Text>
            </ScrollView>
          )}

          <View style={styles.buttonContainer}>
            <Button mode='contained' onPress={onRetry} style={styles.button}>Try Again</Button>
            <Button mode='outlined' onPress={onReload} style={styles.button}>Reload App</Button>
          </View>
          <Text style={styles.errorId}>Error ID: {errorId}</Text>
        </Card.Content>
      </Card>
    </View>
  );
};

/**
 * Catches JavaScript errors anywhere in its child component tree and displays a fallback UI.
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `err-${Date.now().toString(36)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // OPTIMIZATION 2: Centralized logging logic
    this.logError(error, errorInfo);
  }

  logError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real app, you would send this to a service like Sentry, Bugsnag, or Firebase Crashlytics
    if (__DEV__) {
      console.error('ErrorBoundary Caught:', {
        error: error.toString(),
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
      });
    }
  };

  handleRetry = () => {
    this.props.onRetry?.();
    this.setState({ hasError: false, error: null, errorId: '' });
  };

  handleReload = () => {
    if (this.props.onReload) {
      this.props.onReload();
    } else {
      // As a fallback, still attempt a retry
      this.handleRetry();
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }
      return (
        <DefaultErrorFallback
          error={this.state.error}
          errorId={this.state.errorId}
          onRetry={this.handleRetry}
          onReload={this.handleReload}
        />
      );
    }
    return this.props.children;
  }
}

/**
 * A Higher-Order Component (HOC) to easily wrap any component with an ErrorBoundary.
 */
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps: Omit<ErrorBoundaryProps, 'children'> = {}
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

/**
 * A hook to imperatively trigger the nearest ErrorBoundary from within a functional component.
 * Useful for handling errors in event handlers, async functions, etc.
 * @example const { throwError } = useErrorHandler();
 * try { await myAsyncFunc(); } catch (e) { throwError(e); }
 */
export const useErrorHandler = () => {
  const [error, setError] = useState<Error | null>(null);
  if (error) {
    throw error;
  }
  return { throwError: setError };
};

// --- STYLES ---
const createStyles = (theme: ReturnType<typeof useTheme>) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: theme.colors.surfaceVariant,
  },
  errorCard: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  errorTitle: {
    textAlign: 'center',
    marginBottom: 12,
    color: theme.colors.error,
    fontSize: 20,
    fontWeight: 'bold',
  },
  errorMessage: {
    textAlign: 'center',
    marginBottom: 20,
    color: theme.colors.onSurfaceVariant,
    lineHeight: 22,
  },
  debugInfo: {
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 8,
    marginBottom: 20,
    maxHeight: 150,
  },
  debugTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    color: theme.colors.onSurface,
  },
  debugText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  button: {
    minWidth: 120,
  },
  errorId: {
    textAlign: 'center',
    color: theme.colors.onSurfaceVariant,
    fontSize: 10,
    marginTop: 20,
  },
});

export default ErrorBoundary;