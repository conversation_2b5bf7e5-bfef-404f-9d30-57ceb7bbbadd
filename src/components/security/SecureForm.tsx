import React, { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { useSecurity } from '../../hooks/useSecurity';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import TextInput from '../ui/TextInput';

export interface SecureFieldConfig {
  name: string;
  label: string;
  type: 'email' | 'phone' | 'text' | 'number';
  required?: boolean;
  placeholder?: string;
  multiline?: boolean;
  minLength?: number;
  maxLength?: number;
  customValidator?: (value: string) => string | null;
  secureStorage?: boolean; // Whether to store this field securely
}

export interface SecureFormData {
  [fieldName: string]: string;
}

export interface SecureFormErrors {
  [fieldName: string]: string[];
}

interface SecureFormProps {
  fields: SecureFieldConfig[];
  initialData?: SecureFormData;
  onDataChange?: (data: SecureFormData, isValid: boolean) => void;
  onSecurityEvent?: (event: string, details: Record<string, any>) => void;
  enableRealTimeValidation?: boolean;
  enableSecurityLogging?: boolean;
  style?: any;
}

/**
 * SecureForm - A form component with built-in security features
 * 
 * Features:
 * - Automatic input validation and sanitization
 * - XSS and SQL injection protection
 * - Security event logging
 * - Real-time security monitoring
 * - Secure data storage for sensitive fields
 */
export const SecureForm: React.FC<SecureFormProps> = ({
  fields,
  initialData = {},
  onDataChange,
  onSecurityEvent,
  enableRealTimeValidation = true,
  enableSecurityLogging = true,
  style
}) => {
  const theme = useTheme();
  const { 
    validateSecureInput, 
    storeSecurely, 
    logSecurityEvent,
    isSecurityReady 
  } = useSecurity({ 
    enableRealTimeValidation, 
    enableAuditLogging: enableSecurityLogging 
  });

  const [formData, setFormData] = useState<SecureFormData>(initialData);
  const [errors, setErrors] = useState<SecureFormErrors>({});
  const [isValidating, setIsValidating] = useState<Record<string, boolean>>({});
  const [securityWarnings, setSecurityWarnings] = useState<Record<string, string[]>>({});

  // Initialize form data from secure storage for sensitive fields
  useEffect(() => {
    const loadSecureData = async () => {
      if (!isSecurityReady) {return;}

      for (const field of fields) {
        if (field.secureStorage && !initialData[field.name]) {
          try {
            // Note: In a real implementation, you'd retrieve from secure storage
            // For now, we'll just log the intent
            logSecurityEvent('SECURE_FIELD_INIT', {
              fieldName: field.name,
              fieldType: field.type
            });
          } catch (error) {
            LoggingService.error(`Failed to load secure data for field: ${field.name}`, 'SECURE_FORM', error as Error);
          }
        }
      }
    };

    loadSecureData();
  }, [fields, initialData, isSecurityReady, logSecurityEvent]);

  // Validate a single field with security checks
  const validateField = useCallback(async (fieldName: string, value: string) => {
    const field = fields.find(f => f.name === fieldName);
    if (!field) {return;}

    setIsValidating(prev => ({ ...prev, [fieldName]: true }));

    try {
      const validationResult = await validateSecureInput(value, field.type);
      const fieldErrors: string[] = [];
      const fieldWarnings: string[] = [];

      // Basic validation errors
      if (!validationResult.isValid) {
        fieldErrors.push(...validationResult.errors);
      }

      // Security warnings (when input was sanitized but still usable)
      if (validationResult.sanitizedValue !== value) {
        fieldWarnings.push('Input was automatically sanitized for security');
        
        // Update form data with sanitized value
        setFormData(prev => ({ ...prev, [fieldName]: validationResult.sanitizedValue }));
        
        if (onSecurityEvent) {
          onSecurityEvent('INPUT_SANITIZED', {
            fieldName,
            fieldType: field.type,
            originalLength: value.length,
            sanitizedLength: validationResult.sanitizedValue.length
          });
        }
      }

      // Required field validation
      if (field.required && !validationResult.sanitizedValue.trim()) {
        fieldErrors.push(`${field.label} is required`);
      }

      // Length validations
      if (field.minLength && validationResult.sanitizedValue.length < field.minLength) {
        fieldErrors.push(`${field.label} must be at least ${field.minLength} characters`);
      }

      if (field.maxLength && validationResult.sanitizedValue.length > field.maxLength) {
        fieldErrors.push(`${field.label} must not exceed ${field.maxLength} characters`);
      }

      // Custom validation
      if (field.customValidator) {
        const customError = field.customValidator(validationResult.sanitizedValue);
        if (customError) {
          fieldErrors.push(customError);
        }
      }

      // Update errors and warnings
      setErrors(prev => ({ ...prev, [fieldName]: fieldErrors }));
      setSecurityWarnings(prev => ({ ...prev, [fieldName]: fieldWarnings }));

      // Store securely if configured
      if (field.secureStorage && validationResult.sanitizedValue && fieldErrors.length === 0) {
        const stored = await storeSecurely(`secure_form_${fieldName}`, validationResult.sanitizedValue);
        if (stored && enableSecurityLogging) {
          logSecurityEvent('SECURE_FIELD_STORED', {
            fieldName,
            fieldType: field.type
          });
        }
      }

    } catch (error) {
      LoggingService.error(`Field validation failed for ${fieldName}`, 'SECURE_FORM', error as Error);
      setErrors(prev => ({ 
        ...prev, 
        [fieldName]: ['Validation failed - please try again'] 
      }));
    } finally {
      setIsValidating(prev => ({ ...prev, [fieldName]: false }));
    }
  }, [fields, validateSecureInput, storeSecurely, logSecurityEvent, enableSecurityLogging, onSecurityEvent]);

  // Handle field value changes
  const handleFieldChange = useCallback(async (fieldName: string, value: string) => {
    // Update form data immediately for UI responsiveness
    setFormData(prev => ({ ...prev, [fieldName]: value }));

    // Clear previous errors
    setErrors(prev => ({ ...prev, [fieldName]: [] }));
    setSecurityWarnings(prev => ({ ...prev, [fieldName]: [] }));

    // Validate if real-time validation is enabled
    if (enableRealTimeValidation && value.length > 0) {
      await validateField(fieldName, value);
    }

    // Notify parent component
    if (onDataChange) {
      const allErrors = Object.values(errors).flat();
      const isFormValid = allErrors.length === 0 && 
        fields.filter(f => f.required).every(f => formData[f.name]?.trim());
      
      onDataChange({ ...formData, [fieldName]: value }, isFormValid);
    }
  }, [enableRealTimeValidation, validateField, onDataChange, formData, errors, fields]);

  // Validate all fields (for form submission)
  const validateAllFields = useCallback(async (): Promise<boolean> => {
    const validationPromises = fields.map(field => 
      validateField(field.name, formData[field.name] || '')
    );

    await Promise.all(validationPromises);

    // Check if there are any errors after validation
    const hasErrors = Object.values(errors).some(fieldErrors => fieldErrors.length > 0);
    return !hasErrors;
  }, [fields, formData, validateField, errors]);

  // Render individual field with security indicators
  const renderField = (field: SecureFieldConfig) => {
    const fieldErrors = errors[field.name] || [];
    const fieldWarnings = securityWarnings[field.name] || [];
    const isFieldValidating = isValidating[field.name] || false;

    return (
      <View key={field.name} style={styles.fieldContainer}>
        <TextInput
          label={field.label}
          value={formData[field.name] || ''}
          onChangeText={(value) => handleFieldChange(field.name, value)}
          type={field.type as any}
          required={field.required}
          placeholder={field.placeholder}
          multiline={field.multiline}
          minLength={field.minLength}
          error={fieldErrors.length > 0 ? fieldErrors[0] : undefined}
          style={styles.textInput}
          rightIcon={isFieldValidating ? 'refresh' as any : field.secureStorage ? 'lock' : undefined}
        />
        
        {/* Security warnings */}
        {fieldWarnings.map((warning, index) => (
          <Text key={index} style={[styles.warningText, { color: '#FF9800' }]}>
            ⚠️ {warning}
          </Text>
        ))}
        
        {/* Field errors */}
        {fieldErrors.slice(1).map((error, index) => (
          <Text key={index} style={[styles.errorText, { color: theme.colors.error }]}>
            {error}
          </Text>
        ))}
        
        {/* Security indicator for secure storage fields */}
        {field.secureStorage && (
          <Text style={[styles.securityIndicator, { color: theme.colors.primary }]}>
            🔒 This field is stored securely
          </Text>
        )}
      </View>
    );
  };

  if (!isSecurityReady) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }, style]}>
        <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
          Initializing secure form...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {fields.map(renderField)}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: SPACING.xl,
  },
  fieldContainer: {
    marginBottom: SPACING.lg,
  },
  textInput: {
    marginBottom: SPACING.xs,
  },
  errorText: {
    fontSize: 12,
    marginTop: SPACING.xs,
    marginLeft: SPACING.sm,
  },
  warningText: {
    fontSize: 12,
    marginTop: SPACING.xs,
    marginLeft: SPACING.sm,
  },
  securityIndicator: {
    fontSize: 11,
    marginTop: SPACING.xs,
    marginLeft: SPACING.sm,
    fontStyle: 'italic',
  },
});

export default SecureForm;