import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Text, Card, Chip, ProgressBar } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { useSecurity } from '../../hooks/useSecurity';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface SecurityEvent {
  id: string;
  type: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  details?: Record<string, any>;
}

interface SecurityMetrics {
  totalEvents: number;
  highSeverityEvents: number;
  criticalEvents: number;
  lastAuditDate: Date | null;
  encryptionStatus: 'active' | 'degraded' | 'inactive';
  secureStorageStatus: 'active' | 'degraded' | 'inactive';
}

interface SecurityMonitorProps {
  onSecurityEvent?: (event: SecurityEvent) => void;
  autoRefreshInterval?: number; // in milliseconds
  maxEventsToShow?: number;
}

export const SecurityMonitor: React.FC<SecurityMonitorProps> = ({
  onSecurityEvent,
  autoRefreshInterval = 30000, // 30 seconds
  maxEventsToShow = 50
}) => {
  const theme = useTheme();
  const { 
    performSecurityAudit, 
    securityScore, 
    securityIssues, 
    logSecurityEvent,
    isSecurityReady 
  } = useSecurity({ enableAuditLogging: true });

  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0,
    highSeverityEvents: 0,
    criticalEvents: 0,
    lastAuditDate: null,
    encryptionStatus: 'active',
    secureStorageStatus: 'active'
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedEventType, setSelectedEventType] = useState<string | null>(null);

  // Generate mock security events for demonstration
  const generateMockEvents = useCallback((): SecurityEvent[] => {
    const eventTypes = [
      { type: 'LOGIN_SUCCESS', severity: 'low' as const, message: 'User login successful' },
      { type: 'LOGIN_FAILED', severity: 'medium' as const, message: 'Failed login attempt' },
      { type: 'DATA_ENCRYPTED', severity: 'low' as const, message: 'Sensitive data encrypted' },
      { type: 'INPUT_SANITIZED', severity: 'medium' as const, message: 'Malicious input sanitized' },
      { type: 'SQL_INJECTION_ATTEMPT', severity: 'high' as const, message: 'SQL injection attempt blocked' },
      { type: 'SESSION_EXPIRED', severity: 'medium' as const, message: 'User session expired' },
      { type: 'ENCRYPTION_KEY_ROTATED', severity: 'low' as const, message: 'Encryption key rotated' },
      { type: 'SECURITY_AUDIT', severity: 'low' as const, message: 'Security audit completed' }
    ];

    return Array.from({ length: 20 }, (_, index) => {
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      return {
        id: `event_${Date.now()}_${index}`,
        type: eventType.type,
        message: eventType.message,
        severity: eventType.severity,
        timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Last 7 days
        details: {
          source: 'SecurityMonitor',
          userId: `user_${Math.floor(Math.random() * 1000)}`,
          sessionId: `session_${Math.floor(Math.random() * 10000)}`
        }
      };
    }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }, []);

  // Initialize events and metrics
  useEffect(() => {
    const mockEvents = generateMockEvents();
    setEvents(mockEvents);
    
    // Calculate metrics
    const highSeverityCount = mockEvents.filter(e => e.severity === 'high').length;
    const criticalCount = mockEvents.filter(e => e.severity === 'critical').length;
    
    setMetrics({
      totalEvents: mockEvents.length,
      highSeverityEvents: highSeverityCount,
      criticalEvents: criticalCount,
      lastAuditDate: new Date(),
      encryptionStatus: 'active',
      secureStorageStatus: 'active'
    });
  }, [generateMockEvents]);

  // Auto-refresh security data
  useEffect(() => {
    if (!autoRefreshInterval || !isSecurityReady) {return;}

    const interval = setInterval(async () => {
      await refreshSecurityData();
    }, autoRefreshInterval);

    return () => clearInterval(interval);
  }, [autoRefreshInterval, isSecurityReady]);

  const refreshSecurityData = useCallback(async () => {
    if (!isSecurityReady) {return;}

    setIsRefreshing(true);
    try {
      // Perform security audit
      const auditResult = await performSecurityAudit();
      
      // Log the audit event
      logSecurityEvent('SECURITY_MONITOR_REFRESH', {
        securityScore: auditResult.score,
        issuesCount: auditResult.issues.length
      });

      // Add audit event to events list
      const auditEvent: SecurityEvent = {
        id: `audit_${Date.now()}`,
        type: 'SECURITY_AUDIT',
        message: `Security audit completed - Score: ${auditResult.score}/100`,
        severity: auditResult.score < 70 ? 'high' : auditResult.score < 85 ? 'medium' : 'low',
        timestamp: new Date(),
        details: {
          score: auditResult.score,
          issues: auditResult.issues,
          recommendations: auditResult.recommendations
        }
      };

      setEvents(prev => [auditEvent, ...prev.slice(0, maxEventsToShow - 1)]);
      setMetrics(prev => ({
        ...prev,
        lastAuditDate: new Date(),
        totalEvents: prev.totalEvents + 1
      }));

      if (onSecurityEvent) {
        onSecurityEvent(auditEvent);
      }

    } catch (error) {
      LoggingService.error('Security data refresh failed', 'SECURITY_MONITOR', error as Error);
    } finally {
      setIsRefreshing(false);
    }
  }, [isSecurityReady, performSecurityAudit, logSecurityEvent, onSecurityEvent, maxEventsToShow]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return theme.colors.error;
      case 'high': return '#FF9800';
      case 'medium': return '#FFC107';
      case 'low': return theme.colors.success;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return 'warning';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'check-circle';
      default: return 'info';
    }
  };

  const getSecurityScoreColor = (score: number) => {
    if (score >= 90) {return theme.colors.success;}
    if (score >= 70) {return '#FFC107';}
    if (score >= 50) {return '#FF9800';}
    return theme.colors.error;
  };

  const handleEventPress = (event: SecurityEvent) => {
    Alert.alert(
      `Security Event: ${event.type}`,
      `${event.message}\n\nTimestamp: ${event.timestamp.toLocaleString()}\n\nDetails: ${JSON.stringify(event.details || {}, null, 2)}`,
      [{ text: 'OK' }]
    );
  };

  const filteredEvents = selectedEventType 
    ? events.filter(event => event.type === selectedEventType)
    : events;

  const eventTypes = Array.from(new Set(events.map(event => event.type)));

  if (!isSecurityReady) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
          Initializing security monitoring...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Security Score Card */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <View style={styles.scoreHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
              Security Score
            </Text>
            <TouchableOpacity 
              onPress={refreshSecurityData}
              disabled={isRefreshing}
              style={styles.refreshButton}
            >
              <PhosphorIcon 
                name={'refresh' as any} 
                size={20} 
                color={theme.colors.primary} 
              />
            </TouchableOpacity>
          </View>
          
          <View style={styles.scoreContainer}>
            <Text style={[styles.scoreValue, { color: getSecurityScoreColor(securityScore) }]}>
              {securityScore}/100
            </Text>
            <ProgressBar 
              progress={securityScore / 100} 
              color={getSecurityScoreColor(securityScore)}
              style={styles.progressBar}
            />
          </View>

          {securityIssues.length > 0 && (
            <View style={styles.issuesContainer}>
              <Text style={[styles.issuesTitle, { color: theme.colors.error }]}>
                Security Issues:
              </Text>
              {securityIssues.map((issue, index) => (
                <Text key={index} style={[styles.issueText, { color: theme.colors.onSurfaceVariant }]}>
                  • {issue}
                </Text>
              ))}
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Metrics Cards */}
      <View style={styles.metricsRow}>
        <Card style={[styles.metricCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content style={styles.metricContent}>
            <Text style={[styles.metricValue, { color: theme.colors.onSurface }]}>
              {metrics.totalEvents}
            </Text>
            <Text style={[styles.metricLabel, { color: theme.colors.onSurfaceVariant }]}>
              Total Events
            </Text>
          </Card.Content>
        </Card>

        <Card style={[styles.metricCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content style={styles.metricContent}>
            <Text style={[styles.metricValue, { color: '#FF9800' }]}>
              {metrics.highSeverityEvents}
            </Text>
            <Text style={[styles.metricLabel, { color: theme.colors.onSurfaceVariant }]}>
              High Severity
            </Text>
          </Card.Content>
        </Card>

        <Card style={[styles.metricCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content style={styles.metricContent}>
            <Text style={[styles.metricValue, { color: theme.colors.error }]}>
              {metrics.criticalEvents}
            </Text>
            <Text style={[styles.metricLabel, { color: theme.colors.onSurfaceVariant }]}>
              Critical
            </Text>
          </Card.Content>
        </Card>
      </View>

      {/* Event Type Filters */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
            Event Filters
          </Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.filterScroll}
          >
            <Chip
              selected={selectedEventType === null}
              onPress={() => setSelectedEventType(null)}
              style={styles.filterChip}
            >
              All Events
            </Chip>
            {eventTypes.map(type => (
              <Chip
                key={type}
                selected={selectedEventType === type}
                onPress={() => setSelectedEventType(type)}
                style={styles.filterChip}
              >
                {type.replace(/_/g, ' ')}
              </Chip>
            ))}
          </ScrollView>
        </Card.Content>
      </Card>

      {/* Recent Events */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
            Recent Security Events ({filteredEvents.length})
          </Text>
          
          {filteredEvents.length === 0 ? (
            <Text style={[styles.noEventsText, { color: theme.colors.onSurfaceVariant }]}>
              No security events found
            </Text>
          ) : (
            filteredEvents.slice(0, maxEventsToShow).map((event) => (
              <TouchableOpacity
                key={event.id}
                style={[styles.eventItem, { borderLeftColor: getSeverityColor(event.severity) }]}
                onPress={() => handleEventPress(event)}
              >
                <View style={styles.eventHeader}>
                  <View style={styles.eventTitleRow}>
                    <PhosphorIcon 
                      name={getSeverityIcon(event.severity)} 
                      size={16} 
                      color={getSeverityColor(event.severity)} 
                    />
                    <Text style={[styles.eventType, { color: theme.colors.onSurface }]}>
                      {event.type.replace(/_/g, ' ')}
                    </Text>
                  </View>
                  <Text style={[styles.eventTime, { color: theme.colors.onSurfaceVariant }]}>
                    {event.timestamp.toLocaleTimeString()}
                  </Text>
                </View>
                <Text style={[styles.eventMessage, { color: theme.colors.onSurfaceVariant }]}>
                  {event.message}
                </Text>
              </TouchableOpacity>
            ))
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: SPACING.xl,
  },
  card: {
    marginBottom: SPACING.md,
    borderRadius: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: SPACING.sm,
  },
  scoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  refreshButton: {
    padding: SPACING.xs,
  },
  scoreContainer: {
    alignItems: 'center',
    marginVertical: SPACING.md,
  },
  scoreValue: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: SPACING.sm,
  },
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
  },
  issuesContainer: {
    marginTop: SPACING.md,
  },
  issuesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: SPACING.xs,
  },
  issueText: {
    fontSize: 14,
    marginBottom: SPACING.xs,
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  metricCard: {
    flex: 1,
    marginHorizontal: SPACING.xs,
    borderRadius: 8,
  },
  metricContent: {
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  filterScroll: {
    marginVertical: SPACING.sm,
  },
  filterChip: {
    marginRight: SPACING.sm,
  },
  eventItem: {
    padding: SPACING.md,
    borderLeftWidth: 4,
    marginBottom: SPACING.sm,
    borderRadius: 8,
    backgroundColor: 'rgba(128, 128, 128, 0.05)',
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  eventTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventType: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: SPACING.xs,
    textTransform: 'capitalize',
  },
  eventTime: {
    fontSize: 12,
  },
  eventMessage: {
    fontSize: 14,
  },
  noEventsText: {
    textAlign: 'center',
    fontSize: 16,
    marginVertical: SPACING.lg,
  },
});

export default SecurityMonitor;