import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text, useTheme } from 'react-native-paper';

import { SPACING } from '@/theme/theme';
import { PhosphorIcon, PhosphorIconName } from '@/utils/phosphorIconRegistry';

interface MetricCardProps {
  title: string;
  value: string;
  subtitle?: string;
  growth?: string;
  icon: PhosphorIconName;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, subtitle, growth, icon }) => {
  const theme = useTheme();
  const isGrowthNegative = growth?.startsWith('-');
  const growthColor = isGrowthNegative ? theme.colors.error : theme.colors.primary;

  return (
    <View style={styles.metricCardContainer}>
      <Surface style={[styles.metricCardContent, { backgroundColor: theme.colors.surface }]} elevation={0}>
        <PhosphorIcon name={icon} size={24} color={theme.colors.primary} style={styles.icon} />
        <Text variant='headlineSmall' style={[styles.valueText, { color: theme.colors.onSurface }]}>
          {value}
        </Text>
        <Text variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant }}>
          {title}
        </Text>
        {subtitle && (
          <Text variant='bodySmall' style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
            {subtitle}
          </Text>
        )}
        {growth !== undefined && (
          <View style={styles.growthContainer}>
            <PhosphorIcon
              name={isGrowthNegative ? 'trend-down' : 'trend-up'}
              size={16}
              color={growthColor}
            />
            <Text
              variant='bodySmall'
              style={[styles.growthText, { color: growthColor }]}
            >
              {growth}
            </Text>
          </View>
        )}
      </Surface>
    </View>
  );
};

const styles = StyleSheet.create({
  metricCardContainer: { 
    width: '50%', 
    padding: SPACING.xs 
  },
  metricCardContent: { 
    borderRadius: 16, 
    borderWidth: 1, 
    borderColor: 'rgba(0,0,0,0.05)', 
    padding: SPACING.md,
    // FIX: Removed `height: '100%'` to allow natural sizing
  },
  icon: {
    marginBottom: SPACING.sm,
  },
  valueText: { 
    fontWeight: '700',
  },
  growthContainer: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    marginTop: SPACING.xs 
  },
  growthText: { 
    marginLeft: SPACING.xs,
    fontWeight: '600',
  },
});

export default React.memo(MetricCard);