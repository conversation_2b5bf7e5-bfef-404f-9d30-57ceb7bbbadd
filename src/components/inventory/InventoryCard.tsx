import React, { useMemo, memo } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle, Image } from 'react-native';
import { Text, Card, Chip, IconButton } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { InventoryItem } from '../../types/inventory';
import { formatCurrency } from '../../utils/currency';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

import StockLevelIndicator from './StockLevelIndicator';

// --- MAIN PROPS INTERFACE (No changes) ---
interface InventoryCardProps {
  item: InventoryItem;
  stockLevel?: number;
  stockUnit?: string;
  onPress?: () => void;
  showStockLevel?: boolean;
  showActions?: boolean;
  showImage?: boolean;
  showPrices?: boolean;
  compact?: boolean;
  style?: ViewStyle;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onStockPress?: () => void;
}

// --- SUB-COMPONENT: Compact View ---
const CompactInventoryCard = memo<InventoryCardProps>(({ item, stockLevel = 0, stockUnit, onPress, style, showStockLevel }) => {
  const theme = useTheme();
  const displayStockUnit = stockUnit || item.baseUnit;
  const formattedStockLevel = UnitConverter.formatQuantity(stockLevel, displayStockUnit, 2);
  const isOutOfStock = stockLevel <= 0;

  return (
    <TouchableOpacity style={[styles.compactContainer, style]} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.compactContent}>
        <View style={styles.compactInfo}>
          <Text style={[styles.compactTitle, { color: theme.colors.onSurface }]} numberOfLines={1}>{item.name}</Text>
          <Text style={[styles.compactCategory, { color: theme.colors.onSurfaceVariant }]} numberOfLines={1}>{item.category}</Text>
        </View>
        {showStockLevel && (
          <View style={styles.compactStock}>
            <Text style={[styles.compactStockText, { color: isOutOfStock ? theme.colors.error : theme.colors.primary }]}>
              {formattedStockLevel}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
});

// --- SUB-COMPONENT: Full View ---
const FullInventoryCard = memo<InventoryCardProps & { displayData: any }>(({
  item, stockLevel = 0, stockUnit, onPress, showStockLevel, showActions, showImage, showPrices, style,
  onEditPress, onDeletePress, onStockPress, displayData
}) => {
  const theme = useTheme();
  const displayStockUnit = stockUnit || item.baseUnit;

  return (
    <Card style={[styles.container, style]} mode='outlined'>
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        <Card.Content style={styles.content}>
          <View style={styles.headerRow}>
            <View style={styles.titleContainer}>
              <Text style={[styles.title, { color: theme.colors.onSurface }]} numberOfLines={2}>{item.name}</Text>
              <Chip mode='outlined' compact style={[styles.categoryChip, { borderColor: displayData.categoryColor }]} textStyle={{ color: displayData.categoryColor, fontSize: 11 }}>
                {item.category}
              </Chip>
            </View>
            {showImage && (
              <View style={styles.imageContainer}>
                {item.imageUri ? (
                  <Image source={{ uri: item.imageUri }} style={styles.itemImage} resizeMode='cover' />
                ) : (
                  <View style={[styles.placeholderImage, { backgroundColor: theme.colors.surfaceVariant }]}>
                    <PhosphorIcon name='package' size={24} color={theme.colors.onSurfaceVariant} />
                  </View>
                )}
              </View>
            )}
          </View>
          
          {showStockLevel && (
            <View style={styles.stockContainer}>
              <StockLevelIndicator currentStock={stockLevel} minimumLevel={item.minimumStockLevel} unit={displayStockUnit} size='small' />
            </View>
          )}
          
          {showPrices && (
            <View style={styles.priceContainer}>
              <View style={styles.priceRow}>
                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>Purchase</Text>
                  <Text style={[styles.priceValue, { color: theme.colors.onSurface }]}>{displayData.purchasePrice}</Text>
                </View>
                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>Selling</Text>
                  <Text style={[styles.priceValue, { color: theme.colors.primary }]}>{displayData.sellingPrice}</Text>
                </View>
                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>Margin</Text>
                  <Text style={[styles.priceValue, { color: displayData.profitMargin > 0 ? theme.colors.primary : theme.colors.error }]}>
                    {displayData.profitMargin}%
                  </Text>
                </View>
              </View>
            </View>
          )}

          {showActions && (
            <View style={styles.actionsRow}>
              {onStockPress && <IconButton icon='package-variant' size={20} onPress={onStockPress} iconColor={theme.colors.primary} style={styles.actionButton} />}
              {onEditPress && <IconButton icon='pencil' size={20} onPress={onEditPress} iconColor={theme.colors.onSurfaceVariant} style={styles.actionButton} />}
              {onDeletePress && <IconButton icon='delete' size={20} onPress={onDeletePress} iconColor={theme.colors.error} style={styles.actionButton} />}
            </View>
          )}
          
          {!item.isActive && (
            <View style={styles.inactiveOverlay}>
              <Text style={[styles.inactiveText, { color: theme.colors.error }]}>Inactive</Text>
            </View>
          )}
        </Card.Content>
      </TouchableOpacity>
    </Card>
  );
});

// --- MAIN CONTROLLER COMPONENT ---
const InventoryCard: React.FC<InventoryCardProps> = (props) => {
  const { item, compact } = props;
  const theme = useTheme();

  // Centralized hook to compute all derived data once
  const displayData = useMemo(() => {
    const { purchasePrice, sellingPrice, category } = item;
    
    // Hashing function for category color
    const getCategoryColor = (cat: string): string => {
      const colors = [ theme.colors.primary, theme.colors.secondary, theme.colors.tertiary, '#F59E0B', '#10B981', '#8B5CF6', '#EF4444', '#06B6D4' ];
      let hash = 0;
      for (let i = 0; i < (cat || '').length; i++) {
        hash = cat.charCodeAt(i) + ((hash << 5) - hash);
      }
      return colors[Math.abs(hash) % colors.length];
    };
    
    return {
      purchasePrice: formatCurrency(purchasePrice),
      sellingPrice: formatCurrency(sellingPrice),
      profitMargin: sellingPrice > 0 ? (((sellingPrice - purchasePrice) / sellingPrice) * 100).toFixed(1) : '0',
      categoryColor: getCategoryColor(category),
    };
  }, [item, theme.colors]);
  
  if (!item) {return null;}

  if (compact) {
    return <CompactInventoryCard {...props} />;
  }

  return <FullInventoryCard {...props} displayData={displayData} />;
};

// --- STYLES (No changes needed) ---
const styles = StyleSheet.create({
  container: { marginVertical: 4, marginHorizontal: 8 },
  content: { paddingVertical: 12 },
  headerRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 },
  titleContainer: { flex: 1, marginRight: 12 },
  title: { fontSize: 16, fontWeight: 'bold', marginBottom: 4 },
  categoryChip: { alignSelf: 'flex-start' },
  imageContainer: { width: 60, height: 60 },
  itemImage: { width: 60, height: 60, borderRadius: 8 },
  placeholderImage: { width: 60, height: 60, borderRadius: 8, justifyContent: 'center', alignItems: 'center' },
  stockContainer: { marginBottom: 12 },
  priceContainer: { marginBottom: 12 },
  priceRow: { flexDirection: 'row', justifyContent: 'space-between' },
  priceItem: { flex: 1, alignItems: 'center' },
  priceLabel: { fontSize: 11, marginBottom: 2 },
  priceValue: { fontSize: 13, fontWeight: '600' },
  actionsRow: { flexDirection: 'row', justifyContent: 'flex-end', marginTop: 8, borderTopWidth: 1, borderTopColor: '#eee', paddingTop: 4 },
  actionButton: { margin: 0 },
  inactiveOverlay: { position: 'absolute', top: 8, right: 8, backgroundColor: 'rgba(239, 68, 68, 0.1)', paddingHorizontal: 8, paddingVertical: 4, borderRadius: 4 },
  inactiveText: { fontSize: 10, fontWeight: '600' },
  compactContainer: { paddingVertical: 12, paddingHorizontal: 16, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,0.1)' },
  compactContent: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  compactInfo: { flex: 1 },
  compactTitle: { fontSize: 14, fontWeight: '600', marginBottom: 2 },
  compactCategory: { fontSize: 12 },
  compactStock: { alignItems: 'flex-end' },
  compactStockText: { fontSize: 12, fontWeight: '600' },
});

export default memo(InventoryCard); // Memoize the main component as well