import React, { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { TextInput, Text, Menu, Button, HelperText } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

interface UnitQuantityInputProps {
  value: number;
  unit: string;
  availableUnits: string[];
  onQuantityChange: (quantity: number) => void;
  onUnitChange: (unit: string) => void;
  showConversion?: boolean;
  baseUnit?: string;
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  style?: ViewStyle;
  precision?: number;
  minValue?: number;
  maxValue?: number;
}

// Helper functions moved outside the component to prevent re-creation on every render
const getUnitDisplayName = (unitName: string): string => UnitConverter.getUnitDisplayName(unitName);
const getUnitAbbreviation = (unitName: string): string => UnitConverter.getUnitAbbreviation(unitName);

const UnitQuantityInput: React.FC<UnitQuantityInputProps> = ({
  value,
  unit,
  availableUnits,
  onQuantityChange,
  onUnitChange,
  showConversion = true,
  baseUnit = 'meter',
  label = 'Quantity',
  placeholder = 'Enter quantity',
  error: propError,
  disabled = false,
  style,
  precision = 2,
  minValue = 0,
  maxValue,
}) => {
  const theme = useTheme();
  const [inputValue, setInputValue] = useState<string>(value.toString());
  const [showUnitMenu, setShowUnitMenu] = useState<boolean>(false);
  const [conversionText, setConversionText] = useState<string>('');
  const [validationError, setValidationError] = useState<string | null>(null);

  // Update input when the parent's `value` prop changes
  useEffect(() => {
    // Only update if not currently focused, to avoid disrupting user typing
    if (String(value) !== inputValue) {
        setInputValue(value.toFixed(precision));
    }
  }, [value]);

  // Debounce the input for validation and parent callback
  useEffect(() => {
    const handler = setTimeout(() => {
      const numericValue = parseFloat(inputValue);

      if (inputValue === '' || isNaN(numericValue)) {
        setValidationError('Please enter a valid number');
        return;
      }
      if (numericValue < minValue) {
        setValidationError(`Value must be at least ${minValue}`);
        return;
      }
      if (maxValue !== undefined && numericValue > maxValue) {
        setValidationError(`Value must not exceed ${maxValue}`);
        return;
      }

      // If valid, clear error and notify parent
      setValidationError(null);
      if (numericValue !== value) {
          onQuantityChange(numericValue);
      }
    }, 300); // 300ms delay

    return () => {
      clearTimeout(handler);
    };
  }, [inputValue, minValue, maxValue, onQuantityChange]);

  // Update conversion text when the final `value` or `unit` changes
  useEffect(() => {
    if (showConversion && value > 0 && unit !== baseUnit) {
      try {
        const convertedValue = UnitConverter.convertBetweenUnits(value, unit, baseUnit);
        const formatted = UnitConverter.formatQuantity(convertedValue, baseUnit, precision);
        setConversionText(`≈ ${formatted}`);
      } catch (e) {
        setConversionText('');
      }
    } else {
      setConversionText('');
    }
  }, [value, unit, baseUnit, showConversion, precision]);

  const handleUnitSelect = useCallback(
    (selectedUnit: string) => {
      setShowUnitMenu(false);
      if (selectedUnit !== unit) {
        onUnitChange(selectedUnit);
        // Let parent handle the value conversion if needed, or do it here
        const convertedValue = UnitConverter.convertBetweenUnits(value, unit, selectedUnit);
        onQuantityChange(convertedValue);
      }
    },
    [unit, value, onUnitChange, onQuantityChange]
  );
  
  // Format the input to the correct precision when the user is done editing
  const handleBlur = () => {
    if (!validationError) {
      const numericValue = parseFloat(inputValue);
      if (!isNaN(numericValue)) {
        setInputValue(numericValue.toFixed(precision));
      }
    }
  };

  const displayError = propError || validationError;

  return (
    <View style={[styles.container, style]}>
      <View style={styles.inputRow}>
        <TextInput
          label={label}
          value={inputValue}
          onChangeText={setInputValue} // Directly set the input string
          onBlur={handleBlur} // Format on blur
          placeholder={placeholder}
          keyboardType="numeric"
          disabled={disabled}
          error={!!displayError}
          mode="outlined"
          style={styles.quantityInput}
        />
        <Menu
          visible={showUnitMenu}
          onDismiss={() => setShowUnitMenu(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setShowUnitMenu(true)}
              disabled={disabled || availableUnits.length <= 1}
              style={[styles.unitButton, { borderColor: displayError ? theme.colors.error : theme.colors.outline }]}
              contentStyle={styles.unitButtonContent}
              labelStyle={{ color: theme.colors.onSurface }}
              icon={() => <PhosphorIcon name="caret-down" size={16} color={theme.colors.onSurface} />}
            >
              {getUnitAbbreviation(unit)}
            </Button>
          }
        >
          {availableUnits.map(availableUnit => (
            <Menu.Item
              key={availableUnit}
              onPress={() => handleUnitSelect(availableUnit)}
              title={getUnitDisplayName(availableUnit)}
              style={availableUnit === unit ? { backgroundColor: `${theme.colors.primary}1A` } : {}}
            />
          ))}
        </Menu>
      </View>

      {showConversion && conversionText && !displayError && (
        <Text style={[styles.conversionText, { color: theme.colors.onSurfaceVariant }]}>
          {conversionText}
        </Text>
      )}

      {displayError && (
        <HelperText type="error" visible={!!displayError}>
          {displayError}
        </HelperText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { marginVertical: 8 },
  inputRow: { flexDirection: 'row', alignItems: 'flex-start', gap: 8 },
  quantityInput: { flex: 1, backgroundColor: 'transparent' },
  unitButton: { height: 56, justifyContent: 'center' },
  unitButtonContent: { height: 56, flexDirection: 'row-reverse' },
  conversionText: { fontSize: 12, fontStyle: 'italic', paddingHorizontal: 12, marginTop: 4 },
});

export default UnitQuantityInput;