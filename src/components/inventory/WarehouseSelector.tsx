import React, { useState, useCallback, useMemo, memo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, Menu, Button, HelperText } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { Warehouse } from '../../types/inventory';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- PROPS INTERFACE (Unchanged) ---
interface WarehouseSelectorProps {
  warehouses: Warehouse[];
  selectedWarehouseId: string | null;
  onWarehouseSelect: (warehouse: Warehouse) => void;
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  showDefault?: boolean;
  style?: ViewStyle;
  compact?: boolean;
}

// --- HELPER FUNCTIONS (Moved outside for performance) ---
const getDisplayText = (warehouse: Warehouse | undefined, placeholder: string, showDefault: boolean): string => {
  if (!warehouse) {return placeholder;}
  return showDefault && warehouse.isDefault ? `${warehouse.name} (Default)` : warehouse.name;
};
const getWarehouseIcon = (warehouse: Warehouse): PhosphorIconName => warehouse.isDefault ? 'house' : 'warehouse';


// --- MEMOIZED SUB-COMPONENTS ---

// A single item in the dropdown menu
const WarehouseMenuItem = memo(({ warehouse, isSelected, onSelect }: { warehouse: Warehouse, isSelected: boolean, onSelect: (w: Warehouse) => void }) => {
  const theme = useTheme();
  return (
    <Menu.Item
      key={warehouse.id}
      onPress={() => onSelect(warehouse)}
      title={warehouse.name}
      titleStyle={{
        color: isSelected ? theme.colors.primary : theme.colors.onSurface,
        fontWeight: isSelected ? 'bold' : 'normal',
      }}
      leadingIcon={() => <PhosphorIcon name={getWarehouseIcon(warehouse)} size={20} color={warehouse.isDefault ? theme.colors.primary : theme.colors.onSurfaceVariant} />}
      trailingIcon={warehouse.isDefault ? () => <View style={styles.defaultBadge}><Text style={[styles.defaultBadgeText, { color: theme.colors.primary }]}>DEFAULT</Text></View> : undefined}
    />
  );
});
WarehouseMenuItem.displayName = 'WarehouseMenuItem';


// The Full-sized selector component
const WarehouseSelectorFull = memo(({ label, required, error, disabled, selectedWarehouse, activeWarehouses, onWarehouseSelect, placeholder, showDefault }: any) => {
  const theme = useTheme();
  const [showMenu, setShowMenu] = useState(false);
  
  return (
    <View>
      {label && <Text style={[styles.label, { color: theme.colors.onSurface }]}>{label}{required && <Text style={{ color: theme.colors.error }}> *</Text>}</Text>}
      <Menu
        visible={showMenu}
        onDismiss={() => setShowMenu(false)}
        anchor={
          <Button
            mode='outlined'
            onPress={() => setShowMenu(true)}
            disabled={disabled || activeWarehouses.length === 0}
            style={[styles.selectorButton, { borderColor: error ? theme.colors.error : theme.colors.outline }]}
            contentStyle={styles.buttonContent}
            labelStyle={{ color: selectedWarehouse ? theme.colors.onSurface : theme.colors.onSurfaceVariant, fontSize: 16, textAlign: 'left' }}
            icon={() => <PhosphorIcon name='caret-down' size={20} color={theme.colors.onSurface} />}
          >
            {getDisplayText(selectedWarehouse, placeholder, showDefault)}
          </Button>
        }
      >
        {activeWarehouses.map((w: Warehouse) => <WarehouseMenuItem key={w.id} warehouse={w} isSelected={w.id === selectedWarehouse?.id} onSelect={onWarehouseSelect} />)}
      </Menu>
      {selectedWarehouse && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}><PhosphorIcon name='map-pin' size={14} color={theme.colors.onSurfaceVariant} /><Text style={[styles.detailText, { color: theme.colors.onSurfaceVariant }]}>{selectedWarehouse.location}</Text></View>
          {selectedWarehouse.isDefault && <View style={styles.detailRow}><PhosphorIcon name='star' size={14} color={theme.colors.primary} /><Text style={[styles.detailText, { color: theme.colors.primary }]}>Default warehouse</Text></View>}
        </View>
      )}
      {error && <HelperText type='error' visible={!!error}>{error}</HelperText>}
    </View>
  );
});
WarehouseSelectorFull.displayName = 'WarehouseSelectorFull';


// The Compact selector component
const WarehouseSelectorCompact = memo(({ error, disabled, selectedWarehouse, activeWarehouses, onWarehouseSelect, placeholder, showDefault }: any) => {
    const theme = useTheme();
    const [showMenu, setShowMenu] = useState(false);

    return (
        <View>
            <Menu
                visible={showMenu}
                onDismiss={() => setShowMenu(false)}
                anchor={
                    <Button
                        mode='outlined'
                        onPress={() => setShowMenu(true)}
                        disabled={disabled || activeWarehouses.length === 0}
                        style={[styles.compactButton, { borderColor: error ? theme.colors.error : theme.colors.outline }]}
                        contentStyle={styles.compactButtonContent}
                        labelStyle={{ color: selectedWarehouse ? theme.colors.onSurface : theme.colors.onSurfaceVariant, fontSize: 14 }}
                        icon={() => <PhosphorIcon name='caret-down' size={16} color={theme.colors.onSurface} />}
                    >
                        {getDisplayText(selectedWarehouse, placeholder, showDefault)}
                    </Button>
                }
            >
                {activeWarehouses.map((w: Warehouse) => <WarehouseMenuItem key={w.id} warehouse={w} isSelected={w.id === selectedWarehouse?.id} onSelect={onWarehouseSelect} />)}
            </Menu>
            {error && <HelperText type='error' visible={!!error}>{error}</HelperText>}
        </View>
    );
});
WarehouseSelectorCompact.displayName = 'WarehouseSelectorCompact';


// --- MAIN CONTROLLER COMPONENT ---
const WarehouseSelector: React.FC<WarehouseSelectorProps> = (props) => {
  const { warehouses, selectedWarehouseId, onWarehouseSelect, compact, style } = props;

  // Memoize the selected warehouse object to prevent re-finding it on every render
  const selectedWarehouse = useMemo(() =>
    warehouses.find(w => w.id === selectedWarehouseId),
    [warehouses, selectedWarehouseId]
  );

  // Memoize the filtered and sorted list of warehouses to prevent re-calculation
  const activeWarehouses = useMemo(() =>
    warehouses
      .filter(w => w.isActive)
      .sort((a, b) => {
        if (a.isDefault && !b.isDefault) {return -1;}
        if (!a.isDefault && b.isDefault) {return 1;}
        return a.name.localeCompare(b.name);
      }),
    [warehouses]
  );
  
  const handleSelect = useCallback((warehouse: Warehouse) => {
      onWarehouseSelect(warehouse);
  }, [onWarehouseSelect]);

  const sharedProps = { ...props, selectedWarehouse, activeWarehouses, onWarehouseSelect: handleSelect };

  return (
    <View style={[compact ? styles.compactContainer : styles.container, style]}>
      {compact
        ? <WarehouseSelectorCompact {...sharedProps} />
        : <WarehouseSelectorFull {...sharedProps} />
      }
    </View>
  );
};

// --- STYLES ---
const styles = StyleSheet.create({
  container: { marginVertical: 8 },
  label: { fontSize: 14, fontWeight: '500', marginBottom: 8 },
  selectorButton: { justifyContent: 'flex-start', minHeight: 56 },
  buttonContent: { height: 56, justifyContent: 'space-between', flexDirection: 'row-reverse', paddingHorizontal: 16 },
  detailsContainer: { marginTop: 8, paddingHorizontal: 12, gap: 4 },
  detailRow: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  detailText: { fontSize: 12 },
  defaultBadge: { paddingHorizontal: 6, paddingVertical: 2, borderRadius: 4, backgroundColor: 'rgba(0, 122, 255, 0.1)' },
  defaultBadgeText: { fontSize: 10, fontWeight: '600' },
  compactContainer: { marginVertical: 4 },
  compactButton: { minHeight: 40, justifyContent: 'center' },
  compactButtonContent: { height: 40, flexDirection: 'row-reverse' },
});

export default memo(WarehouseSelector);