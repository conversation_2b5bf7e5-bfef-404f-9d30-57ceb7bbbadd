import React, { memo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, Card, Divider } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { useConversion } from '../../hooks/useConversion'; // FIX 1: Corrected import path
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// --- PROPS INTERFACE (No changes) ---
interface ConversionPreviewProps {
  quantity: number;
  fromUnit: string;
  toUnit: string;
  showBothDirections?: boolean;
  precision?: number;
  style?: ViewStyle;
  compact?: boolean;
  showCalculation?: boolean;
}

// --- FIX 2: Define props for the sub-component ---
interface ConversionRowProps {
  quantity: string;
  unitName: string;
  result: string;
  resultUnitName: string;
  resultColor: string;
}

// --- REUSABLE SUB-COMPONENT ---
const ConversionRow: React.FC<ConversionRowProps> = memo(({ quantity, unitName, result, resultUnitName, resultColor }) => {
  const theme = useTheme();
  return (
    <View style={styles.conversionRow}>
      <View style={styles.conversionItem}>
        <Text style={[styles.quantityText, { color: theme.colors.onSurface }]}>{quantity}</Text>
        <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>{unitName}</Text>
      </View>
      <PhosphorIcon name='arrow-right' size={16} color={theme.colors.onSurfaceVariant} />
      <View style={styles.conversionItem}>
        <Text style={[styles.quantityText, { color: resultColor }]}>{result}</Text>
        <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>{resultUnitName}</Text>
      </View>
    </View>
  );
});
ConversionRow.displayName = 'ConversionRow'; // Good practice for memoized components

// --- MAIN COMPONENT ---
const ConversionPreview: React.FC<ConversionPreviewProps> = ({
  quantity,
  fromUnit,
  toUnit,
  showBothDirections = false,
  precision = 2,
  style,
  compact = false,
  showCalculation = false,
}) => {
  const theme = useTheme();
  const {
    forwardResult,
    backwardResult,
    formattedQuantity,
    conversionRate,
    fromUnitInfo,
    toUnitInfo,
    error,
  } = useConversion({ quantity, fromUnit, toUnit, precision });

  if (fromUnit === toUnit) {return null;}

  if (compact) {
    return (
      <View style={[styles.compactContainer, style]}>
        <Text style={[styles.compactText, { color: error ? theme.colors.error : theme.colors.onSurfaceVariant }]}>
          {error ? 'Conversion error' : `${formattedQuantity} → ${forwardResult}`}
        </Text>
      </View>
    );
  }

  // Centralized Error UI
  if (error) {
    return (
      <Card style={[styles.container, styles.errorCard, style]} mode='outlined'>
        <Card.Content style={styles.errorContainer}>
          <PhosphorIcon name='warning' size={18} color={theme.colors.error} />
          <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={[styles.container, style]} mode='outlined'>
      <Card.Content style={styles.content}>
        <View style={styles.header}>
          <PhosphorIcon name='arrows-left-right' size={20} color={theme.colors.primary} />
          <Text style={[styles.headerText, { color: theme.colors.primary }]}>Unit Conversion</Text>
        </View>
        
        <ConversionRow
          quantity={formattedQuantity!}
          unitName={fromUnitInfo!.name}
          result={forwardResult!}
          resultUnitName={toUnitInfo!.name}
          resultColor={theme.colors.primary}
        />

        {showCalculation && conversionRate && (
          <View style={styles.calculationContainer}>
            <Divider style={{ marginVertical: 8 }} />
            <Text style={[styles.calculationText, { color: theme.colors.onSurfaceVariant }]}>
              {`1 ${fromUnitInfo!.abbr} = ${conversionRate.toFixed(4)} ${toUnitInfo!.abbr}`}
            </Text>
          </View>
        )}

        {showBothDirections && (
          <>
            <Divider style={{ marginVertical: 12 }} />
            <ConversionRow
              quantity={`${quantity} ${toUnitInfo!.abbr}`}
              unitName={toUnitInfo!.name}
              result={backwardResult!}
              resultUnitName={fromUnitInfo!.name}
              resultColor={theme.colors.secondary}
            />
          </>
        )}
      </Card.Content>
    </Card>
  );
};

// --- STYLES (with minor additions for error card) ---
const styles = StyleSheet.create({
  container: { marginVertical: 8 },
  content: { paddingVertical: 12 },
  header: { flexDirection: 'row', alignItems: 'center', marginBottom: 12, gap: 8 },
  headerText: { fontSize: 14, fontWeight: '600' },
  conversionRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', marginVertical: 4 },
  conversionItem: { flex: 1, alignItems: 'center', paddingHorizontal: 8 },
  quantityText: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' },
  unitText: { fontSize: 12, marginTop: 2, textAlign: 'center' },
  calculationContainer: { marginTop: 8 },
  calculationText: { fontSize: 11, textAlign: 'center', marginVertical: 1 },
  compactContainer: { paddingVertical: 4 },
  compactText: { fontSize: 12, textAlign: 'center' },
  errorCard: { borderColor: 'rgba(255, 0, 0, 0.3)' },
  errorContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', gap: 8 },
  errorText: { fontSize: 13, fontWeight: '500' },
});

export default ConversionPreview;