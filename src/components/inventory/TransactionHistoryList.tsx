/**
 * Transaction History List Component
 * Displays a list of inventory transactions with filtering and details.
 * Optimized for performance using FlatList and modular components.
 */

import React, { useMemo, useCallback, memo } from 'react';
import { View, StyleSheet, ViewStyle, FlatList, TouchableOpacity } from 'react-native';
import { Text, Card, Divider } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { InventoryTransaction } from '../../types/inventory';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

// --- FIX 1: Derive AppTheme type directly from the useTheme hook ---
type AppTheme = ReturnType<typeof useTheme>;

// --- FIX 2: Create a local, extended type for transactions ---
interface TransactionWithDetails extends InventoryTransaction {
  item_name?: string;
  warehouse_name?: string;
}

// --- PROPS ---
interface TransactionHistoryListProps {
  transactions: InventoryTransaction[];
  showItemName?: boolean;
  showWarehouseName?: boolean;
  showPerformedBy?: boolean;
  compact?: boolean;
  maxItems?: number;
  style?: ViewStyle;
  emptyMessage?: string;
  onTransactionPress?: (transaction: InventoryTransaction) => void;
}

// --- HELPER FUNCTIONS (Moved outside for performance) ---
const getTransactionTypeInfo = (type: InventoryTransaction['type'], theme: AppTheme) => {
  switch (type) {
    case 'IN': return { icon: 'arrow-down' as PhosphorIconName, color: theme.colors.primary, label: 'Stock In' };
    case 'OUT': return { icon: 'arrow-up' as PhosphorIconName, color: theme.colors.error, label: 'Stock Out' };
    case 'TRANSFER': return { icon: 'arrows-left-right' as PhosphorIconName, color: theme.colors.tertiary, label: 'Transfer' };
    case 'ADJUSTMENT': return { icon: 'wrench' as PhosphorIconName, color: theme.colors.primary, label: 'Adjustment' };
    default: return { icon: 'question' as PhosphorIconName, color: theme.colors.onSurfaceVariant, label: 'Unknown' };
  }
};

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  let dateLabel = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  if (date.toDateString() === today.toDateString()) {dateLabel = 'Today';}
  else if (date.toDateString() === yesterday.toDateString()) {dateLabel = 'Yesterday';}

  const timeLabel = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  return { date: dateLabel, time: timeLabel };
};

// --- SUB-COMPONENT: Compact Item ---
interface CompactItemProps {
  transaction: TransactionWithDetails;
  onPress?: (transaction: InventoryTransaction) => void;
}
const TransactionItemCompact = memo(({ transaction, onPress }: CompactItemProps) => {
    const theme = useTheme();
    const typeInfo = getTransactionTypeInfo(transaction.type, theme);
    const { date, time } = formatDateTime(transaction.createdAt);
    const formattedQuantity = UnitConverter.formatQuantity(Math.abs(transaction.quantity), transaction.unit, 2);

    return (
        <TouchableOpacity onPress={() => onPress?.(transaction)} activeOpacity={0.7}>
            <View style={[styles.compactItem, { borderLeftColor: typeInfo.color, backgroundColor: theme.colors.surface }]}>
                <View style={styles.compactHeader}>
                  <View style={styles.compactTypeContainer}>
                      <PhosphorIcon name={typeInfo.icon} size={16} color={typeInfo.color} />
                      <Text style={[styles.compactTypeText, { color: typeInfo.color }]}>{typeInfo.label}</Text>
                  </View>
                  <Text style={[styles.compactQuantity, { color: transaction.quantity >= 0 ? theme.colors.primary : theme.colors.error }]}>
                      {transaction.quantity >= 0 ? '+' : '−'}{formattedQuantity}
                  </Text>
                </View>
                <View style={styles.compactDetails}>
                  <Text style={[styles.compactTime, { color: theme.colors.onSurfaceVariant }]}>{date} at {time}</Text>
                  {transaction.note && <Text style={[styles.compactNote, { color: theme.colors.onSurfaceVariant }]} numberOfLines={1}>{transaction.note}</Text>}
                </View>
            </View>
        </TouchableOpacity>
    );
});
TransactionItemCompact.displayName = 'TransactionItemCompact';

// --- SUB-COMPONENT: Full Item ---
interface FullItemProps extends Omit<TransactionHistoryListProps, 'transactions' | 'compact' | 'maxItems' | 'style' | 'emptyMessage'> {
  transaction: TransactionWithDetails;
}

const TransactionItemFull = memo(({ transaction, showItemName, showWarehouseName, showPerformedBy, onTransactionPress }: FullItemProps) => {
  const theme = useTheme();
  const typeInfo = getTransactionTypeInfo(transaction.type, theme);
  const { date, time } = formatDateTime(transaction.createdAt);
  const formattedQuantity = UnitConverter.formatQuantity(Math.abs(transaction.quantity), transaction.unit, 2);

  return (
    <Card style={styles.transactionCard} mode='outlined' onPress={() => onTransactionPress?.(transaction)}>
      <Card.Content style={styles.cardContent}>
        <View style={styles.headerRow}>
          <View style={[styles.typeIndicator, { backgroundColor: `${typeInfo.color}1A` }]}>
            <PhosphorIcon name={typeInfo.icon} size={20} color={typeInfo.color} />
            <Text style={[styles.typeLabel, { color: typeInfo.color }]}>{typeInfo.label}</Text>
          </View>
          <View style={styles.quantityContainer}>
            <Text style={[styles.quantityText, { color: transaction.quantity >= 0 ? theme.colors.primary : theme.colors.error }]}>
              {transaction.quantity >= 0 ? '+' : '−'}{formattedQuantity}
            </Text>
          </View>
        </View>

        <View style={styles.detailsRow}>
          <View style={styles.detailsLeft}>
            {showItemName && transaction.item_name && (
              <View style={styles.detailItem}><PhosphorIcon name='package' size={14} color={theme.colors.onSurfaceVariant} /><Text style={[styles.detailText, { color: theme.colors.onSurface }]}>{transaction.item_name}</Text></View>
            )}
            {showWarehouseName && transaction.warehouse_name && (
              <View style={styles.detailItem}><PhosphorIcon name='warehouse' size={14} color={theme.colors.onSurfaceVariant} /><Text style={[styles.detailText, { color: theme.colors.onSurface }]}>{transaction.warehouse_name}</Text></View>
            )}
          </View>
          <View style={styles.detailsRight}>
            <Text style={[styles.dateText, { color: theme.colors.onSurfaceVariant }]}>{date}</Text>
            <Text style={[styles.timeText, { color: theme.colors.onSurfaceVariant }]}>{time}</Text>
          </View>
        </View>

        {(transaction.reference || transaction.note) && (
          <View style={styles.additionalInfo}>
            <Divider style={{ marginVertical: 8 }} />
            {transaction.reference && <View style={styles.infoRow}><Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>Reference:</Text><Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>{transaction.reference}</Text></View>}
            {transaction.note && <View style={styles.infoRow}><Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>Note:</Text><Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>{transaction.note}</Text></View>}
          </View>
        )}

        {showPerformedBy && transaction.performedBy && (
          <View style={styles.performedByContainer}>
            <PhosphorIcon name='user' size={12} color={theme.colors.onSurfaceVariant} /><Text style={[styles.performedByText, { color: theme.colors.onSurfaceVariant }]}>by {transaction.performedBy}</Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );
});
TransactionItemFull.displayName = 'TransactionItemFull';

// --- MAIN COMPONENT ---
const TransactionHistoryList: React.FC<TransactionHistoryListProps> = ({
  transactions,
  compact = false,
  maxItems,
  style,
  emptyMessage = 'No transactions found',
  ...rest
}) => {
  const theme = useTheme();

  const displayTransactions = useMemo(() => {
    return maxItems ? transactions.slice(0, maxItems) : transactions;
  }, [transactions, maxItems]);

  const renderItem = useCallback(({ item }: { item: InventoryTransaction }) => {
    const transactionItem = item as TransactionWithDetails;
    if (compact) {
      return <TransactionItemCompact transaction={transactionItem} onPress={rest.onTransactionPress} />;
    }
    return <TransactionItemFull transaction={transactionItem} {...rest} />;
  }, [compact, rest]);

  const ListEmptyComponent = useMemo(() => (
    <View style={[styles.emptyContainer, style]}>
      <PhosphorIcon name='clock' size={48} color={theme.colors.onSurfaceVariant} /> 
      <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>{emptyMessage}</Text>
    </View>
  ), [style, emptyMessage, theme.colors]);

  const ListFooterComponent = useMemo(() => {
    if (maxItems && transactions.length > maxItems) {
      return (
        <View style={styles.moreIndicator}>
          <Text style={[styles.moreText, { color: theme.colors.primary }]}>
            +{transactions.length - maxItems} more transactions
          </Text>
        </View>
      );
    }
    return null;
  }, [transactions, maxItems, theme.colors]);

  if (transactions.length === 0) {
    return ListEmptyComponent;
  }

  return (
    <FlatList
      data={displayTransactions}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      style={[styles.container, style]}
      ItemSeparatorComponent={() => <View style={{ height: compact ? 2 : 8 }} />}
      ListFooterComponent={ListFooterComponent}
      scrollEnabled={false}
    />
  );
};

// --- STYLES ---
const styles = StyleSheet.create({
  container: {},
  transactionCard: { marginVertical: 2 },
  cardContent: { paddingVertical: 12 },
  headerRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  typeIndicator: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 16, gap: 6 },
  typeLabel: { fontSize: 12, fontWeight: '600' },
  quantityContainer: { alignItems: 'flex-end' },
  quantityText: { fontSize: 16, fontWeight: 'bold' },
  detailsRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' },
  detailsLeft: { flex: 1, gap: 4, marginRight: 8 },
  detailsRight: { alignItems: 'flex-end' },
  detailItem: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  detailText: { fontSize: 13 },
  dateText: { fontSize: 12, fontWeight: '500' },
  timeText: { fontSize: 11, marginTop: 2 },
  additionalInfo: { marginTop: 8 },
  infoRow: { flexDirection: 'row', marginVertical: 2, gap: 8 },
  infoLabel: { fontSize: 11, fontWeight: '500', minWidth: 60 },
  infoValue: { fontSize: 11, flex: 1 },
  performedByContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end', marginTop: 8, gap: 4 },
  performedByText: { fontSize: 10, fontStyle: 'italic' },
  compactItem: { paddingVertical: 8, paddingHorizontal: 12, borderLeftWidth: 3, marginVertical: 1 },
  compactHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 },
  compactTypeContainer: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  compactTypeText: { fontSize: 12, fontWeight: '600' },
  compactQuantity: { fontSize: 14, fontWeight: 'bold' },
  compactDetails: { gap: 2 },
  compactTime: { fontSize: 10 },
  compactNote: { fontSize: 10, fontStyle: 'italic' },
  emptyContainer: { alignItems: 'center', paddingVertical: 32 },
  emptyText: { fontSize: 14, marginTop: 12, textAlign: 'center' },
  moreIndicator: { paddingVertical: 12, alignItems: 'center' },
  moreText: { fontSize: 12, fontWeight: '500' },
});

export default memo(TransactionHistoryList);