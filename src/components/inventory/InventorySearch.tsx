import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { <PERSON>bar, Text, <PERSON><PERSON>, Card, Menu, IconButton, Switch } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { useInventoryFilter } from '../../hooks/useInventoryFilter'; // Correct path to your new hook
import { InventoryItem } from '../../types/inventory';

interface InventorySearchProps {
  items: InventoryItem[];
  onFilteredResults: (filteredItems: InventoryItem[]) => void;
  placeholder?: string;
  showAdvancedFilters?: boolean;
}

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'category', label: 'Category' },
  { value: 'sellingPrice', label: 'Price' },
  { value: 'minimumStockLevel', label: 'Stock Level' },
];

const InventorySearch: React.FC<InventorySearchProps> = ({
  items,
  onFilteredResults,
  placeholder = 'Search items by name or category...',
  showAdvancedFilters = true,
}) => {
  const theme = useTheme();
  const {
    searchQuery,
    setSearchQuery,
    filteredItems,
    filters,
    dispatch,
    activeFilterCount,
    clearFilters,
  } = useInventoryFilter(items);
  
  const [showFiltersUI, setShowFiltersUI] = useState<boolean>(false);
  const [showSortMenu, setShowSortMenu] = useState<boolean>(false);

  // Notify parent component of the results
  React.useEffect(() => {
    onFilteredResults(filteredItems);
  }, [filteredItems, onFilteredResults]);

  const handleSortChange = (sortBy: string) => {
    dispatch({ type: 'SET_SORT', payload: { sortBy, sortOrder: filters.sortOrder } });
    setShowSortMenu(false);
  };

  const toggleSortOrder = () => {
    dispatch({ type: 'SET_SORT', payload: { sortBy: filters.sortBy, sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' } });
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchRow}>
        <Searchbar
          placeholder={placeholder}
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant }]}
        />
        {showAdvancedFilters && (
          <IconButton
            icon={showFiltersUI ? 'filter-variant-remove' : 'filter-variant'}
            size={24}
            onPress={() => setShowFiltersUI(!showFiltersUI)}
            iconColor={activeFilterCount > 0 ? theme.colors.primary : theme.colors.onSurfaceVariant}
          />
        )}
      </View>

      <View style={styles.summaryRow}>
        <Text style={[styles.summaryText, { color: theme.colors.onSurfaceVariant }]}>
          Showing {filteredItems.length} of {items.length} items
        </Text>
        {activeFilterCount > 0 && (
          <Button mode='text' onPress={clearFilters} compact>
            Clear Filters ({activeFilterCount})
          </Button>
        )}
      </View>

      {showAdvancedFilters && showFiltersUI && (
        <Card style={styles.filtersCard} mode='outlined'>
          <Card.Content>
            <View style={styles.filterSection}>
              <Text style={styles.filterLabel}>Sort By</Text>
              <View style={styles.sortRow}>
                <Menu
                  visible={showSortMenu}
                  onDismiss={() => setShowSortMenu(false)}
                  anchor={
                    <Button mode='outlined' onPress={() => setShowSortMenu(true)} icon='sort' style={styles.sortButton}>
                      {SORT_OPTIONS.find(opt => opt.value === filters.sortBy)?.label}
                    </Button>
                  }
                >
                  {SORT_OPTIONS.map(opt => (
                    <Menu.Item key={opt.value} onPress={() => handleSortChange(opt.value)} title={opt.label} />
                  ))}
                </Menu>
                <IconButton
                  icon={filters.sortOrder === 'asc' ? 'sort-ascending' : 'sort-descending'}
                  onPress={toggleSortOrder}
                />
              </View>
            </View>
            <View style={styles.toggleRow}>
              <Text style={styles.filterLabel}>Include Inactive Items</Text>
              <Switch value={!filters.isActive} onValueChange={() => dispatch({ type: 'TOGGLE_ACTIVE' })} />
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

// Styles remain very similar
const styles = StyleSheet.create({
  container: { marginBottom: 16, paddingHorizontal: 8 },
  searchRow: { flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 8 },
  searchBar: { flex: 1, elevation: 0 },
  summaryRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8, paddingHorizontal: 4 },
  summaryText: { fontSize: 12 },
  filtersCard: { marginTop: 8 },
  filterSection: { marginBottom: 16 },
  filterLabel: { fontSize: 14, fontWeight: '600', marginBottom: 8 },
  sortRow: { flexDirection: 'row', alignItems: 'center', gap: 8 },
  sortButton: { flex: 1 },
  toggleRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
});

export default InventorySearch;