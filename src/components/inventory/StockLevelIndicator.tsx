import React, { useMemo, memo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, ProgressBar } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import UnitConverter from '../../utils/UnitConverter';

// --- TYPE DERIVATION (This section solves the errors) ---
// 1. Get the full theme type directly from the hook's return value
type AppTheme = ReturnType<typeof useTheme>;
// 2. Get the specific keys of the 'colors' object from the theme type
type StockColorKey = Exclude<keyof AppTheme['colors'], 'toast'>;

// --- PROPS INTERFACE ---
type IndicatorSize = 'small' | 'medium' | 'large';
interface StockLevelIndicatorProps {
  currentStock: number;
  minimumLevel: number;
  unit: string;
  size?: IndicatorSize;
  showProgressBar?: boolean;
  showIcon?: boolean;
  showText?: boolean;
  style?: ViewStyle;
  maxLevel?: number;
  precision?: number;
}

// --- CONFIGURATION TYPES ---
interface StockLevelConfig {
  status: string;
  threshold: number | ((min: number) => number);
  icon: PhosphorIconName;
  text: string;
  colorKey: StockColorKey; // Use the derived key type
}

const STOCK_LEVELS: StockLevelConfig[] = [
  { status: 'out-of-stock', threshold: 0, icon: 'x-circle', text: 'Out of Stock', colorKey: 'error' },
  { status: 'low-stock', threshold: (min) => min, icon: 'warning', text: 'Low Stock', colorKey: 'tertiary' },
  { status: 'adequate', threshold: (min) => min * 2, icon: 'check-circle', text: 'Adequate', colorKey: 'primary' },
  { status: 'high-stock', threshold: Infinity, icon: 'check-circle', text: 'Good Stock', colorKey: 'primary' },
];

// --- INTERNAL SUB-COMPONENTS ---
const DetailRow = memo(({ label, value }: { label: string; value: string }) => {
  const theme = useTheme();
  return (
    <View style={styles.detailRow}>
      <Text style={[styles.detailLabel, { color: theme.colors.onSurfaceVariant }]}>{label}:</Text>
      <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>{value}</Text>
    </View>
  );
});
DetailRow.displayName = 'DetailRow';

const ProgressBarSection = memo(
  ({ progress, color, minimumLevel, maxLevel, formattedMinimum, height }: any) => {
    const theme = useTheme();
    const minLevelPercentage = Math.min((minimumLevel / maxLevel) * 100, 100);

    return (
      <View style={styles.progressContainer}>
        <ProgressBar progress={progress} color={color} style={[styles.progressBar, { height }]} />
        {minimumLevel > 0 && (
          <View>
            <View
              style={[
                styles.minimumLine,
                { backgroundColor: theme.colors.onSurfaceVariant, left: `${minLevelPercentage}%` },
              ]}
            />
            <Text style={[styles.minimumText, { color: theme.colors.onSurfaceVariant }]}>
              Min: {formattedMinimum}
            </Text>
          </View>
        )}
      </View>
    );
  }
);
ProgressBarSection.displayName = 'ProgressBarSection';


// --- MAIN COMPONENT ---
const StockLevelIndicator: React.FC<StockLevelIndicatorProps> = ({
  currentStock,
  minimumLevel,
  unit,
  size = 'medium',
  showProgressBar = true,
  showIcon = true,
  showText = true,
  style,
  maxLevel,
  precision = 2,
}) => {
  const theme = useTheme();

  const sizeConfig = useMemo(() => {
    switch (size) {
      case 'small': return { iconSize: 16, textSize: 12, quantitySize: 14, padding: 8, progressHeight: 4 };
      case 'large': return { iconSize: 24, textSize: 16, quantitySize: 20, padding: 16, progressHeight: 8 };
      default: return { iconSize: 20, textSize: 14, quantitySize: 16, padding: 12, progressHeight: 6 };
    }
  }, [size]);

  const stockInfo = useMemo(() => {
    const level =
      STOCK_LEVELS.slice()
        .reverse()
        .find((l) => {
          const threshold = typeof l.threshold === 'function' ? l.threshold(minimumLevel) : l.threshold;
          return currentStock <= threshold;
        }) || STOCK_LEVELS[0];

    const color = theme.colors[level.colorKey];

    return {
      ...level,
      color: typeof color === 'string' ? color : theme.colors.primary,
      backgroundColor: typeof color === 'string' ? `${color}1A` : `${theme.colors.primary}1A`,
    };
  }, [currentStock, minimumLevel, theme.colors]);

  const formattedStock = UnitConverter.formatQuantity(currentStock, unit, precision);
  const formattedMinimum = UnitConverter.formatQuantity(minimumLevel, unit, precision);

  const effectiveMaxLevel = maxLevel || minimumLevel * 3 || 1;
  const progress = Math.min(currentStock / effectiveMaxLevel, 1);

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: stockInfo.backgroundColor, padding: sizeConfig.padding },
        style,
      ]}
    >
      <View style={styles.headerRow}>
        {showIcon && (
          <PhosphorIcon name={stockInfo.icon} size={sizeConfig.iconSize} color={stockInfo.color} />
        )}
        {showText && (
          <View style={styles.textContainer}>
            <Text style={[styles.statusText, { color: stockInfo.color, fontSize: sizeConfig.textSize }]}>
              {stockInfo.text}
            </Text>
            <Text
              style={[
                styles.quantityText,
                { color: theme.colors.onSurface, fontSize: sizeConfig.quantitySize },
              ]}
            >
              {formattedStock}
            </Text>
          </View>
        )}
      </View>

      {showProgressBar && (
        <ProgressBarSection
          progress={progress}
          color={stockInfo.color}
          minimumLevel={minimumLevel}
          maxLevel={effectiveMaxLevel}
          formattedMinimum={formattedMinimum}
          height={sizeConfig.progressHeight}
        />
      )}

      {size === 'large' && (
        <View style={styles.detailsContainer}>
          <DetailRow label="Current" value={formattedStock} />
          <DetailRow label="Minimum" value={formattedMinimum} />
          {maxLevel && (
            <DetailRow label="Maximum" value={UnitConverter.formatQuantity(maxLevel, unit, precision)} />
          )}
        </View>
      )}
    </View>
  );
};

// --- STYLES ---
const styles = StyleSheet.create({
  container: { borderRadius: 12, marginVertical: 4 },
  headerRow: { flexDirection: 'row', alignItems: 'center', gap: 12 },
  textContainer: { flex: 1 },
  statusText: { fontWeight: '600' },
  quantityText: { fontWeight: 'bold', marginTop: 2 },
  progressContainer: { marginTop: 12 },
  progressBar: { borderRadius: 99 },
  minimumLine: { position: 'absolute', top: -4, width: 2, height: 8 },
  minimumText: { fontSize: 10, marginTop: 4, alignSelf: 'center' },
  detailsContainer: { marginTop: 16, borderTopWidth: 1, borderTopColor: 'rgba(0,0,0,0.05)', paddingTop: 12, gap: 6 },
  detailRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  detailLabel: { fontSize: 12 },
  detailValue: { fontSize: 12, fontWeight: '500' },
});

export default memo(StockLevelIndicator);