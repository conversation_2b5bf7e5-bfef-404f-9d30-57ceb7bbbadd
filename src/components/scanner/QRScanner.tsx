// EXPO GO COMPATIBLE QR SCANNER COMPONENT - Professional QR code scanning
import { CameraView, Camera } from 'expo-camera';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Button, ActivityIndicator } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';

interface QRScannerProps {
  onScan: (data: string) => void;
  isVisible: boolean;
}

const QRScanner: React.FC<QRScannerProps> = ({ onScan, isVisible = true }) => {
  const SCAN_FRAME_SIZE = 250; // Moved here
  const [scanned, setScanned] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const scannerRef = useRef<CameraView | null>(null);
  const { theme } = useTheme();
  const { showError } = useToast();

  const requestCameraPermission = useCallback(async () => {
    setIsLoading(true);
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    } catch (error) {
      setHasPermission(false);
      showError('Camera not available on this device.');
    } finally {
      setIsLoading(false);
    }
  }, [showError]); // Add external dependency to useCallback array

  useEffect(() => {
    if (isVisible) {
      requestCameraPermission();
    }
  }, [isVisible, requestCameraPermission]);


  const handleBarCodeScanned = ({ data }: { data: string }) => {
    if (scanned) {return;}
    setScanned(true);
    if (onScan) {
      onScan(data);
    }
  };

  const resetScanner = () => {
    setScanned(false);
  };

  const styles = React.useMemo(() => {
    return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000',
    },
    camera: {
      flex: 1,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      flex: 1,
      flexDirection: 'column',
    },
    darkOverlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
    },
    // --- Header / Footer ---
    
    // --- Scanning Area ---
    scanningArea: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    scanFrame: {
      width: SCAN_FRAME_SIZE,
      height: SCAN_FRAME_SIZE,
      // REFACTOR: Use theme color for consistency
      borderColor: theme.colors.primary,
      borderRadius: 16,
      borderWidth: 3,
    },
    
    
  })}, [theme]);

  if (!isVisible) {
    return null;
  }

  

  

  return (
    <View style={styles.container}>
      <CameraView
        ref={scannerRef}
        style={styles.camera}
        facing="back"
        barcodeScannerSettings={{
          barcodeTypes: ["qr"],
        }}
        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
      />

      {/* Dark Overlay */}
      <View style={styles.overlay}>
        {/* Top dark overlay */}
        <View style={[styles.darkOverlay, { flex: 1, width: '100%' }]} />
        {/* Middle section with transparent cutout */}
        <View style={{ flexDirection: 'row', height: SCAN_FRAME_SIZE }}>
          {/* Left dark overlay */}
          <View style={[styles.darkOverlay, { flex: 1, height: '100%' }]} />
          {/* Scanning area (transparent) */}
          <View style={styles.scanFrame} />
          {/* Right dark overlay */}
          <View style={[styles.darkOverlay, { flex: 1, height: '100%' }]} />
        </View>
        {/* Bottom dark overlay */}
        <View style={[styles.darkOverlay, { flex: 1, width: '100%' }]} />
      </View>
    </View>
  );
};

export default QRScanner;
