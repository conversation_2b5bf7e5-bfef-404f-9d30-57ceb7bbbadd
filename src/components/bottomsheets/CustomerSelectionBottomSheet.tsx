import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFlatList,
} from '@gorhom/bottom-sheet';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ListRenderItem,
  StyleSheet,
  TextInput as RNTextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Text } from 'react-native-paper';

import { DataCard } from '@/components/cards';
import Button from '@/components/ui/Button';
import ChipGroup from '@/components/ui/ChipGroup';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
// FIX: Using the global Customer type to ensure consistency
import { Customer } from '@/types/business';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

// --- Types & Hooks ---
export interface CustomerSelectionBottomSheetProps {
  onSelect: (customer: Customer) => void;
  onClose: () => void;
}

export interface CustomerSelectionBottomSheetRef {
  open: () => void;
  close: () => void;
}

const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
};

// --- Component ---
const CustomerSelectionBottomSheet = forwardRef<
  CustomerSelectionBottomSheetRef,
  CustomerSelectionBottomSheetProps
>(({ onSelect, onClose }, ref) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { state } = useData();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<string>('All');

  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const snapPoints = useMemo(() => ['80%'], []);
  const allCustomers = useMemo(() => state.customers || [], [state.customers]);

  const filteredCustomers = useMemo(() => {
    let filtered = allCustomers;
    const searchLower = debouncedSearchQuery.trim().toLowerCase();

    if (searchLower) {
      filtered = filtered.filter(
        c =>
          c.name.toLowerCase().includes(searchLower) ||
          c.phone?.includes(searchLower)
      );
    }
    // ... your existing filter logic can be added back here if needed
    return filtered;
  }, [allCustomers, debouncedSearchQuery, selectedFilter]);

  useImperativeHandle(ref, () => ({
      open: () => bottomSheetRef.current?.snapToIndex(0),
      close: () => bottomSheetRef.current?.close(),
  }));

  const handleConfirmSelection = useCallback(() => {
    if (selectedCustomer) {
      onSelect(selectedCustomer);
      bottomSheetRef.current?.close();
    }
  }, [selectedCustomer, onSelect]);

  const renderCustomerItem: ListRenderItem<Customer> = useCallback(
    ({ item }) => (
      // FIX: Reverted to using your existing DataCard and removed the non-existent 'activeOrders' prop
      <DataCard
        id={item.id}
        name={item.name}
        phone={item.phone}
        totalOrders={item.totalOrders}
        isVIP={item.isVIP}
        lastOrderDate={item.lastOrderDate ? new Date(item.lastOrderDate) : null}
        cardType="customer"
        onPress={() => setSelectedCustomer(item)}
        onCheckboxPress={() => setSelectedCustomer(item)}
        selected={selectedCustomer?.id === item.id}
        showSelectionIndicator
        isInBottomSheet
      />
    ),
    [selectedCustomer]
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop 
        {...props} 
        appearsOnIndex={0} 
        disappearsOnIndex={-1} 
        pressBehavior="close" 
      />
    ),
    []
  );

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={snapPoints}
      onChange={(index: number) => { if (index === -1) {onClose()} }}
      backdropComponent={renderBackdrop}
      handleIndicatorStyle={{ backgroundColor: theme.colors.outlineVariant }}
      backgroundStyle={{ backgroundColor: theme.colors.surface }}
    >
      {/* FIX: Header UI is now included directly in this file */}
      <View style={[styles.header, { borderBottomColor: theme.colors.outlineVariant }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.onSurface }]}>Select Customer</Text>
        <TouchableOpacity style={styles.closeButton} onPress={() => bottomSheetRef.current?.close()}>
          <PhosphorIcon name="x" size={20} color={theme.colors.onSurface} />
        </TouchableOpacity>
      </View>

      <View style={styles.fixedSection}>
        <RNTextInput
          style={[styles.searchInput, { color: theme.colors.onSurface, backgroundColor: theme.colors.surfaceVariant }]}
          placeholder="Search customers..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={theme.colors.onSurfaceVariant}
        />
        <ChipGroup
          filters={['All', 'Active', 'VIP', 'New']}
          selectedFilter={selectedFilter}
          onFilterChange={(filter) => setSelectedFilter(String(filter))}
        />
      </View>

      <BottomSheetFlatList
        data={filteredCustomers}
        renderItem={renderCustomerItem}
        keyExtractor={(item: Customer) => item.id}
        contentContainerStyle={styles.scrollableContentContainer}
        keyboardShouldPersistTaps="handled"
      />

      <View style={[styles.actionButtonsContainer, { borderTopColor: theme.colors.outlineVariant }]}>
        <Button variant="ghost" onPress={() => bottomSheetRef.current?.close()} style={styles.button}>Cancel</Button>
        <Button variant="primary" onPress={handleConfirmSelection} style={styles.button} disabled={!selectedCustomer}>Select</Button>
      </View>
    </BottomSheet>
  );
});

// --- Styles ---
const createStyles = (theme: ReturnType<typeof useTheme>) =>
  StyleSheet.create({
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
    },
    headerTitle: { fontSize: 20, fontWeight: '600' },
    closeButton: { padding: 4 },
    fixedSection: {
      paddingHorizontal: 16,
      paddingTop: 8,
      paddingBottom: 4,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outlineVariant,
    },
    scrollableContentContainer: { paddingHorizontal: 16, paddingBottom: 16 },
    searchInput: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 24,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      marginBottom: 12,
    },
    actionButtonsContainer: {
      flexDirection: 'row',
      padding: 16,
      borderTopWidth: 1,
      gap: 12,
    },
    button: { flex: 1 },
  });

export default CustomerSelectionBottomSheet;