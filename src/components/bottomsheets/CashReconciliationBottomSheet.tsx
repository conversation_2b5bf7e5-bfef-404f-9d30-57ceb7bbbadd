import { BottomSheetBackdrop, BottomSheetBackdropProps, BottomSheetModal } from '@gorhom/bottom-sheet';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Keyboard, ScrollView, StyleSheet, View } from 'react-native';
import { Card, Chip, Divider, IconButton, Portal, Text, TextInput, ActivityIndicator } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { FINANCIAL_CONFIG } from '../../config/constants';
import { useTheme } from '../../context/ThemeContext';
import { useCashReconciliation } from '../../hooks/useCashReconciliation';
import { formatCurrency } from '../../utils/currency';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import ActionSheet from '../ui/ActionSheet';
import Button from '../ui/Button';

// --- Helper ---


export interface CashReconciliationBottomSheetRef {
  present: (date?: string) => void;
  close: () => void;
}
// --- Component ---
const CashReconciliationBottomSheet = forwardRef<CashReconciliationBottomSheetRef, {}>((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const {
    state,
    difference,
    differenceStatus,
    loadExpectedCash,
    handleInputChange,
    submitReconciliation,
    reset,
  } = useCashReconciliation();

  const { form, expectedCashData, isLoading, isCalculating, error, errors } = state;
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actionSheetContent, setActionSheetContent] = useState({ title: '', description: '' });

  const snapPoints = useMemo(() => ['90%'], []);

  useEffect(() => {
    // This effect ensures that whenever the bottom sheet is opened and the date is set,
    // it fetches the expected cash data.
    loadExpectedCash(form.date);
  }, [form.date]); // Dependency on form.date

  useImperativeHandle(
    ref,
    () => ({
      present: (date) => {
        reset(); // Reset state to default
        if (date) {
            // If a specific date is passed, update it in the form state
            // This will trigger the useEffect above to load data for the new date
            handleInputChange('date', date);
        } else {
            // If no date is passed, load for the default date (today)
            loadExpectedCash(form.date);
        }
        bottomSheetRef.current?.present();
      },
      close: () => bottomSheetRef.current?.close(),
    }),
    [reset, handleInputChange, loadExpectedCash, form.date]
  );

  const handleSubmit = async () => {
    Keyboard.dismiss();
    const success = await submitReconciliation();
    if (success) {
      const diffText = formatCurrency(Math.abs(difference));
      const isBalanced = differenceStatus.status === 'balanced';
      const message = isBalanced
        ? `Reconciliation successful! The difference of ${diffText} is within tolerance.`
        : `Discrepancy detected! The difference is ${diffText}. Please review and investigate.`;
      setActionSheetContent({ title: 'Reconciliation Complete', description: message });
      setActionSheetVisible(true);
    } else if (state.error) { // Check state.error after submission attempt
      setActionSheetContent({ title: 'Error', description: state.error });
      setActionSheetVisible(true);
    }
  };

  const getStatusInfo = useMemo(() => {
    if (!form.actualCash || !expectedCashData) {
      return 'Awaiting actual cash amount';
    }
    const diffText = formatCurrency(Math.abs(difference));
    return `${
      differenceStatus.status === 'balanced' ? 'Balanced' : 'Discrepancy'
    } • ${diffText}`;
  }, [form.actualCash, expectedCashData, difference, differenceStatus.status]);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />
    ),
    []
  );

  return (
    <Portal>
      <BottomSheetModal
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        enablePanDownToClose
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outlineVariant }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.tertiary}20` }]}>
                <PhosphorIcon name="cash-register" size={24} color={theme.colors.tertiary} />
              </View>
              <View>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Cash Reconciliation
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {new Date(form.date).toLocaleDateString()} • {getStatusInfo}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={24}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Expected Cash Card */}
            <Card style={[styles.card, { backgroundColor: theme.colors.surfaceVariant }]}>
              <Card.Content>
                <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                  Expected Cash Calculation
                </Text>
                {isCalculating ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator color={theme.colors.primary} />
                    <Text style={[styles.infoText, {color: theme.colors.onSurfaceVariant}]}>Calculating...</Text>
                  </View>
                ) : expectedCashData ? (
                  <View style={styles.breakdownContainer}>
                    <View style={styles.breakdownRow}>
                      <Text>Opening Cash</Text>
                      <Text>{formatCurrency(expectedCashData.openingCash)}</Text>
                    </View>
                    <View style={styles.breakdownRow}>
                      <Text>Cash Sales ({expectedCashData.orderCount} orders)</Text>
                      <Text style={{ color: '#4CAF50' }}>+{formatCurrency(expectedCashData.cashSales)}</Text>
                    </View>
                    <View style={styles.breakdownRow}>
                      <Text>Cash Expenses</Text>
                      <Text style={{ color: '#F44336' }}>-{formatCurrency(expectedCashData.cashExpenses)}</Text>
                    </View>
                    <Divider style={styles.divider} />
                    <View style={styles.breakdownRow}>
                      <Text variant="titleMedium" style={{ fontWeight: 'bold' }}>Expected Closing Cash</Text>
                      <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: 'bold' }}>
                        {formatCurrency(expectedCashData.expectedClosingCash)}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <Text style={[styles.infoText, { color: theme.colors.error }]}>
                    {error || 'Could not calculate expected cash.'}
                  </Text>
                )}
              </Card.Content>
            </Card>

            {/* Inputs & Difference Card */}
            <View style={styles.formSection}>
              <TextInput
                label="Actual Cash Count *"
                value={form.actualCash}
                onChangeText={text => handleInputChange('actualCash', text)}
                keyboardType="numeric"
                error={!!errors.actualCash}
                placeholder="0.00"
              />
              {!!errors.actualCash && <Text style={styles.errorText}>{errors.actualCash}</Text>}

              {form.actualCash && expectedCashData && (
                <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
                  <Card.Content>
                    <View style={styles.differenceHeader}>
                      <PhosphorIcon
                        name={differenceStatus.icon}
                        size={24}
                        color={differenceStatus.color}
                      />
                      <Text variant="titleMedium" style={{ marginLeft: 8 }}>Reconciliation Status</Text>
                    </View>
                    <View style={styles.breakdownRow}>
                      <Text>Difference</Text>
                      <Text variant="titleMedium" style={{ color: differenceStatus.color, fontWeight: 'bold' }}>
                        {difference >= 0 ? '+' : ''}{formatCurrency(difference)}
                      </Text>
                    </View>
                    <Chip
                      icon={differenceStatus.icon}
                      style={[styles.statusChip, { backgroundColor: `${differenceStatus.color}20` }]}
                      textStyle={{ color: differenceStatus.color }}
                    >
                      {differenceStatus.status === 'balanced' ? 'Balanced' : 'Discrepancy'}
                    </Chip>
                  </Card.Content>
                </Card>
              )}

              <TextInput
                label="Performed By *"
                value={form.performedBy}
                onChangeText={text => handleInputChange('performedBy', text)}
                error={!!errors.performedBy}
                placeholder="Manager name"
              />
              {!!errors.performedBy && <Text style={styles.errorText}>{errors.performedBy}</Text>}

              <TextInput
                label="Notes (Optional)"
                value={form.notes}
                onChangeText={text => handleInputChange('notes', text)}
                multiline
                numberOfLines={3}
                placeholder="Note any discrepancies..."
              />
            </View>
            <View style={{ height: insets.bottom + 80 }} />
          </ScrollView>

          {/* Footer */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outlineVariant }]}>
            <Button
              variant="outline"
              onPress={() => bottomSheetRef.current?.close()}
              style={styles.button}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onPress={handleSubmit}
              style={styles.button}
              loading={isLoading}
              disabled={isLoading || !expectedCashData || isCalculating}
              icon="check"
            >
              Complete
            </Button>
          </View>
        </View>
      </BottomSheetModal>

      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={actionSheetContent.title}
        description={actionSheetContent.description}
        options={[{ text: 'OK', onPress: () => setActionSheetVisible(false) }]}
      />
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: { elevation: 8, zIndex: 1000 },
  container: { flex: 1 },
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: StyleSheet.hairlineWidth },
  headerLeft: { flex: 1, flexDirection: 'row', alignItems: 'center', gap: 12 },
  iconContainer: { width: 40, height: 40, borderRadius: 12, justifyContent: 'center', alignItems: 'center' },
  title: { fontWeight: '700' },
  subtitle: { marginTop: 2 },
  content: { flex: 1, paddingHorizontal: 16, paddingTop: 16 },
  card: { marginBottom: 16, borderRadius: 12 },
  infoText: { marginLeft: 8, fontSize: 14 },
  loadingContainer: { flexDirection: 'row', alignItems: 'center', paddingTop: 16 },
  breakdownContainer: { gap: 8, marginTop: 16 },
  breakdownRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 4 },
  divider: { marginVertical: 8 },
  formSection: { paddingBottom: 32, gap: 16 },
  differenceHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  statusChip: { alignSelf: 'flex-start', marginTop: 8 },
  footer: { paddingHorizontal: 16, paddingTop: 12, borderTopWidth: StyleSheet.hairlineWidth, flexDirection: 'row', gap: 12 },
  button: { flex: 1 },
  errorText: { color: 'red', marginTop: -12, fontSize: 12, paddingHorizontal: 4 },
});

export default CashReconciliationBottomSheet;