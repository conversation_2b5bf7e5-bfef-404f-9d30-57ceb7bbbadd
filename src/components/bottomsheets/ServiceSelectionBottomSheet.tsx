import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFlatList, // 👈 1. Import the correct list component
} from '@gorhom/bottom-sheet';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ListRenderItem, // FlatList is no longer imported from here
  StyleSheet,
  TextInput as RNTextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { StorageService } from '../../services/StorageService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import { DataCard } from '../cards';
import Button from '../ui/Button';

// --- Types & Hooks ---
type Theme = ReturnType<typeof useTheme>;

interface ServiceTemplate {
  id: string;
  name: string;
  price: number;
  measurementFields: string[];
}

interface ServiceTypeSelectionBottomSheetProps {
  onSelect: (templates: ServiceTemplate[]) => void;
  onClose: () => void;
}

export interface ServiceTypeSelectionBottomSheetRef {
  open: () => void;
  close: () => void;
}

const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
};

// --- Component ---
const ServiceSelectionBottomSheet = forwardRef<
  ServiceTypeSelectionBottomSheetRef,
  ServiceTypeSelectionBottomSheetProps
>(({ onSelect, onClose }, ref) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const bottomSheetRef = useRef<BottomSheet>(null);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplates, setSelectedTemplates] = useState<ServiceTemplate[]>([]);
  const [templates, setTemplates] = useState<ServiceTemplate[]>([]);

  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const snapPoints = useMemo(() => ['80%'], []);

  // Load templates from storage once, seeding if empty
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        let stored = await StorageService.get<ServiceTemplate[]>('serviceTypes');
        if (!stored || stored.length === 0) {
          const defaultTemplates: ServiceTemplate[] = [
            { id: '1', name: 'Shirt', price: 1200, measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length'] },
            { id: '2', name: 'Pants', price: 1000, measurementFields: ['waist', 'hip', 'length', 'inseam'] },
            { id: '3', name: 'Suit', price: 3500, measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length', 'inseam'] },
            { id: '4', name: 'Dress', price: 1800, measurementFields: ['bust', 'waist', 'hip', 'length'] },
            { id: '5', name: 'Kurta', price: 1500, measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length'] },
          ];
          await StorageService.set('serviceTypes', defaultTemplates);
          stored = defaultTemplates;
        }
        setTemplates(stored);
      } catch (error) {
        LoggingService.error("Failed to load service templates", 'SERVICE_SELECTION', error as Error);
      }
    };
    loadTemplates();
  }, []);

  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {onClose();}
  }, [onClose]);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} pressBehavior="close" />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    open: () => bottomSheetRef.current?.snapToIndex(0),
    close: () => bottomSheetRef.current?.close(),
  }), []);

  const filteredTemplates = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return templates;
    }
    const searchLower = debouncedSearchQuery.toLowerCase();
    return templates.filter(
      template =>
        template.name.toLowerCase().includes(searchLower) ||
        template.measurementFields.join(' ').toLowerCase().includes(searchLower)
    );
  }, [templates, debouncedSearchQuery]);

  const handleTemplateSelect = useCallback((template: ServiceTemplate) => {
    setSelectedTemplates(prev => {
      const isSelected = prev.some(t => t.id === template.id);
      return isSelected ? prev.filter(t => t.id !== template.id) : [...prev, template];
    });
  }, []);

  const handleConfirmSelection = useCallback(() => {
    onSelect(selectedTemplates);
    bottomSheetRef.current?.close();
  }, [onSelect, selectedTemplates]);

  const isTemplateSelected = useCallback(
    (template: ServiceTemplate) => selectedTemplates.some(t => t.id === template.id),
    [selectedTemplates]
  );

  const renderTemplateItem: ListRenderItem<ServiceTemplate> = useCallback(
    ({ item }) => (
      <DataCard
        {...item}
        cardType="service"
        onPress={() => handleTemplateSelect(item)}
        onCheckboxPress={() => handleTemplateSelect(item)}
        selected={isTemplateSelected(item)}
        showSelectionIndicator
        isInBottomSheet
      />
    ),
    [handleTemplateSelect, isTemplateSelected]
  );

  const keyExtractor = useCallback((item: ServiceTemplate) => item.id, []);

  const ListEmptyComponent = useMemo(
    () => (
      <View style={styles.noResultsContainer}>
        <Text style={styles.noResultsText}>
          {debouncedSearchQuery ? 'No services found' : 'No services available'}
        </Text>
      </View>
    ),
    [debouncedSearchQuery, styles]
  );

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      backdropComponent={renderBackdrop}
      enablePanDownToClose={false}
      handleStyle={styles.handleStyle}
      handleIndicatorStyle={styles.handleIndicator}
      backgroundStyle={styles.background}
      keyboardBehavior="extend"
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Select Service Type</Text>
        <TouchableOpacity style={styles.closeButton} onPress={() => bottomSheetRef.current?.close()}>
          <PhosphorIcon name="x" size={20} color={theme.colors.onSurface} />
        </TouchableOpacity>
      </View>

      <View style={styles.fixedSection}>
        <View style={styles.searchInputWrapper}>
          <PhosphorIcon name="magnifying-glass" size={20} color={theme.colors.onSurfaceVariant} style={styles.searchIcon} />
          <RNTextInput
            style={styles.searchInput}
            placeholder="Search service types..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={theme.colors.onSurfaceVariant}
          />
          {searchQuery ? (
            <TouchableOpacity style={styles.clearButton} onPress={() => setSearchQuery('')}>
              <PhosphorIcon name="x" size={20} color={theme.colors.onSurfaceVariant} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      <View style={styles.scrollableContent}>
        {/* 👇 2. Use the correct list component for scrolling */}
        <BottomSheetFlatList
          data={filteredTemplates}
          renderItem={renderTemplateItem}
          keyExtractor={keyExtractor}
          ListEmptyComponent={ListEmptyComponent}
          contentContainerStyle={styles.scrollableContentContainer}
          keyboardShouldPersistTaps="handled"
          removeClippedSubviews
          maxToRenderPerBatch={10}
          windowSize={11}
          initialNumToRender={10}
          getItemLayout={(_data: ArrayLike<ServiceTemplate> | null | undefined, index: number) => ({
            length: 88, // Approximate height of DataCard
            offset: 88 * index,
            index,
          })}
        />
      </View>

      <View style={styles.actionButtonsContainer}>
        <Button variant="ghost" onPress={() => bottomSheetRef.current?.close()} style={styles.button}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onPress={handleConfirmSelection}
          style={styles.button}
          disabled={selectedTemplates.length === 0}
        >
          Select ({selectedTemplates.length})
        </Button>
      </View>
    </BottomSheet>
  );
});

// --- Styles ---
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: theme.spacing.lg, paddingVertical: theme.spacing.md, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant, backgroundColor: theme.colors.background },
    headerTitle: { fontSize: theme.typography.fontSize.xl, fontWeight: theme.typography.fontWeight.bold, color: theme.colors.onSurface, flex: 1 },
    closeButton: { padding: theme.spacing.xs, borderRadius: theme.borderRadius.sm },
    fixedSection: { paddingHorizontal: theme.spacing.lg, paddingTop: theme.spacing.sm, paddingBottom: theme.spacing.md, backgroundColor: theme.colors.background, borderBottomWidth: 1, borderBottomColor: theme.colors.outlineVariant },
    scrollableContent: { flex: 1, backgroundColor: theme.colors.background },
    scrollableContentContainer: { paddingHorizontal: theme.spacing.lg, paddingBottom: theme.spacing.lg },
    searchInputWrapper: { position: 'relative', flexDirection: 'row', alignItems: 'center' },
    searchInput: { backgroundColor: theme.colors.surfaceVariant, borderRadius: theme.borderRadius.lg, paddingHorizontal: theme.spacing.lg, paddingVertical: theme.spacing.md, paddingLeft: 44, paddingRight: 44, fontSize: theme.typography.fontSize.md, color: theme.colors.onSurface, flex: 1 },
    searchIcon: { position: 'absolute', left: theme.spacing.md, zIndex: 1 },
    clearButton: { position: 'absolute', right: theme.spacing.md, zIndex: 1 },
    noResultsContainer: { justifyContent: 'center', alignItems: 'center', paddingVertical: theme.spacing.xl, minHeight: 200 },
    noResultsText: { fontSize: theme.typography.fontSize.md, color: theme.colors.onSurfaceVariant, textAlign: 'center' },
    actionButtonsContainer: { flexDirection: 'row', padding: theme.spacing.lg, backgroundColor: theme.colors.background, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, gap: theme.spacing.md },
    button: { flex: 1 },
    handleStyle: { paddingTop: theme.spacing.xs, paddingBottom: theme.spacing.xs },
    handleIndicator: { backgroundColor: theme.colors.outline },
    background: { backgroundColor: theme.colors.background },
  });

export default ServiceSelectionBottomSheet;