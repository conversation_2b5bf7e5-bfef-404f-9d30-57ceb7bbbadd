import { BottomSheetModal, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
// ✅ FIX 2: Import DimensionValue for proper style typing
import { View, StyleSheet, TouchableOpacity, ScrollView, ViewStyle, Text as RNText, DimensionValue } from 'react-native';
import { Text } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import { useTheme } from '../../context/ThemeContext';
import { TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

import TextInput from './TextInput'; 

// --- Component Types ---
interface DropdownOption {
  label: string;
  value: string;
}

interface DropdownProps {
  label?: string;
  placeholder?: string;
  value?: string;
  options: DropdownOption[];
  onValueChange: (value: string) => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  style?: ViewStyle;
  // ✅ FIX 2: Update width prop to use DimensionValue
  width?: DimensionValue;
  flex?: number;
}

const OptionItem = React.memo<{
  option: DropdownOption;
  isSelected: boolean;
  onSelect: (value: string) => void;
}>(({ option, isSelected, onSelect }) => {
  const theme = useTheme();

  const handlePress = useCallback(() => {
    onSelect(option.value);
  }, [onSelect, option.value]);

  return (
    <TouchableOpacity
      style={[
        styles.optionItem,
        { backgroundColor: isSelected ? theme.colors.primaryContainer : 'transparent' },
      ]}
      onPress={handlePress}
    >
      <Text
        style={[
          styles.optionText,
          {
            color: isSelected ? theme.colors.onPrimaryContainer : theme.colors.onSurface,
            fontWeight: isSelected ? TYPOGRAPHY.fontWeight.semibold : TYPOGRAPHY.fontWeight.medium,
          },
        ]}
      >
        {option.label}
      </Text>
      {isSelected && (
        <PhosphorIcon name='check' size={20} color={theme.colors.onPrimaryContainer} />
      )}
    </TouchableOpacity>
  );
});

const DropdownBottomSheetContent = React.memo<{
  options: DropdownOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
}>(({ options, selectedValue, onSelect }) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredOptions = useMemo(
    () => options.filter(option => option.label.toLowerCase().includes(searchQuery.toLowerCase())),
    [options, searchQuery]
  );

  return (
    <View style={styles.bottomSheetContent}>
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
          <TextInput
            // ✅ FIX 1: Add the required 'label' prop
            label='Search'
            placeholder='Search...'
            value={searchQuery}
            onChangeText={setSearchQuery}
            rightIcon='magnifying-glass'
            style={styles.searchInput}
          />
      </View>

      <ScrollView
        style={styles.optionsScroll}
        keyboardShouldPersistTaps='handled'
        contentContainerStyle={styles.optionsContentContainer}
      >
        {filteredOptions.length > 0 ? (
          filteredOptions.map(option => (
            <OptionItem
              key={option.value}
              option={option}
              isSelected={option.value === selectedValue}
              onSelect={onSelect}
            />
          ))
        ) : (
          <View style={styles.noResultsContainer}>
            <Text style={[styles.noResultsText, { color: theme.colors.onSurfaceVariant }]}>
              No options found
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
});

// --- Main Dropdown Component ---
const Dropdown: React.FC<DropdownProps> = ({
  label,
  placeholder = 'Select an option',
  value,
  options,
  onValueChange,
  disabled = false,
  required = false,
  error,
  style,
  width,
  flex,
}) => {
  const theme = useTheme();
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const labelAnimation = useSharedValue(value ? 1 : 0);
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const selectedOption = useMemo(() => options.find(option => option.value === value), [options, value]);
  const displayText = selectedOption?.label || '';

  useEffect(() => {
    const shouldAnimate = !!value || isFocused;
    labelAnimation.value = withTiming(shouldAnimate ? 1 : 0, { duration: 200 });
  }, [value, isFocused, labelAnimation]);

  const handleSelect = useCallback((optionValue: string) => {
    onValueChange(optionValue);
    bottomSheetRef.current?.close();
  }, [onValueChange]);

  const handleInputPress = useCallback(() => {
    if (!disabled) {
      bottomSheetRef.current?.present();
    }
  }, [disabled]);

  const handleSheetChange = useCallback((index: number) => {
      setIsFocused(index > -1);
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />,
    []
  );

  const labelAnimatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(labelAnimation.value, [0, 1], [0, -24]);
    const fontSize = interpolate(labelAnimation.value, [0, 1], [16, 12]);
    return { transform: [{ translateY }], fontSize };
  }, []);
  
  const borderColor = isFocused ? theme.colors.primary : error ? theme.colors.error : theme.colors.outline;

  // With the prop type fixed, this style object is now type-safe.
  return (
    <View style={[{ width, flex }, style]}>
      <TouchableOpacity
        style={[
          styles.textInput,
          {
            borderColor,
            borderWidth: isFocused || error ? 2 : 1,
            backgroundColor: disabled ? theme.colors.surfaceVariant : theme.colors.surface,
          },
        ]}
        onPress={handleInputPress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {label && (
          <Animated.Text style={[styles.floatingLabel, labelAnimatedStyle, { color: borderColor }]}>
            {label}
            {required && <RNText style={{ color: theme.colors.error }}> *</RNText>}
          </Animated.Text>
        )}
        <Text
          style={[
            styles.inputText,
            { color: displayText ? theme.colors.onSurface : theme.colors.onSurfaceVariant },
          ]}
          numberOfLines={1}
        >
          {displayText || placeholder}
        </Text>
        <View style={styles.dropdownIcon}>
          <PhosphorIcon name='caret-down' size={20} color={theme.colors.onSurfaceVariant} />
        </View>
      </TouchableOpacity>

      {error && <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>}

      <BottomSheetModal
        ref={bottomSheetRef}
        snapPoints={['50%', '90%']}
        onChange={handleSheetChange}
        backdropComponent={renderBackdrop}
      >
        <DropdownBottomSheetContent options={options} selectedValue={value} onSelect={handleSelect} />
      </BottomSheetModal>
    </View>
  );
};

const styles = StyleSheet.create({
  // ... styles remain unchanged
  textInput: {
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.lg + SPACING.xs,
    paddingBottom: SPACING.sm,
    minHeight: 56,
    justifyContent: 'center',
  },
  inputText: {
    fontSize: 16,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  floatingLabel: {
    position: 'absolute',
    left: SPACING.md,
    top: 16,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  dropdownIcon: {
    position: 'absolute',
    right: SPACING.md,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
  },
  errorText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    marginTop: SPACING.xs,
    marginLeft: SPACING.md,
  },
  bottomSheetContent: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  searchInput: {},
  optionsScroll: {
    flex: 1,
  },
  optionsContentContainer: {
    padding: SPACING.sm,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  optionText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    flex: 1,
  },
  noResultsContainer: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontStyle: 'italic',
  },
});

export default Dropdown;