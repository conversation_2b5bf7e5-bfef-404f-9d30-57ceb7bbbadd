import React, { useEffect, memo, useCallback } from 'react';
import { Pressable, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  interpolateColor, // Import for smooth color transitions
} from 'react-native-reanimated';

import { useTheme } from '../../context/ThemeContext';

const SWITCH_WIDTH = 48;
const SWITCH_HEIGHT = 28;
const THUMB_SIZE = 20;
const PADDING = 3;

interface UnifiedSwitchProps {
  value: boolean;
  onValueChange?: (value: boolean) => void;
  disabled?: boolean;
  style?: ViewStyle;
}

// Optimization 1: Memoize the component to prevent re-renders when parent state changes.
const Switch: React.FC<UnifiedSwitchProps> = memo(({
  value,
  onValueChange,
  disabled = false,
  style,
}) => {
  const theme = useTheme();
  const progress = useSharedValue(value ? 1 : 0);

  // Animate the progress value when the external `value` prop changes.
  useEffect(() => {
    progress.value = withTiming(value ? 1 : 0, { duration: 200 });
  }, [value, progress]);

  // Optimization 2: Use a stable function reference for the press handler.
  const handlePress = useCallback(() => {
    if (!disabled && onValueChange) {
      onValueChange(!value);
    }
  }, [disabled, onValueChange, value]);

  // Optimization 3: Use semantic theme colors for better adaptability.
  const trackColors = {
    true: theme.colors.primary,
    false: theme.colors.surfaceVariant,
  };
  const thumbColors = {
    true: theme.colors.onPrimary,
    false: theme.colors.outline,
  };

  // Animated style for the track (background)
  const trackAnimatedStyle = useAnimatedStyle(() => {
    // Interpolate the background color for a smooth transition.
    const backgroundColor = interpolateColor(
      progress.value,
      [0, 1],
      [trackColors.false, trackColors.true]
    );
    return { backgroundColor };
  });

  // Animated style for the thumb (the moving circle)
  const thumbAnimatedStyle = useAnimatedStyle(() => {
    // Interpolate the horizontal position.
    const translateX = interpolate(
      progress.value,
      [0, 1],
      [PADDING, SWITCH_WIDTH - THUMB_SIZE - PADDING]
    );
    // Also interpolate the thumb's color.
    const backgroundColor = interpolateColor(
        progress.value,
        [0, 1],
        [thumbColors.false, thumbColors.true]
    );
    return {
      transform: [{ translateX }],
      backgroundColor,
    };
  }, []);

  return (
    <Pressable
      onPress={handlePress}
      disabled={disabled}
      style={[styles.container, { opacity: disabled ? 0.5 : 1 }, style]}
      accessibilityRole='switch'
      accessibilityState={{ checked: value, disabled }}
    >
      <Animated.View style={[styles.track, trackAnimatedStyle]}>
        <Animated.View style={[styles.thumb, thumbAnimatedStyle]} />
      </Animated.View>
    </Pressable>
  );
});

const styles = StyleSheet.create({
  container: {
    width: SWITCH_WIDTH,
    height: SWITCH_HEIGHT,
  },
  track: {
    flex: 1,
    borderRadius: SWITCH_HEIGHT / 2,
    justifyContent: 'center',
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 2,
    shadowOpacity: 0.2,
  },
});

export default Switch;