// @ts-nocheck
import PropTypes from 'prop-types';
import React, { useMemo } from 'react';
import { Platform, Pressable, TouchableOpacity, StyleSheet } from 'react-native';

const ItemRow = React.memo(({ active, onPress, children, theme }) => {
  const styles = useMemo(
    () =>
      StyleSheet.create({
        row: {
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: active ? theme?.colors?.error || '#FFFFFF00' : 'transparent',
          borderRadius: 12,
          paddingVertical: 12,
          paddingHorizontal: 16,
          marginBottom: 4,
        },
      }),
    [active, theme]
  );

  if (Platform.OS === 'android') {
    return (
      <Pressable
        onPress={onPress}
        android_ripple={{ color: `${theme?.colors?.primary || '#FFFFFFFF'}22`, borderless: false }}
        style={styles.row}
        accessibilityRole='button'
        accessibilityState={{ selected: !!active }}
      >
        {children}
      </Pressable>
    );
  }
  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.6}
      style={styles.row}
      accessibilityRole='button'
      accessibilityState={{ selected: !!active }}
    >
      {children}
    </TouchableOpacity>
  );
});

ItemRow.propTypes = {
  active: PropTypes.bool,
  onPress: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  theme: PropTypes.object.isRequired,
};

ItemRow.displayName = 'ItemRow';

export default ItemRow;
