import React, { useState, useEffect, useCallback, useMemo, memo, forwardRef } from 'react';
import {
  View,
  TextInput as RNTextInput,
  StyleSheet,
  ViewStyle,
  Text,
  Pressable,
  TextInputProps,
  StyleProp,
  TextStyle,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import { useTheme } from '../../context/ThemeContext';
import { TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- Type Definitions ---
type InputType = 'text' | 'email' | 'password' | 'number' | 'textarea' | 'date';

interface UnifiedTextInputProps extends Omit<TextInputProps, 'onChangeText' | 'value' | 'style'> {
  label: string;
  value: string;
  onChangeText: (value: string) => void;
  type?: InputType;
  required?: boolean;
  minLength?: number;
  validate?: (value: string) => string;
  error?: string | boolean;
  style?: StyleProp<ViewStyle>;
  rightAffix?: string;
  rightIcon?: PhosphorIconName;
  disabled?: boolean;
  as?: boolean;
}

const validators: Record<InputType, (value: string, minLength?: number) => string> = {
  email: (value) => (!value || /\S+@\S+\.\S+/.test(value) ? '' : 'Invalid email address'),
  number: (value) => (value === '' || !isNaN(Number(value)) ? '' : 'Invalid number'),
  password: (value, minLength = 8) => (value.length >= minLength ? '' : `Password must be at least ${minLength} characters`),
  text: () => '',
  textarea: () => '',
  date: () => '',
};

// --- Component Definition ---
// FIX 1: The component is now wrapped in `forwardRef` and `memo`.
// It now accepts a `ref` as its second argument.
const TextInput = memo(forwardRef<RNTextInput, UnifiedTextInputProps>(({
    label,
    value,
    onChangeText,
    type = 'text',
    required = false,
    minLength,
    validate,
    error: externalError,
    onBlur,
    onFocus,
    style,
    rightAffix,
    rightIcon,
    placeholder,
    disabled = false,
    ...rest
}, ref) => { // <-- `ref` is now available here
    const theme = useTheme();
    const [internalError, setInternalError] = useState('');
    const [isFocused, setIsFocused] = useState(false);
    const [isPasswordHidden, setIsPasswordHidden] = useState(true);
    const labelAnimation = useSharedValue(value || isFocused ? 1 : 0);

    const error = useMemo(() => 
      typeof externalError === 'string' ? externalError : internalError,
      [externalError, internalError]
    );
    
    useEffect(() => {
      labelAnimation.value = withTiming(value || isFocused ? 1 : 0, { duration: 200 });
    }, [value, isFocused, labelAnimation]);

    const runValidation = useCallback((val: string): void => {
        if (required && !val.trim()) {
            setInternalError(`${label} is required.`);
            return;
        }
        const validator = validators[type];
        if (validator) {
            const validationError = validator(val, minLength);
            if (validationError) {
                setInternalError(validationError);
                return;
            }
        }
        if (validate) {
            const customError = validate(val);
             if (customError) {
                setInternalError(customError);
                return;
            }
        }
        setInternalError('');
    }, [required, label, type, minLength, validate]);

    const handleChange = useCallback((val: string) => {
        onChangeText(val);
        if (isFocused) {
            runValidation(val);
        }
    }, [onChangeText, isFocused, runValidation]);

    const handleBlur = useCallback((e: any) => {
        setIsFocused(false);
        runValidation(value);
        onBlur?.(e);
    }, [onBlur, runValidation, value]);

    const handleFocus = useCallback((e: any) => {
        setIsFocused(true);
        onFocus?.(e);
    }, [onFocus]);

    const borderColor = isFocused ? theme.colors.primary : error ? theme.colors.error : theme.colors.outline;
    
    const labelAnimatedStyle = useAnimatedStyle(() => {
        const isFloated = value || isFocused;
        const translateY = interpolate(labelAnimation.value, [0, 1], [0, -14]);
        const translateX = interpolate(labelAnimation.value, [0, 1], [0, -SPACING.xs]);
        const scale = interpolate(labelAnimation.value, [0, 1], [1, 0.85]);
        return {
            transform: [{ translateY }, { translateX }, { scale }],
            color: isFloated && error ? theme.colors.error : isFloated ? theme.colors.primary : theme.colors.onSurfaceVariant,
            backgroundColor: isFloated ? theme.colors.surface : 'transparent',
            paddingHorizontal: isFloated ? SPACING.xs : 0,
        };
    }, [value, isFocused, error, theme]);
    
    const renderRightAccessory = () => {
        if (type === 'password') {
            return (
                <Pressable onPress={() => setIsPasswordHidden(p => !p)} style={styles.iconContainer}>
                    <PhosphorIcon name={isPasswordHidden ? 'eye-slash' : 'eye'} size={22} color={theme.colors.onSurfaceVariant} />
                </Pressable>
            );
        }
        if (rightIcon) {
            return (
                 <View style={styles.iconContainer}>
                    <PhosphorIcon name={rightIcon} size={22} color={theme.colors.onSurfaceVariant} />
                </View>
            );
        }
        if (rightAffix) {
            return <Text style={[styles.affix, { color: theme.colors.onSurfaceVariant }]}>{rightAffix}</Text>;
        }
        return null;
    };

    return (
        <View style={StyleSheet.flatten([styles.container, style])}>
            <View style={[
                styles.inputContainer,
                { borderColor, borderWidth: isFocused || !!error ? 2 : 1, backgroundColor: theme.colors.surface }
            ]}>
                <Animated.Text style={[styles.floatingLabel, labelAnimatedStyle]} pointerEvents="none">
                    {label}
                    {required && ' *'}
                </Animated.Text>
                <RNTextInput
                    // FIX 2: The `ref` is now passed directly to the underlying TextInput.
                    ref={ref}
                    style={StyleSheet.flatten([styles.textInput, { color: theme.colors.onSurface }]) as StyleProp<TextStyle>}
                    value={value}
                    onChangeText={handleChange}
                    onBlur={handleBlur}
                    onFocus={handleFocus}
                    editable={!disabled}
                    placeholder={isFocused ? (placeholder || '') : ''}
                    secureTextEntry={type === 'password' && isPasswordHidden}
                    keyboardType={type === 'number' ? 'numeric' : type === 'email' ? 'email-address' : 'default'}
                    autoCapitalize={type === 'email' || type === 'password' ? 'none' : 'sentences'}
                    {...rest}
                />
                {renderRightAccessory()}
            </View>
            {!!error && (
                <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
            )}
        </View>
    );
}));

const styles = StyleSheet.create({
    container: { marginBottom: SPACING.md, },
    inputContainer: { flexDirection: 'row', alignItems: 'center', borderRadius: BORDER_RADIUS.md, minHeight: 56, paddingHorizontal: SPACING.md, position: 'relative', },
    floatingLabel: { position: 'absolute', left: SPACING.md, top: 16, fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.normal, },
    textInput: { flex: 1, fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.normal, paddingTop: SPACING.sm, paddingVertical: 0, height: '100%', },
    affix: { fontSize: TYPOGRAPHY.fontSize.md, fontWeight: TYPOGRAPHY.fontWeight.normal, marginLeft: SPACING.sm, },
    iconContainer: { paddingLeft: SPACING.sm, },
    errorText: { fontSize: TYPOGRAPHY.fontSize.sm, fontWeight: TYPOGRAPHY.fontWeight.normal, marginTop: SPACING.xs, marginLeft: SPACING.md, },
});

export default TextInput;