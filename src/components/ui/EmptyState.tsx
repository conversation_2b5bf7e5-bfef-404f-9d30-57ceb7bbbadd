/**
 * EmptyState - Consistent empty state component for all screens
 * Optimized with a type-safe, discriminated union props API.
 */
import React, { useMemo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { SPACING } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

import Button from './Button';

// --- Type Definitions ---

interface PresetConfig {
  icon: PhosphorIconName;
  title: string;
  description: string;
  actionLabel?: string;
  actionIcon?: PhosphorIconName;
}

const PRESET_CONFIGS: Record<string, PresetConfig> = {
  products: {
    icon: 'package',
    title: 'No Products Found',
    description: 'Get started by adding your first product.',
    actionLabel: 'Add Product',
    actionIcon: 'plus',
  },
  orders: {
    icon: 'receipt',
    title: 'No Orders Found',
    description: 'New orders from your customers will appear here.',
    actionLabel: 'Create Order',
    actionIcon: 'plus',
  },
  search: {
    icon: 'magnifying-glass',
    title: 'Search for items',
    description: "Enter a term above to find what you're looking for.",
    actionLabel: undefined,
  },
  error: {
    icon: 'warning-circle',
    title: 'Something Went Wrong',
    description: "We couldn't load the data. Please check your connection and try again.",
    actionLabel: 'Retry',
  },
  network: {
    // ✅ CORRECTED LINE: Changed 'wifi-slash' back to 'wifi-off'
    icon: 'wifi-off',
    title: 'No Internet Connection',
    description: "You're currently offline. Please check your network.",
    actionLabel: 'Retry',
  },
  activities: {
    icon: 'clipboard-text',
    title: 'No Activity Yet',
    description: 'Recent activities and events will be logged here.',
  },
};

type EmptyStateProps = {
  style?: ViewStyle;
  onActionPress?: () => void;
} & (
  | {
      type: 'products' | 'orders' | 'error' | 'network' | 'activities';
      searchQuery?: string;
      icon?: PhosphorIconName;
      title?: string;
      description?: string;
      actionLabel?: string;
      iconColor?: string;
    }
  | {
      type: 'search';
      searchQuery?: string;
      description?: string;
    }
  | {
      type: 'custom';
      icon: PhosphorIconName;
      title: string;
      description: string;
      actionLabel?: string;
      actionIcon?: PhosphorIconName;
      iconColor?: string;
      searchQuery?: string;
    }
);


const EmptyState: React.FC<EmptyStateProps> = (props) => {
  const { style, onActionPress } = props;
  const theme = useTheme();

  const config = useMemo((): PresetConfig => {
    if (props.type === 'custom') {
      return {
        icon: props.icon,
        title: props.title,
        description: props.description,
        actionLabel: props.actionLabel,
        actionIcon: props.actionIcon,
      };
    }

    if (props.type === 'search' && props.searchQuery) {
      return {
        ...PRESET_CONFIGS.search,
        title: `No results for "${props.searchQuery}"`,
        description: 'Try checking for typos or using different keywords.',
      };
    }
    
    return PRESET_CONFIGS[props.type];
  }, [props]);


  return (
    <View style={[styles.container, style]}>
      <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
        <PhosphorIcon
          name={config.icon}
          size={48}
          color={theme.colors.primary}
        />
      </View>

      <Text variant='titleLarge' style={[styles.title, { color: theme.colors.onSurface }]}>
        {config.title}
      </Text>

      <Text variant='bodyMedium' style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
        {config.description}
      </Text>

      {config.actionLabel && onActionPress && (
        <Button
          variant='primary'
          onPress={onActionPress}
          style={styles.actionButton}
          icon={config.actionIcon}
        >
          {config.actionLabel}
        </Button>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    minHeight: 300,
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  title: {
    textAlign: 'center',
    marginBottom: SPACING.sm,
    fontWeight: '600',
  },
  description: {
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 20,
    maxWidth: 300,
  },
  actionButton: {
    minWidth: 160,
  },
});

export default EmptyState;