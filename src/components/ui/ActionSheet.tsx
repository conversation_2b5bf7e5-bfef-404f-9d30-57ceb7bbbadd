import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import {
  Animated,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
  BackHandler,
  ScrollView,
  Dimensions,
  ActivityIndicator,
  InteractionManager,
  StyleProp,
  ViewStyle,
  TextStyle,
  Alert,
} from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPE DEFINITIONS ---
export interface ActionSheetOption {
  text: string;
  onPress: () => void | Promise<void>;
  icon?: PhosphorIconName;
  style?: 'cancel' | 'primary' | 'destructive' | 'default';
  disabled?: boolean;
  isAction?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
}

export interface ActionSheetProps {
  visible: boolean;
  onDismiss: () => void;
  title: string;
  description?: string;
  options: ActionSheetOption[];
  showCancel?: boolean;
  cancelText?: string;
  closeOnBackdropPress?: boolean;
  customHeader?: React.ReactNode;
}

// --- SUB-COMPONENT FOR LIST ITEMS ---
const OptionItem: React.FC<{
  option: ActionSheetOption;
  onPress: () => Promise<void>;
  isLoading: boolean;
}> = React.memo(({ option, onPress, isLoading }) => {
  const theme = useTheme();

  const textStyle = useMemo<StyleProp<TextStyle>>(() => [
      styles.optionText,
      { color: theme.colors.onSurface },
      option.style === 'destructive' && { color: theme.colors.onErrorContainer },
      option.style === 'primary' && { color: theme.colors.onPrimaryContainer },
  ], [theme, option.style]);

  const iconColor = useMemo(() => {
      if (option.style === 'destructive') return theme.colors.onErrorContainer;
      if (option.style === 'primary') return theme.colors.onPrimaryContainer;
      return theme.colors.onSurface;
  }, [theme, option.style]);
  
  return (
    <TouchableOpacity
      style={[
        styles.optionItem,
        {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.outlineVariant,
        },
        option.style === 'destructive' && { backgroundColor: theme.colors.errorContainer },
        option.style === 'primary' && { backgroundColor: theme.colors.primaryContainer },
        option.disabled && styles.optionItemDisabled,
      ]}
      onPress={onPress}
      disabled={option.disabled || isLoading}
      accessibilityLabel={option.accessibilityLabel || option.text}
      accessibilityHint={option.accessibilityHint}
      testID={option.testID}
      accessibilityRole="button"
    >
      {option.icon && <PhosphorIcon name={option.icon} color={iconColor} size={20} weight="bold" style={styles.optionIcon} />}
      <Text style={textStyle} numberOfLines={1}>{option.text}</Text>
      {isLoading && <ActivityIndicator size="small" color={theme.colors.primary} style={styles.optionLoadingIndicator} />}
    </TouchableOpacity>
  );
});

// --- MAIN ACTIONSHEET COMPONENT ---
const ActionSheet: React.FC<ActionSheetProps> = ({
  visible,
  onDismiss,
  title,
  description,
  options = [],
  showCancel = true,
  cancelText = 'Cancel',
  closeOnBackdropPress = true,
  customHeader,
}) => {
  const theme = useTheme();
  const animation = useRef(new Animated.Value(0)).current;
  const [isLoading, setIsLoading] = useState<Record<number, boolean>>({});

  useEffect(() => {
    Animated.spring(animation, {
      toValue: visible ? 1 : 0,
      useNativeDriver: true,
      bounciness: 4,
      speed: 12,
    }).start();
  }, [visible, animation]);
  
  useEffect(() => {
    if (!visible) return;
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      onDismiss();
      return true;
    });
    return () => backHandler.remove();
  }, [visible, onDismiss]);

  const handleOptionPress = useCallback(async (option: ActionSheetOption, index: number) => {
    if (isLoading[index]) return;

    setIsLoading(prev => ({ ...prev, [index]: true }));
    try {
      await option.onPress();
    } catch (error) {
      LoggingService.error(`ActionSheet option "${option.text}" failed.`, 'UI', error as Error);
      Alert.alert('Action Failed', 'The requested action could not be completed.');
    } finally {
      InteractionManager.runAfterInteractions(() => {
        setIsLoading(prev => ({ ...prev, [index]: false }));
        if (option.isAction !== false) {
          onDismiss();
        }
      });
    }
  }, [isLoading, onDismiss]);

  const handleBackdropPress = useCallback(() => {
    if (closeOnBackdropPress) {
      onDismiss();
    }
  }, [closeOnBackdropPress, onDismiss]);

  const screenHeight = Dimensions.get('window').height;

  // Don't render anything if not visible
  if (!visible) {
    return null;
  }

  return (
    <Modal
      transparent
      visible={visible}
      onRequestClose={onDismiss}
      animationType="none"
      statusBarTranslucent
    >
      <View style={styles.backdrop}>
        <TouchableOpacity
          style={styles.flex1}
          onPress={handleBackdropPress}
          activeOpacity={1}
        />
        <Animated.View
          style={[
            styles.actionSheetContainer,
            {
              transform: [{
                translateY: animation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [screenHeight, 0]
                })
              }],
              maxHeight: screenHeight * 0.8,
            },
          ]}
        >
          <View style={[styles.mainContent, { backgroundColor: theme.colors.surface }]}>
            {customHeader || (
              <View style={[styles.header, { borderBottomColor: theme.colors.outlineVariant }]}>
                <Text style={[styles.title, { color: theme.colors.onSurface }]}>{title}</Text>
                {description && <Text style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>{description}</Text>}
              </View>
            )}
            <ScrollView showsVerticalScrollIndicator={false}>
              {options.map((option, index) => (
                <OptionItem
                  key={`${option.text}-${index}`}
                  option={option}
                  isLoading={!!isLoading[index]}
                  onPress={() => handleOptionPress(option, index)}
                />
              ))}
            </ScrollView>
          </View>

          {showCancel && (
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: theme.colors.surface }]}
              onPress={onDismiss}
            >
              <Text style={[styles.cancelButtonText, { color: theme.colors.primary }]}>{cancelText}</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
    backdrop: { flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.4)', justifyContent: 'flex-end' },
    flex1: { flex: 1 },
    actionSheetContainer: { paddingHorizontal: SPACING.sm, paddingBottom: SPACING.lg },
    mainContent: { borderRadius: BORDER_RADIUS.xl, overflow: 'hidden' },
    header: { padding: SPACING.md, borderBottomWidth: StyleSheet.hairlineWidth, alignItems: 'center' },
    title: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: 'bold' },
    description: { fontSize: TYPOGRAPHY.fontSize.sm, marginTop: SPACING.xs, color: '#6c757d' },
    cancelButton: { 
        marginTop: SPACING.sm,
        borderRadius: BORDER_RADIUS.xl,
        paddingVertical: SPACING.md,
        alignItems: 'center',
    },
    cancelButtonText: { fontSize: 16, fontWeight: 'bold' },
    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.lg,
      borderBottomWidth: StyleSheet.hairlineWidth,
    },
    optionItemDisabled: { opacity: 0.5 },
    optionIcon: { marginRight: SPACING.md },
    optionText: { fontSize: 16, flex: 1 },
    optionLoadingIndicator: { marginLeft: SPACING.md },
});

export default ActionSheet;