import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

import { NotificationLog } from '../../services/notificationService'; // Import NotificationLog

interface NotificationItemProps {
  notification: NotificationLog; // Use NotificationLog
  themeColors: {
    surface: string;
    onSurface: string;
    onSurfaceDisabled: string;
    primary: string;
    elevation: string;
    border: string;
    background: string;
    onBackground: string;
  };
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  formatTimestamp: (timestamp: string) => string;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  themeColors,
  onMarkAsRead,
  onDelete,
  formatTimestamp,
}) => {
  return (
    <View style={[styles.container, { backgroundColor: themeColors.surface, borderColor: themeColors.border }]}>
      <Text style={[styles.title, { color: themeColors.onSurface }]}>{notification.title}</Text>
      <Text style={[styles.timestamp, { color: themeColors.onSurfaceDisabled }]}>
        {formatTimestamp(notification.timestamp)}
      </Text>
      {/* Add more details or actions here as needed */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  timestamp: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default NotificationItem;
