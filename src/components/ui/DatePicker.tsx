import dayjs from 'dayjs';
import React, {
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList } from 'react-native';
import { CalendarList, DateData } from 'react-native-calendars';

import { useTheme } from '../../context/ThemeContext';
import BottomSheet, {
  BottomSheetRef as CustomBottomSheetRef,
} from '../bottomsheets/BottomSheet';

import Button from './Button';

// --- Type Definitions ---

type DatePickerBaseProps = {
  onClose?: () => void;
};

interface DatePickerSingleProps extends DatePickerBaseProps {
  mode: 'single';
  date?: dayjs.Dayjs;
  onConfirm: (date: dayjs.Dayjs) => void;
}

interface DatePickerRangeProps extends DatePickerBaseProps {
  mode: 'range';
  startDate?: dayjs.Dayjs;
  endDate?: dayjs.Dayjs;
  onConfirm: (startDate: dayjs.Dayjs, endDate: dayjs.Dayjs) => void;
}

type DatePickerProps = DatePickerSingleProps | DatePickerRangeProps;

export interface DatePickerRef {
  open: () => void;
  close: () => void;
}

// --- Chips ---
const singleDateChips = [
  { label: 'After 3 Days', id: '3d' },
  { label: 'Next Week', id: '1w' },
  { label: 'Next Month', id: '1m' },
];

const rangeDateChips = [
  { label: 'Last 7 Days', id: '7d' },
  { label: 'Last Month', id: '1m' },
  { label: 'Last 90 Days', id: '90d' },
];

type Chip = { label: string; id: string };

// --- Component ---

export const DatePicker = forwardRef<DatePickerRef, DatePickerProps>((props, ref) => {
  const theme = useTheme();
  const bottomSheetRef = useRef<CustomBottomSheetRef>(null);

  const [activeChip, setActiveChip] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(props.mode === 'single' && props.date ? props.date.toDate() : null);
  const [rangeStart, setRangeStart] = useState<Date | null>(props.mode === 'range' && props.startDate ? props.startDate.toDate() : null);
  const [rangeEnd, setRangeEnd] = useState<Date | null>(props.mode === 'range' && props.endDate ? props.endDate.toDate() : null);

  useImperativeHandle(ref, () => ({
    open: () => {
      setActiveChip(null);
      bottomSheetRef.current?.present();
    },
    close: () => bottomSheetRef.current?.close(),
  }));

  const handleChipPress = (id: string) => {
    setActiveChip(id);

    if (props.mode === 'single') {
      let newDate = dayjs();
      switch (id) {
        case '3d': newDate = dayjs().add(3, 'day'); break;
        case '1w': newDate = dayjs().add(1, 'week'); break;
        case '1m': newDate = dayjs().add(1, 'month'); break;
      }
      setSelectedDate(newDate.toDate());
    } else {
      const end = dayjs();
      let start = dayjs();
      switch (id) {
        case '7d': start = end.subtract(7, 'days'); break;
        case '1m': start = end.subtract(1, 'month'); break;
        case '90d': start = end.subtract(90, 'days'); break;
      }
      setRangeStart(start.toDate());
      setRangeEnd(end.toDate());
    }
  };

  const handleConfirm = () => {
    if (props.mode === 'single' && selectedDate) {
      props.onConfirm(dayjs(selectedDate));
    } else if (props.mode === 'range' && rangeStart && rangeEnd) {
      props.onConfirm(dayjs(rangeStart), dayjs(rangeEnd));
    }
    bottomSheetRef.current?.close();
  };

  const chips = props.mode === 'single' ? singleDateChips : rangeDateChips;

  // Marked dates for CalendarList
  const markedDates: Record<string, any> = {};
  if (props.mode === 'single' && selectedDate) {
    const key = dayjs(selectedDate).format('YYYY-MM-DD');
    markedDates[key] = {
      selected: true,
      selectedColor: theme.colors.primary,
      selectedTextColor: theme.colors.onPrimary,
    };
  } else if (props.mode === 'range' && rangeStart) {
    const start = dayjs(rangeStart);
    const end = rangeEnd ? dayjs(rangeEnd) : start;
    let current = start.clone();
    while (current.isBefore(end) || current.isSame(end, 'day')) {
      const key = current.format('YYYY-MM-DD');
      markedDates[key] = {
        selected: true,
        color: theme.colors.primary,
        textColor: theme.colors.onPrimary,
        startingDay: current.isSame(start, 'day'),
        endingDay: current.isSame(end, 'day'),
      };
      current = current.add(1, 'day');
    }
  }

  const renderChip = ({ item }: { item: Chip }) => (
    <TouchableOpacity
      style={[
        styles.chip,
        {
          backgroundColor: activeChip === item.id ? theme.colors.primaryContainer : theme.colors.surfaceVariant,
          borderColor: activeChip === item.id ? theme.colors.primary : theme.colors.outline,
        },
      ]}
      onPress={() => handleChipPress(item.id)}
    >
      <Text style={[styles.chipText, { color: activeChip === item.id ? theme.colors.onPrimaryContainer : theme.colors.onSurfaceVariant }]}>
        {item.label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <BottomSheet
      ref={bottomSheetRef}
      title={props.mode === 'single' ? 'Select a Date' : 'Select a Date Range'}
      onClose={props.onClose}
      snapPoints={['75%']}
    >
      <View style={styles.contentContainer}>
        <FlatList
          data={chips}
          keyExtractor={(item) => item.id}
          renderItem={renderChip}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.chipContainer}
        />

        <CalendarList
          pastScrollRange={24}
          futureScrollRange={24}
          scrollEnabled
          showScrollIndicator
          onDayPress={(day: DateData) => {
            if (props.mode === 'single') {
              setSelectedDate(new Date(day.dateString));
              setActiveChip(null);
            } else {
              if (!rangeStart || (rangeStart && rangeEnd)) {
                setRangeStart(new Date(day.dateString));
                setRangeEnd(null);
              } else {
                const picked = new Date(day.dateString);
                if (picked >= rangeStart) {
                  setRangeEnd(picked);
                } else {
                  setRangeEnd(rangeStart);
                  setRangeStart(picked);
                }
              }
              setActiveChip(null);
            }
          }}
          markingType={props.mode === 'range' ? 'period' : 'dot'}
          markedDates={markedDates}
          theme={{
            calendarBackground: theme.colors.background,
            textSectionTitleColor: theme.colors.onSurfaceVariant,
            monthTextColor: theme.colors.onSurface,
            dayTextColor: theme.colors.onSurface,
            todayTextColor: theme.colors.primary,
          }}
        />

        <View style={styles.buttonContainer}>
          <Button
            variant="outline"
            onPress={() => bottomSheetRef.current?.close()}
            style={styles.button}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onPress={handleConfirm}
            style={styles.button}
            disabled={props.mode === 'range' && (!rangeStart || !rangeEnd)}
          >
            Confirm
          </Button>
        </View>
      </View>
    </BottomSheet>
  );
});

// --- Styles ---
const styles = StyleSheet.create({
  contentContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    flex: 1,
  },
  chipContainer: {
    paddingVertical: 8,
    gap: 8,
  },
  chip: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  chipText: {
    fontWeight: '500',
    fontSize: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    gap: 12,
  },
  button: {
    flex: 1,
  },
});

export default DatePicker;
