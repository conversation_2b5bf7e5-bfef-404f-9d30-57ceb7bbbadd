import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

import { SPACING } from '../../theme/theme';
import { StatCard, StatCardGroupProps } from '../../types';
import InfoCard from '../cards/InfoCard';

const StatCardGroup: React.FC<StatCardGroupProps> = ({
  title,
  cards = [],
  columns = 2,
  showTitle = true,
  titleStyle = {},
  containerStyle = {},
  onCardPress,
}) => {
  const handleCardPress = (card: StatCard, _index: number): void => {
    if (onCardPress) {
      onCardPress(card);
    } else if (card.onPress) {
      card.onPress();
    }
  };

  const isOddCount = cards.length % columns !== 0;
  const lastCardIndex = cards.length - 1;

  return (
    <View style={[styles.container, containerStyle]}>
      {showTitle && title && (
        <Text variant='titleLarge' style={[styles.title, titleStyle as any]}>
          {title}
        </Text>
      )}

      <View style={[styles.grid, { marginHorizontal: -2, marginVertical: 0 }]}>
        {cards.map((card, index) => {
          const isLastCard = index === lastCardIndex;
          const shouldSpanFullWidth = isOddCount && isLastCard;

          return (
            <View
              key={card.key || index}
              style={[
                styles.cardWrapper,
                {
                  width: shouldSpanFullWidth ? '100%' : `${100 / columns}%`,
                  paddingHorizontal: 2,
                  paddingVertical: 0,
                },
              ]}
            >
              <InfoCard
                type='stat'
                title={card.title}
                value={card.value}
                icon={card.icon!}
                iconColor={card.iconColor || card.color}
                elevation={card.elevation}
                onPress={() => handleCardPress(card, index)}
                style={{ marginHorizontal: 2 }}
                {...card.props}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.sm,
  },
  title: {
    fontWeight: '700',
    marginBottom: SPACING.sm,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    rowGap: SPACING.md,
  },
  cardWrapper: {},
});

export default StatCardGroup;