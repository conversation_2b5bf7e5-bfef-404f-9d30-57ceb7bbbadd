import React, { useMemo } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ViewStyle,
} from 'react-native';
import { Text, Surface, IconButton, Menu, Chip, Button } from 'react-native-paper';

import { useTheme } from '@/context/ThemeContext';
import {
  SPACING,
  BORDER_RADIUS,
  COMPONENT_SIZES,
  getBorderColor,
} from '@/theme/theme';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon, PhosphorIconName } from '@/utils/phosphorIconRegistry';

// --- Prop Types ---
interface CardProps {
  title?: string;
  subtitle?: string;
  description?: string;
  price?: number | string;
  status?: string;
  image?: string;
  icon?: PhosphorIconName;
  iconColor?: string;
  iconBackgroundColor?: string;
  statusColor?: string;
  statusBackgroundColor?: string;
  badge?: string;
  badgeColor?: string;
  onPress?: () => void;
  onLongPress?: () => void;
  actions?: { icon: PhosphorIconName; size?: number; color?: string; onPress: () => void; disabled?: boolean; style?: ViewStyle }[];
  primaryAction?: { title: string; onPress: () => void; disabled?: boolean; color?: string; textColor?: string; icon?: PhosphorIconName };
  secondaryAction?: { title: string; onPress: () => void; disabled?: boolean };
  menuItems?: { title: string; onPress: () => void; leadingIcon?: PhosphorIconName; disabled?: boolean; icon?: PhosphorIconName }[];
  menuVisible?: boolean;
  onMenuToggle?: () => void;
  onMenuDismiss?: () => void;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  disabled?: boolean;
  showImage?: boolean;
  showIcon?: boolean;
  showActions?: boolean;
  layout?: string;
}

// --- Memoized Sub-components for Performance & Readability ---

const CardHeader = React.memo(({ icon, showIcon, title, subtitle, status, badge, iconBackgroundColor, iconColor, statusBackgroundColor, statusColor, badgeColor }: CardProps) => {
  const theme = useTheme();
  if (!showIcon && !title && !subtitle) {return null;}

  return (
    <View style={styles.cardHeader}>
      {showIcon && icon && (
        <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor || `${theme.colors.primary}15` }]}>
          <PhosphorIcon name={icon} size={COMPONENT_SIZES.icon.lg} color={iconColor || theme.colors.primary} />
        </View>
      )}
      <View style={styles.cardInfo}>
        <View style={styles.titleRow}>
          {title && <Text variant='titleMedium' style={styles.titleText} numberOfLines={1}>{title}</Text>}
          {status && (
            <View style={[styles.statusBadge, { backgroundColor: statusBackgroundColor || theme.colors.primaryContainer }]}>
              <Text variant='bodySmall' style={[styles.statusText, { color: statusColor || theme.colors.onPrimaryContainer }]}>{status}</Text>
            </View>
          )}
          {badge && (
            <Chip mode='flat' textStyle={{ color: badgeColor || theme.colors.onSurfaceVariant }} style={styles.badge}>
              {badge}
            </Chip>
          )}
        </View>
        {subtitle && <Text variant='bodySmall' style={[styles.subtitleText, { color: theme.colors.onSurfaceVariant }]}>{subtitle}</Text>}
      </View>
    </View>
  );
});

const CardActions = React.memo(({ actions = [], menuItems = [], primaryAction, secondaryAction, menuVisible, onMenuToggle, onMenuDismiss }: CardProps) => {
  const theme = useTheme();
  if (actions.length === 0 && menuItems.length === 0 && !primaryAction && !secondaryAction) {return null;}

  return (
    <View style={styles.actionButtons}>
      {actions.map((action, index) => (
        <IconButton key={index} icon={action.icon} size={action.size || 20} iconColor={action.color || theme.colors.onSurfaceVariant} onPress={action.onPress} disabled={action.disabled} style={action.style} />
      ))}
      {menuItems.length > 0 && (
        <Menu visible={!!menuVisible} onDismiss={onMenuDismiss} anchor={<IconButton icon='dots-vertical' size={20} iconColor={theme.colors.onSurfaceVariant} onPress={onMenuToggle} />}>
          {menuItems.map((item, index) => <Menu.Item key={index} onPress={item.onPress} title={item.title} leadingIcon={item.icon} disabled={item.disabled} />)}
        </Menu>
      )}
      {secondaryAction && <Button mode='outlined' onPress={secondaryAction.onPress} disabled={secondaryAction.disabled} compact style={styles.actionButton}>{secondaryAction.title}</Button>}
      {primaryAction && <Button mode='contained' onPress={primaryAction.onPress} disabled={primaryAction.disabled} compact style={styles.primaryActionButton} buttonColor={primaryAction.color} textColor={primaryAction.textColor} icon={primaryAction.icon}>{primaryAction.title}</Button>}
    </View>
  );
});

// --- Main Card Component ---

const Card: React.FC<CardProps> = React.memo((props) => {
  const {
    description, price, image, onPress, onLongPress, style,
    contentStyle, disabled = false, showImage = true,
  } = props;

  const theme = useTheme();

  const surfaceStyle = useMemo(() => [
    styles.card,
    { backgroundColor: theme.colors.surface, borderColor: getBorderColor(theme) },
    disabled && styles.disabledCard,
    style,
  ], [theme, disabled, style]);

  const cardWrapperProps = useMemo(() => onPress ? {
    onPress, onLongPress, disabled, activeOpacity: 0.7,
  } : {}, [onPress, onLongPress, disabled]);

  const CardWrapper = onPress ? TouchableOpacity : View;

  return (
    <CardWrapper {...cardWrapperProps}>
      <Surface style={surfaceStyle} elevation={0}>
        {showImage && image && <Image source={{ uri: image }} style={styles.cardImage} />}
        <View style={[styles.cardContent, contentStyle]}>
          <CardHeader {...props} />
          {description && <Text variant='bodySmall' style={[styles.descriptionText, { color: theme.colors.onSurfaceVariant }]}>{description}</Text>}
          {price != null && (
            <Text variant='titleLarge' style={[styles.priceText, { color: theme.colors.onSurface }]}>
              {typeof price === 'number' ? formatCurrency(price) : price}
            </Text>
          )}
          <CardActions {...props} />
        </View>
      </Surface>
    </CardWrapper>
  );
});

const styles = StyleSheet.create({
  card: { marginBottom: SPACING.md, borderRadius: BORDER_RADIUS.xl, borderWidth: 1, overflow: 'hidden' },
  disabledCard: { opacity: 0.6 },
  cardImage: { width: '100%', height: 150, resizeMode: 'cover' },
  cardContent: { padding: SPACING.lg },
  cardHeader: { flexDirection: 'row', alignItems: 'flex-start', marginBottom: SPACING.sm },
  iconContainer: { width: COMPONENT_SIZES.avatar.md, height: COMPONENT_SIZES.avatar.md, borderRadius: BORDER_RADIUS.lg, justifyContent: 'center', alignItems: 'center', marginRight: SPACING.md },
  cardInfo: { flex: 1, justifyContent: 'center' },
  titleRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: SPACING.sm },
  titleText: { fontWeight: '600', flexShrink: 1 },
  subtitleText: { marginTop: 2 },
  descriptionText: { marginTop: SPACING.xs, marginBottom: SPACING.sm },
  priceText: { fontWeight: '700', marginTop: SPACING.sm },
  statusBadge: { paddingHorizontal: SPACING.sm, paddingVertical: SPACING.xs, borderRadius: BORDER_RADIUS.xl },
  statusText: { fontWeight: '600' },
  badge: {},
  actionButtons: { flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center', gap: SPACING.sm, marginTop: SPACING.md },
  actionButton: { borderRadius: BORDER_RADIUS.md },
  primaryActionButton: { borderRadius: BORDER_RADIUS.md },
});

export default Card;