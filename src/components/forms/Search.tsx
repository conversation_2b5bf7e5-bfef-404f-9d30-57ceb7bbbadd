import React, {
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextInput as RNTextInput,
  Keyboard,
} from 'react-native';

import { useTheme } from '../../context/ThemeContext';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

export interface SearchRef {
  focus: () => void;
  blur: () => void;
  clear: () => void;
}

interface SearchProps {
  placeholder?: string;
  onSearchChange?: (query: string) => void; 
  onSearchSubmit?: (query: string) => void;
  style?: ViewStyle;
}

const DEBOUNCE_DELAY = 300;

const Search = forwardRef<SearchRef, SearchProps>(({
  placeholder = 'Search...',
  onSearchChange,
  onSearchSubmit,
  style,
}, ref) => {
  const theme = useTheme();
  const textInputRef = useRef<RNTextInput>(null);
  const [query, setQuery] = useState('');

  useImperativeHandle(ref, () => ({
    focus: () => textInputRef.current?.focus(),
    blur: () => textInputRef.current?.blur(),
    clear: () => {
      setQuery('');
      textInputRef.current?.clear();
    },
  }));

  useEffect(() => {
    if (onSearchChange) {
      const timeoutId = setTimeout(() => {
        onSearchChange(query);
      }, DEBOUNCE_DELAY);
      return () => clearTimeout(timeoutId);
    }
  }, [query, onSearchChange]);
  
  const handleSubmit = () => {
    if (onSearchSubmit) {
      onSearchSubmit(query.trim());
      Keyboard.dismiss();
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.searchInputContainer, { backgroundColor: theme.colors.surfaceVariant }]}>
        <RNTextInput
          ref={textInputRef}
          value={query}
          onChangeText={setQuery}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          style={[styles.searchInput, { color: theme.colors.onSurface }]}
          returnKeyType="search"
          onSubmitEditing={handleSubmit}
        />
        {query ? (
          <TouchableOpacity style={styles.clearButton} onPress={() => setQuery('')}>
            <PhosphorIcon name="x-circle" weight='fill' size={20} color={theme.colors.onSurfaceVariant} />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: { position: 'relative', zIndex: 1 },
  searchInputContainer: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    height: 44,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
  },
  searchInput: { 
    flex: 1, 
    height: '100%',
    fontSize: TYPOGRAPHY.fontSize.md,
  },
  clearButton: { 
    paddingLeft: SPACING.sm,
  },
});

Search.displayName = 'Search';
export default Search;