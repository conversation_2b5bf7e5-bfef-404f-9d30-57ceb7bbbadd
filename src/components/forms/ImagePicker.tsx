import React, { useState, useMemo, useCallback, memo } from 'react';
import { View, TouchableOpacity, Image, useWindowDimensions, StyleSheet } from 'react-native';
import { Text, Surface } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';
import ActionSheet from '../ui/ActionSheet';

import { useImagePicker } from '@/hooks/useImagePicker';

// --- Interface and Constants remain the same ---
interface ImagePickerProps {
  onImageSelected: (imageUri: string) => void;
  currentImage?: string | null;
  placeholder?: string;
  size?: 'small' | 'medium' | 'large';
  aspectRatio?: number;
  quality?: number;
  allowsEditing?: boolean;
  showCamera?: boolean;
  showGallery?: boolean;
  rounded?: boolean;
}
const SIZES = { small: 80, medium: 100, large: 140 };
const ICON_SIZES = { small: 24, medium: 30, large: 36 };

// --- The Component ---
const ImagePickerComponent: React.FC<ImagePickerProps> = ({
  onImageSelected,
  currentImage,
  placeholder = 'Add Image',
  size = 'medium',
  aspectRatio = 1,
  quality = 1,
  allowsEditing = true,
  showCamera = true,
  showGallery = true,
  rounded = false,
}) => {
  const theme = useTheme();
  const { width: screenWidth } = useWindowDimensions();
  const [isPressed, setIsPressed] = useState(false);
  const [isActionSheetVisible, setActionSheetVisible] = useState(false);
  const [isRemoveConfirmVisible, setRemoveConfirmVisible] = useState(false);

  // All the complex logic is now handled by this single line!
  const { pickImage } = useImagePicker({ onImageSelected, allowsEditing, aspectRatio, quality });

  const dynamicSize = useMemo(() => {
    const scale = screenWidth < 375 ? 0.9 : screenWidth > 768 ? 1.2 : 1.0;
    return Math.round(SIZES[size] * scale);
  }, [screenWidth, size]);

  const containerStyle = useMemo(() => ({
    width: dynamicSize,
    height: dynamicSize,
    borderRadius: rounded ? dynamicSize / 2 : BORDER_RADIUS.lg,
  }), [dynamicSize, rounded]);

  const handleRemoveImage = useCallback(() => {
    onImageSelected('');
    setRemoveConfirmVisible(false);
  }, [onImageSelected]);

  const openRemoveConfirm = useCallback(() => {
    setActionSheetVisible(false);
    setRemoveConfirmVisible(true);
  }, []);

  const mainActionSheetOptions = useMemo(() => {
    const options = [];
    if (showCamera) {
      options.push({ text: 'Take Photo', onPress: () => pickImage('camera'), icon: 'camera' as PhosphorIconName, isAction: true });
    }
    if (showGallery) {
      options.push({ text: 'Choose from Gallery', onPress: () => pickImage('gallery'), icon: 'image' as PhosphorIconName, isAction: true });
    }
    if (currentImage) {
      options.push({ text: 'Remove Image', onPress: openRemoveConfirm, icon: 'trash' as PhosphorIconName, style: 'destructive' as const, isAction: true });
    }
    return options;
  }, [showCamera, showGallery, currentImage, pickImage, openRemoveConfirm]);
  
  const removeConfirmOptions = useMemo(() => ([
    { text: 'Remove Image', onPress: handleRemoveImage, icon: 'trash' as PhosphorIconName, style: 'destructive' as const, isAction: true },
  ]), [handleRemoveImage]);

  return (
    // JSX remains identical to the previous "best" version
    <>
      <TouchableOpacity
        style={[styles.container, containerStyle]}
        onPress={() => setActionSheetVisible(true)}
        onPressIn={() => setIsPressed(true)}
        onPressOut={() => setIsPressed(false)}
        activeOpacity={0.8}
      >
        {currentImage ? (
          <Surface style={[styles.contentWrapper, containerStyle, { backgroundColor: theme.colors.surface }]} elevation={isPressed ? 3 : 1}>
            <Image source={{ uri: currentImage }} style={[styles.image, containerStyle, isPressed && styles.imagePressed]} resizeMode="cover" />
            <View style={[styles.overlay, containerStyle, { opacity: isPressed ? 1 : 0 }]}>
              <View style={[StyleSheet.absoluteFill, { backgroundColor: theme.colors.primary, opacity: 0.4, borderRadius: containerStyle.borderRadius }]} />
              <PhosphorIcon name="camera" size={ICON_SIZES[size] / 1.5} color={theme.colors.onPrimary} />
            </View>
          </Surface>
        ) : (
          <Surface style={[styles.contentWrapper, containerStyle, { backgroundColor: theme.colors.surface }]} elevation={isPressed ? 3 : 1}>
            <View style={[styles.placeholder, containerStyle, { borderColor: theme.colors.outline }]}>
              <PhosphorIcon name="camera" size={ICON_SIZES[size]} color={theme.colors.primary} />
              <Text variant="bodySmall" style={[styles.placeholderText, { color: theme.colors.onSurfaceVariant }]}>
                {placeholder}
              </Text>
            </View>
          </Surface>
        )}
      </TouchableOpacity>
      <ActionSheet visible={isActionSheetVisible} onDismiss={() => setActionSheetVisible(false)} title="Select Image" options={mainActionSheetOptions} />
      <ActionSheet visible={isRemoveConfirmVisible} onDismiss={() => setRemoveConfirmVisible(false)} title="Are you sure?" description="This action cannot be undone." options={removeConfirmOptions} />
    </>
  );
};

const styles = StyleSheet.create({
  container: { alignSelf: 'flex-start', justifyContent: 'center', alignItems: 'center' },
  contentWrapper: { overflow: 'hidden', justifyContent: 'center', alignItems: 'center' },
  image: { width: '100%', height: '100%' },
  imagePressed: { opacity: 0.7 },
  overlay: { ...StyleSheet.absoluteFillObject, justifyContent: 'center', alignItems: 'center' },
  placeholder: { justifyContent: 'center', alignItems: 'center', borderWidth: 2, borderStyle: 'dashed', width: '100%', height: '100%', padding: SPACING.sm },
  placeholderText: { marginTop: SPACING.xs, textAlign: 'center', fontWeight: '500' },
});

export default memo(ImagePickerComponent);