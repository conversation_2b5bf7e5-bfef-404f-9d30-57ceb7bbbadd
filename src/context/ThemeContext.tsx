import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useMemo,
  useCallback,
} from 'react';

import LoggingService from '../services/LoggingService';
import { StorageService } from '../services/StorageService';
import {
  lightTheme,
  darkTheme,
  SPACING,
  BORDER_RADIUS,
  TYPOGRAPHY,
  SHADOWS,
  COMPONENT_SIZES,
  LAYOUT,
  ANIMATIONS,
  OPACITY,
  Z_INDEX,
  BREAKPOINTS,
  COMMON_STYLES,
} from '../theme/theme';

// Consider moving these interfaces to a separate 'theme.types.ts' file
interface ThemeColors {
  primary: string;
  primaryContainer: string;
  secondary: string;
  secondaryContainer: string;
  tertiary: string;
  tertiaryContainer: string;
  surface: string;
  surfaceVariant: string;
  background: string;
  error: string;
  errorContainer: string;
  onPrimary: string;
  onPrimaryContainer: string;
  onSecondary: string;
  onSecondaryContainer: string;
  onTertiary: string;
  onTertiaryContainer: string;
  onSurface: string;
  onSurfaceVariant: string;
  on: string;
  onVariant: string;
  onError: string;
  onErrorContainer: string;
  onBackground: string;
  outline: string;
  outlineVariant: string;
  inverse: string;
  inverseOn: string;
  inversePrimary: string;
  primaryDark: string;
  errorDark: string;
  warning: string;
  warningDark: string;
  success: string;
  successDark: string;
  info: string;
  infoDark: string;
  toast: {
    surface: string;
    surfaceVariant: string;
    success: string;
    error: string;
    warning: string;
    info: string;
    successText: string;
    errorText: string;
    warningText: string;
    infoText: string;
    defaultText: string;
  };
}

interface Theme {
  colors: ThemeColors;
  spacing: typeof SPACING;
  borderRadius: typeof BORDER_RADIUS;
  typography: typeof TYPOGRAPHY;
  shadows: typeof SHADOWS;
  componentSizes: typeof COMPONENT_SIZES;
  layout: typeof LAYOUT;
  animations: typeof ANIMATIONS;
  opacity: typeof OPACITY;
  zIndex: typeof Z_INDEX;
  breakpoints: typeof BREAKPOINTS;
  commonStyles: typeof COMMON_STYLES;
  mode: 'light' | 'dark';
}

// FIX: Added the 'export' keyword here. This is the crucial change.
export interface ThemeContextType extends Theme {
  isDarkMode: boolean;
  toggleTheme: () => Promise<void>;
  isLoading: boolean;
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const buildTheme = (baseTheme: { colors: ThemeColors; mode: 'light' | 'dark' }): Theme => ({
  ...baseTheme,
  spacing: SPACING,
  borderRadius: BORDER_RADIUS,
  typography: TYPOGRAPHY,
  shadows: SHADOWS,
  componentSizes: COMPONENT_SIZES,
  layout: LAYOUT,
  animations: ANIMATIONS,
  opacity: OPACITY,
  zIndex: Z_INDEX,
  breakpoints: BREAKPOINTS,
  commonStyles: COMMON_STYLES,
});

const preloadedLightTheme = buildTheme({ ...lightTheme, mode: 'light' });
const preloadedDarkTheme = buildTheme({ ...darkTheme, mode: 'dark' });

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadThemePreference = async (): Promise<void> => {
      try {
        const savedTheme = await StorageService.get<boolean>('darkMode');
        if (savedTheme !== null) {
          setIsDarkMode(savedTheme);
        }
      } catch (error) {
        LoggingService.warn('Error loading theme, using default.', 'THEME', error as Error);
        setIsDarkMode(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, []);

  const toggleTheme = useCallback(async (): Promise<void> => {
    const newThemeIsDark = !isDarkMode;
    setIsDarkMode(newThemeIsDark);
    try {
      await StorageService.set('darkMode', newThemeIsDark);
      LoggingService.info(`Theme switched to ${newThemeIsDark ? 'dark' : 'light'} mode`, 'THEME');
    } catch (error) {
      LoggingService.error('Error saving theme preference', 'THEME', error as Error);
      setIsDarkMode(!newThemeIsDark); // Revert on failure
    }
  }, [isDarkMode]);

  const theme = isDarkMode ? preloadedDarkTheme : preloadedLightTheme;

  const value = useMemo(
    () => ({
      ...theme,
      theme,
      isDarkMode,
      toggleTheme,
      isLoading,
    }),
    [isDarkMode, isLoading, theme, toggleTheme]
  );

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

const defaultThemeContextValue: ThemeContextType = {
  ...preloadedLightTheme,
  isDarkMode: false,
  toggleTheme: async () => {
    LoggingService.warn('toggleTheme called outside of ThemeProvider', 'THEME');
  },
  isLoading: true,
  theme: preloadedLightTheme,
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  return context || defaultThemeContextValue;
};