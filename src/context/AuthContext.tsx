import React, { ReactNode, createContext, useContext, useEffect, useReducer, useMemo } from 'react';

import { useAuthActions } from '../hooks/useAuthActions';
import { usePermissions } from '../hooks/usePermissions';
import { LoginCredentials, UserSession } from '../services/AuthService';

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserSession | null;
  error: string | null;
  sessionInfo: { timeUntilExpiry?: number; lastActivity?: number; } | null;
}

export interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
  hasRole: (requiredRole: 'admin' | 'manager' | 'user') => boolean;
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
  isManagerOrHigher: () => boolean;
}

export type AuthAction =
  | { type: 'AUTH_LOADING'; payload?: boolean }
  | { type: 'AUTH_SUCCESS'; payload: UserSession }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_SESSION_INFO'; payload: AuthState['sessionInfo'] };

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  error: null,
  sessionInfo: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_LOADING': return { ...state, isLoading: action.payload ?? true, error: null };
    case 'AUTH_SUCCESS': return { ...state, isAuthenticated: true, isLoading: false, user: action.payload, error: null };
    case 'AUTH_ERROR': return { ...state, isAuthenticated: false, isLoading: false, user: null, error: action.payload };
    case 'AUTH_LOGOUT': return { ...initialState, isLoading: false };
    case 'CLEAR_ERROR': return { ...state, error: null };
    case 'UPDATE_SESSION_INFO': return { ...state, sessionInfo: action.payload };
    default: return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const actions = useAuthActions(dispatch);
  const permissions = usePermissions(state.user);

  useEffect(() => {
    actions.checkAuthStatus();
  }, [actions.checkAuthStatus]);

  // PRODUCTION FIX: Reduce session info update frequency and add error handling
  useEffect(() => {
    if (!state.isAuthenticated) {return;}
    
    // Update session info every 5 minutes instead of every minute
    const interval = setInterval(() => {
      try {
        actions.updateSessionInfo();
      } catch (error) {
        console.warn('Session info update failed:', error);
      }
    }, 5 * 60 * 1000); // Increased from 60 seconds to 5 minutes
    
    return () => clearInterval(interval);
  }, [state.isAuthenticated, actions.updateSessionInfo]);

  const contextValue = useMemo(() => ({
    state,
    ...actions,
    ...permissions,
  }), [state, actions, permissions]);

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const useCurrentUser = (): UserSession | null => useAuth().state.user;
export const useSessionInfo = (): AuthState['sessionInfo'] => useAuth().state.sessionInfo;