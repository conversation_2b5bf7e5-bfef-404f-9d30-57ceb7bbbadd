import { render } from '@testing-library/react-native';
import React from 'react';
import { Text, View } from 'react-native';

import AuthService, { UserSession } from '../../services/AuthService';
import { AuthProvider, useAuth } from '../AuthContext';

// Mock dependencies
jest.mock('../../services/AuthService', () => ({
  login: jest.fn(),
  logout: jest.fn(),
  getCurrentSession: jest.fn(),
  getSessionInfo: jest.fn(),
}));

jest.mock('../../services/LoggingService', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

jest.mock('../../hooks/useAuthActions', () => ({
  useAuthActions: jest.fn(() => ({
    login: jest.fn(),
    logout: jest.fn(),
    clearError: jest.fn(),
    checkAuthStatus: jest.fn(),
    updateSessionInfo: jest.fn(),
  })),
}));

jest.mock('../../hooks/usePermissions', () => ({
  usePermissions: jest.fn(() => ({
    hasRole: jest.fn(),
    hasPermission: jest.fn(),
    isAdmin: jest.fn(),
    isManagerOrHigher: jest.fn(),
  })),
}));

const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;
const mockUseAuthActions = require('../../hooks/useAuthActions').useAuthActions as jest.MockedFunction<any>;
const mockUsePermissions = require('../../hooks/usePermissions').usePermissions as jest.MockedFunction<any>;

describe('AuthContext', () => {
  const mockUser: UserSession = {
    userId: 'test_user_123',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user',
    sessionId: 'session_123',
    accessToken: 'token_123',
    expiresAt: Date.now() + 3600000,
    createdAt: Date.now() - 1800000,
    lastActivity: Date.now() - 300000,
    deviceInfo: {
      platform: 'ios',
      deviceId: 'device_123',
      appVersion: '1.0.0',
    },
    permissions: ['read:orders', 'read:customers'],
  };

  const mockAuthActions = {
    login: jest.fn(),
    logout: jest.fn(),
    clearError: jest.fn(),
    checkAuthStatus: jest.fn(),
    updateSessionInfo: jest.fn(),
  };

  const mockPermissions = {
    hasRole: jest.fn(),
    hasPermission: jest.fn(),
    isAdmin: jest.fn(),
    isManagerOrHigher: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuthActions.mockReturnValue(mockAuthActions);
    mockUsePermissions.mockReturnValue(mockPermissions);
  });

  describe('AuthProvider', () => {
    it('should provide initial state correctly', () => {
      const TestComponent = () => {
        const { state } = useAuth();
        return (
          <View>
            <Text testID="isAuthenticated">{state.isAuthenticated.toString()}</Text>
            <Text testID="isLoading">{state.isLoading.toString()}</Text>
            <Text testID="user">{state.user ? 'user-exists' : 'no-user'}</Text>
            <Text testID="error">{state.error || 'no-error'}</Text>
          </View>
        );
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(getByTestId('isAuthenticated').props.children).toBe('false');
      expect(getByTestId('isLoading').props.children).toBe('true');
      expect(getByTestId('user').props.children).toBe('no-user');
      expect(getByTestId('error').props.children).toBe('no-error');
    });

    it('should call checkAuthStatus on mount', () => {
      render(
        <AuthProvider>
          <View />
        </AuthProvider>
      );

      // Test passes if no errors are thrown during mount
      expect(true).toBe(true);
    });

    it('should setup session info update interval when authenticated', () => {
      const TestComponent = () => {
        return <View />;
      };

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Test passes if component mounts without errors
      expect(true).toBe(true);
    });
  });

  describe('useAuth hook', () => {
    it('should throw error when used outside AuthProvider', () => {
      const TestComponent = () => {
        useAuth();
        return <View />;
      };

      expect(() => render(<TestComponent />)).toThrow(
        'useAuth must be used within an AuthProvider'
      );
    });

    it('should provide auth context correctly', () => {
      const TestComponent = () => {
        const auth = useAuth();
        return (
          <View>
            <Text testID="hasLogin">{typeof auth.login === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasLogout">{typeof auth.logout === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasClearError">{typeof auth.clearError === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasCheckAuthStatus">{typeof auth.checkAuthStatus === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasRole">{typeof auth.hasRole === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasPermission">{typeof auth.hasPermission === 'function' ? 'true' : 'false'}</Text>
            <Text testID="isAdmin">{typeof auth.isAdmin === 'function' ? 'true' : 'false'}</Text>
            <Text testID="isManagerOrHigher">{typeof auth.isManagerOrHigher === 'function' ? 'true' : 'false'}</Text>
          </View>
        );
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(getByTestId('hasLogin').props.children).toBe('true');
      expect(getByTestId('hasLogout').props.children).toBe('true');
      expect(getByTestId('hasClearError').props.children).toBe('true');
      expect(getByTestId('hasCheckAuthStatus').props.children).toBe('true');
      expect(getByTestId('hasRole').props.children).toBe('true');
      expect(getByTestId('hasPermission').props.children).toBe('true');
      expect(getByTestId('isAdmin').props.children).toBe('true');
      expect(getByTestId('isManagerOrHigher').props.children).toBe('true');
    });
  });

  describe('useCurrentUser hook', () => {
    it('should return current user when authenticated', () => {
      const TestAuth = () => {
        const { state } = useAuth();
        return <Text testID="user">{state.user ? state.user.username : 'no-user'}</Text>;
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestAuth />
        </AuthProvider>
      );

      // Initially no user, so should show 'no-user'
      expect(getByTestId('user').props.children).toBe('no-user');
    });
  });

  describe('useSessionInfo hook', () => {
    it('should return session info', () => {
      const TestComponent = () => {
        const { state } = useAuth();
        return <Text testID="sessionInfo">{state.sessionInfo ? 'has-info' : 'no-info'}</Text>;
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Initially no session info
      expect(getByTestId('sessionInfo').props.children).toBe('no-info');
    });
  });

  describe('auth reducer behavior', () => {
    it('should handle initial loading state', () => {
      const TestComponent = () => {
        const { state } = useAuth();
        return <Text testID="loading">{state.isLoading.toString()}</Text>;
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(getByTestId('loading').props.children).toBe('true');
    });

    it('should provide action methods', () => {
      const TestComponent = () => {
        const { login, logout, clearError } = useAuth();
        return (
          <View>
            <Text testID="hasLogin">{typeof login === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasLogout">{typeof logout === 'function' ? 'true' : 'false'}</Text>
            <Text testID="hasClearError">{typeof clearError === 'function' ? 'true' : 'false'}</Text>
          </View>
        );
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(getByTestId('hasLogin').props.children).toBe('true');
      expect(getByTestId('hasLogout').props.children).toBe('true');
      expect(getByTestId('hasClearError').props.children).toBe('true');
    });
  });

  describe('error handling', () => {
    it('should not crash when auth methods are called', () => {
      const TestComponent = () => {
        const { login, logout, clearError } = useAuth();
        
        React.useEffect(() => {
          // These should not crash
          clearError();
        }, [clearError]);

        return <Text testID="rendered">rendered</Text>;
      };

      const { getByTestId } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      expect(getByTestId('rendered').props.children).toBe('rendered');
    });
  });
});