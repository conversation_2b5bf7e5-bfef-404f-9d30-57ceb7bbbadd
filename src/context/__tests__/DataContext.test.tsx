import { render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Text, View } from 'react-native';

import masterDb from '../../services/MasterDatabaseService';
import { Customer, Product, Order } from '../../types';
import { DataProvider, useData } from '../DataContext';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  multiGet: jest.fn(),
}));

jest.mock('../../services/LoggingService', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

jest.mock('../../services/MasterDatabaseService', () => ({
  createProduct: jest.fn(),
  updateProduct: jest.fn(),
  deleteProduct: jest.fn(),
  getProducts: jest.fn(),
  createCustomer: jest.fn(),
  updateCustomer: jest.fn(),
  deleteCustomer: jest.fn(),
  getCustomers: jest.fn(),
  getOrders: jest.fn(),
  getStaff: jest.fn(),
  getSettingsByCategory: jest.fn(),
  clearAllData: jest.fn(),
}));

const mockMasterDb = masterDb as jest.Mocked<typeof masterDb>;

describe('DataContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up default mock returns
    mockMasterDb.getProducts.mockResolvedValue([]);
    mockMasterDb.getOrders.mockResolvedValue([]);
    mockMasterDb.getCustomers.mockResolvedValue([]);
    mockMasterDb.getStaff.mockResolvedValue([]);
    mockMasterDb.getSettingsByCategory.mockResolvedValue({});
  });

  describe('DataProvider', () => {
    it('should provide initial state correctly', () => {
      const TestComponent = () => {
        const { state } = useData();
        return (
          <View>
            <Text testID="products-count">{state.products.length}</Text>
            <Text testID="orders-count">{state.orders.length}</Text>
            <Text testID="customers-count">{state.customers.length}</Text>
            <Text testID="data-loaded">{state.isDataLoaded.toString()}</Text>
          </View>
        );
      };

      const { getByTestId } = render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      expect(getByTestId('products-count').props.children).toBe(0);
      expect(getByTestId('orders-count').props.children).toBe(0);
      expect(getByTestId('customers-count').props.children).toBe(0);
      expect(getByTestId('data-loaded').props.children).toBe('true');
    });

    it('should throw error when useData is used outside provider', () => {
      const TestComponent = () => {
        useData();
        return <View />;
      };

      expect(() => render(<TestComponent />)).toThrow(
        'useData must be used within a DataProvider'
      );
    });
  });

  describe('Product operations', () => {
    it('should add product successfully', async () => {
      const newProduct: any = {
        id: '1',
        name: 'Test Product',
        price: 100,
        category: 'test',
        stock: 10,
        basePrice: 100,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockMasterDb.createProduct.mockResolvedValue(newProduct);

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const result = await testActions.addProduct({
        name: 'Test Product',
        price: 100,
        category: 'test',
      });

      expect(result).toEqual(newProduct);
      expect(mockMasterDb.createProduct).toHaveBeenCalledWith({
        name: 'Test Product',
        price: 100,
        category: 'test',
        basePrice: 100,
      });
    });

    it('should update product successfully', async () => {
      const updatedProduct: any = {
        id: '1',
        name: 'Updated Product',
        price: 150,
        category: 'test',
        stock: 15,
        basePrice: 150,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const result = await testActions.updateProduct(updatedProduct);

      expect(result).toEqual(updatedProduct);
    });

    it('should delete product successfully', async () => {
      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      await testActions.deleteProduct('1');

      // Should complete without throwing
      expect(true).toBe(true);
    });
  });

  describe('Customer operations', () => {
    it('should add customer successfully', async () => {
      const newCustomer: Customer = {
        id: '1',
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '**********',
        address: 'Test Address',
        isActive: true,
        // outletId: 'outlet1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockMasterDb.createCustomer.mockResolvedValue(newCustomer);

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const result = await testActions.addCustomer({
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '**********',
        address: 'Test Address',
        isActive: true,
        // outletId: 'outlet1',
      });

      expect(result).toEqual(newCustomer);
      expect(mockMasterDb.createCustomer).toHaveBeenCalledWith({
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '**********',
        address: 'Test Address',
        isActive: true,
        // outletId: 'outlet1',
      });
    });

    it('should update customer successfully', async () => {
      const updatedCustomer: Customer = {
        id: '1',
        name: 'Updated Customer',
        email: '<EMAIL>',
        phone: '**********',
        address: 'Updated Address',
        isActive: true,
        // outletId: 'outlet1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const result = await testActions.updateCustomer(updatedCustomer);

      expect(result.name).toBe('Updated Customer');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should delete customer successfully', async () => {
      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      await testActions.deleteCustomer('1');

      // Should complete without throwing
      expect(true).toBe(true);
    });
  });

  describe('Order operations', () => {
    it('should add order successfully', async () => {
      let testActions: any;
      let testState: any;
      const TestComponent = () => {
        const { actions, state } = useData();
        testActions = actions;
        testState = state;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const orderData = {
        customerId: '1',
        customerName: 'Test Customer',
        customer_phone: '**********',
        items: [],
        subtotal: 100,
        tax: 8,
        total: 108,
        status: 'pending' as const,
        paymentStatus: 'pending' as const,
      };

      const result = await testActions.addOrder(orderData);

      expect(result).toBeDefined();
      expect(result.customerId).toBe('1');
      expect(result.total).toBe(108);
      expect(result.id).toBe('001'); // First order should get ID 001
    });

    it('should update order successfully', async () => {
      const order: any = {
        id: '1',
        customerId: '1',
        customerName: 'Test Customer',
        customer_phone: '**********',
        items: [],
        subtotal: 100,
        tax: 8,
        total: 108,
        status: 'Pending' as any,
        paymentStatus: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const result = await testActions.updateOrder(order);

      expect(result.id).toBe('1');
      expect(result.total).toBe(108);
    });

    it('should update order status successfully', async () => {
      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      await testActions.updateOrderStatus('1', 'completed');

      // Should complete without throwing
      expect(true).toBe(true);
    });

    it('should delete order successfully', async () => {
      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      await testActions.deleteOrder('1');

      // Should complete without throwing
      expect(true).toBe(true);
    });
  });

  describe('Settings operations', () => {
    it('should update settings successfully', async () => {
      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      const newSettings = {
        storeName: 'Updated Store',
        notifications: false,
      };

      await testActions.updateSettings(newSettings);

      // Should complete without throwing
      expect(true).toBe(true);
    });
  });

  

  describe('Error handling', () => {
    it('should handle product creation errors gracefully', async () => {
      mockMasterDb.createProduct.mockRejectedValue(new Error('Database error'));

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      // Should fallback to in-memory storage
      const result = await testActions.addProduct({
        name: 'Test Product',
        price: 100,
        category: 'test',
      });

      expect(result).toBeDefined();
      expect(result.name).toBe('Test Product');
    });

    it('should handle customer creation errors gracefully', async () => {
      mockMasterDb.createCustomer.mockRejectedValue(new Error('Database error'));

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      // Should fallback to in-memory storage
      const result = await testActions.addCustomer({
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '**********',
        address: 'Test Address',
        isActive: true,
        // outletId: 'outlet1',
      });

      expect(result).toBeDefined();
      expect(result.name).toBe('Test Customer');
    });

    it('should handle data clearing errors gracefully', async () => {
      mockMasterDb.clearAllData.mockRejectedValue(new Error('Clear failed'));

      let testActions: any;
      const TestComponent = () => {
        const { actions } = useData();
        testActions = actions;
        return <View />;
      };

      render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      // Should not throw error
      await expect(testActions.clearData()).resolves.not.toThrow();
    });
  });

  describe('State management', () => {
    it('should maintain state consistency across operations', async () => {
      let testActions: any;
      let testState: any;
      const TestComponent = () => {
        const { actions, state } = useData();
        testActions = actions;
        testState = state;
        return (
          <View>
            <Text testID="products-count">{state.products.length}</Text>
            <Text testID="customers-count">{state.customers.length}</Text>
          </View>
        );
      };

      const { getByTestId } = render(
        <DataProvider>
          <TestComponent />
        </DataProvider>
      );

      // Initial state
      expect(getByTestId('products-count').props.children).toBe(0);
      expect(getByTestId('customers-count').props.children).toBe(0);

      // Add data
      await testActions.addOrder({
        customerId: '1',
        customerName: 'Test Customer',
        customer_phone: '**********',
        items: [],
        subtotal: 100,
        tax: 8,
        total: 108,
        status: 'Pending' as any,
        paymentStatus: 'pending',
      });

      // State should be consistent
      expect(testState.orders.length).toBe(1);
      expect(testState.nextOrderId).toBe(2); // Should increment
    });
  });
});