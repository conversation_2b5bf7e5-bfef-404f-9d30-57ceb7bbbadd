import React, { createContext, useContext, useState, useCallback, useRef, useMemo, ReactNode } from 'react';
import { Portal } from 'react-native-paper';

import Toast from '../components/ui/Toast';

type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default';
type Position = 'top' | 'bottom';

interface ToastItem {
  id: string;
  message: string;
  type: ToastType;
  duration: number;
  position: Position;
  visible: boolean;
}

interface ToastContextType {
  showToast: (message: string, type?: ToastType, duration?: number, position?: Position) => void;
  showSuccess: (message: string, duration?: number, position?: Position) => void;
  showError: (message: string, duration?: number, position?: Position) => void;
  showWarning: (message: string, duration?: number, position?: Position) => void;
  showInfo: (message: string, duration?: number, position?: Position) => void;
  hideAllToasts: () => void;
}

const ToastContext = createContext<ToastContextType | null>(null);

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {throw new Error('useToast must be used within ToastProvider');}
  return context;
};

export const ToastProvider: React.FC<{ children: ReactNode; maxToasts?: number }> = ({ 
  children, 
  maxToasts = 3 
}) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const idCounter = useRef(0);

  const showToast = useCallback((
    message: string,
    type: ToastType = 'info',
    duration = 3000,
    position: Position = 'top'
  ) => {
    const newToast: ToastItem = {
      id: `toast_${idCounter.current++}_${Date.now()}`,
      message,
      type,
      duration,
      position,
      visible: true,
    };

    setToasts(prev => [...prev.filter(t => t.visible), newToast].slice(-maxToasts));
  }, [maxToasts]);

  const showSuccess = useCallback((msg: string, dur = 3000, pos: Position = 'top') => 
    showToast(msg, 'success', dur, pos), [showToast]);
  
  const showError = useCallback((msg: string, dur = 4000, pos: Position = 'top') => 
    showToast(msg, 'error', dur, pos), [showToast]);
  
  const showWarning = useCallback((msg: string, dur = 3500, pos: Position = 'top') => 
    showToast(msg, 'warning', dur, pos), [showToast]);
  
  const showInfo = useCallback((msg: string, dur = 3000, pos: Position = 'top') => 
    showToast(msg, 'info', dur, pos), [showToast]);

  const hideAllToasts = useCallback(() => 
    setToasts(prev => prev.map(toast => ({ ...toast, visible: false }))), []);

  const handleHide = useCallback((id: string) => 
    setToasts(prev => prev.filter(toast => toast.id !== id)), []);

  const value = useMemo(() => ({
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideAllToasts,
  }), [showToast, showSuccess, showError, showWarning, showInfo, hideAllToasts]);

  return (
    <ToastContext.Provider value={value}>
      {children}
      <Portal>
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            message={toast.message}
            type={toast.type}
            duration={toast.duration}
            visible={toast.visible}
            position={toast.position}
            onHide={() => handleHide(toast.id)}
          />
        ))}
      </Portal>
    </ToastContext.Provider>
  );
};