import React, { createContext, useContext, useReducer, useEffect, ReactNode, useMemo } from 'react';

import { useFinancialActions } from '../hooks/useFinancialActions';
import {
  ActionTypes,
  CashReconciliation,
  DerivedData,
  Expense,
  ExpectedCashData,
  FinancialAction,
  FinancialState,
  PaymentAnalytics,
  ProfitLossData,
  TaxSummary,
} from '../types/financial';

export interface FinancialContextType {
  state: FinancialState;
  derivedData: DerivedData;
  loadExpenses: (filters?: Record<string, any>) => Promise<void>;
  addExpense: (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Expense>;
  updateExpense: (id: string, updates: Partial<Expense>) => Promise<Expense>;
  deleteExpense: (id: string) => Promise<void>;
  loadReconciliations: (filters?: Record<string, any>) => Promise<void>;
  performReconciliation: (reconciliationData: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>) => Promise<CashReconciliation>;
  calculateDailyCashExpected: (date: string, outletId?: string) => Promise<ExpectedCashData>;
  generateProfitLossStatement: (startDate: string, endDate: string) => Promise<ProfitLossData>;
  getPaymentMethodAnalytics: (startDate: string, endDate: string) => Promise<PaymentAnalytics>;
  getTaxSummary: (startDate: string, endDate: string) => Promise<TaxSummary>;
  clearError: () => void;
  clearData: () => void;
}

const initialState: FinancialState = {
  expenses: [],
  reconciliations: [],
  profitLossData: null,
  paymentAnalytics: null,
  taxSummary: null,
  loading: false,
  error: null,
  lastUpdated: null,
};

const financialReducer = (state: FinancialState, action: FinancialAction): FinancialState => {
  const timestamp = new Date().toISOString();
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    case ActionTypes.SET_EXPENSES:
      return { ...state, expenses: action.payload, loading: false, error: null, lastUpdated: timestamp };
    case ActionTypes.ADD_EXPENSE:
      return { ...state, expenses: [action.payload, ...state.expenses], lastUpdated: timestamp };
    case ActionTypes.UPDATE_EXPENSE:
      return { ...state, expenses: state.expenses.map(e => (e.id === action.payload.id ? action.payload : e)), lastUpdated: timestamp };
    case ActionTypes.DELETE_EXPENSE:
      return { ...state, expenses: state.expenses.filter(e => e.id !== action.payload), lastUpdated: timestamp };
    case ActionTypes.SET_RECONCILIATIONS:
      return { ...state, reconciliations: action.payload, loading: false, error: null, lastUpdated: timestamp };
    case ActionTypes.ADD_RECONCILIATION:
      return { ...state, reconciliations: [action.payload, ...state.reconciliations], lastUpdated: timestamp };
    case ActionTypes.SET_PROFIT_LOSS:
      return { ...state, profitLossData: action.payload, loading: false, error: null, lastUpdated: timestamp };
    case ActionTypes.SET_PAYMENT_ANALYTICS:
      return { ...state, paymentAnalytics: action.payload, loading: false, error: null, lastUpdated: timestamp };
    case ActionTypes.SET_TAX_SUMMARY:
      return { ...state, taxSummary: action.payload, loading: false, error: null, lastUpdated: timestamp };
    case ActionTypes.CLEAR_DATA:
      return { ...initialState, loading: false };
    default:
      return state;
  }
};

const FinancialContext = createContext<FinancialContextType | undefined>(undefined);

export const FinancialProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(financialReducer, initialState);
  const actions = useFinancialActions(dispatch);

  useEffect(() => {
    actions.loadExpenses();
    actions.loadReconciliations();
  }, []);

  const derivedData = useMemo((): DerivedData => {
    const totalExpenses = state.expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const expensesByCategory = state.expenses.reduce((acc, expense) => {
      acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
      return acc;
    }, {} as Record<string, number>);
    const recentReconciliations = state.reconciliations.slice(0, 5);
    const reconciliationTrend = state.reconciliations.map(r => ({
      date: r.date,
      difference: r.difference,
      status: r.status,
    }));
    return { totalExpenses, expensesByCategory, recentReconciliations, reconciliationTrend };
  }, [state.expenses, state.reconciliations]);

  const contextValue = useMemo(() => ({
    state,
    derivedData,
    ...actions,
  }), [state, derivedData, actions]);

  return <FinancialContext.Provider value={contextValue}>{children}</FinancialContext.Provider>;
};

export const useFinancial = (): FinancialContextType => {
  const context = useContext(FinancialContext);
  if (context === undefined) {
    throw new Error('useFinancial must be used within a FinancialProvider');
  }
  return context;
};