import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
} from 'react';

import LoggingService from '../services/LoggingService';
import masterDb from '../services/MasterDatabaseService';
import {
  Customer,
  OrderItem,
  Product,
  Staff,
  CustomerMeasurement,
} from '../types/index';
import { Order } from '../types/index'; // Import Order from types/index
import { Warehouse, InventoryItem } from '../types/inventory';

// Settings interfaces
interface StoreHours {
  open: string;
  close: string;
  closed: boolean;
}

interface PaymentMethod {
  enabled: boolean;
  processingFee: number;
}
// --- ADD THIS AT THE TOP ---
export interface EnhancedCustomer extends Omit<Customer, 'lastOrderDate'> {
  totalSpent: number;
  totalOrders: number;
  activeOrders: number;
  lastOrderDate: Date | null;
}
export interface Settings {
  storeName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
  taxRate: number;
  currency: string;
  notifications: boolean;
  darkMode: boolean;
  autoBackup: boolean;
  storeHours: {
    monday: StoreHours;
    tuesday: StoreHours;
    wednesday: StoreHours;
    thursday: StoreHours;
    friday: StoreHours;
    saturday: StoreHours;
    sunday: StoreHours;
  };
  paymentMethods: {
    cash: PaymentMethod;
    card: PaymentMethod;
    digitalWallet: PaymentMethod;
    bankTransfer: PaymentMethod;
    giftCard: PaymentMethod;
  };
  profileImage?: string;
}

// State interface
interface DataState {
  enhancedCustomers: EnhancedCustomer[];
  products: Product[];
  orders: Order[];
  customers: Customer[];
  measurements: CustomerMeasurement[];
  settings: Settings;
  nextProductId: number;
  nextOrderId: number;
  nextCustomerId: number;
  isDataLoaded: boolean;
  // Inventory state
  warehouses: Warehouse[];
  inventoryItems: InventoryItem[];
  // Tailor shop state
  staff: Staff[];
  loading: boolean;
  error: string | null;
}

// Action types
enum ActionTypes {
  // Products
  ADD_PRODUCT = 'ADD_PRODUCT',
  UPDATE_PRODUCT = 'UPDATE_PRODUCT',
  DELETE_PRODUCT = 'DELETE_PRODUCT',
  UPDATE_STOCK = 'UPDATE_STOCK',

  // Orders
  ADD_ORDER = 'ADD_ORDER',
  UPDATE_ORDER = 'UPDATE_ORDER',
  DELETE_ORDER = 'DELETE_ORDER',
  UPDATE_ORDER_STATUS = 'UPDATE_ORDER_STATUS',

  // Customers
  ADD_CUSTOMER = 'ADD_CUSTOMER',
  UPDATE_CUSTOMER = 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER = 'DELETE_CUSTOMER',

  // Settings
  UPDATE_SETTINGS = 'UPDATE_SETTINGS',

  // Data
  LOAD_DATA = 'LOAD_DATA',
  SET_DATA_LOADED = 'SET_DATA_LOADED',
  CLEAR_DATA = 'CLEAR_DATA',

  

  // Staff
  LOAD_STAFF = 'LOAD_STAFF',
  ADD_STAFF = 'ADD_STAFF',
  UPDATE_STAFF = 'UPDATE_STAFF',
  DELETE_STAFF = 'DELETE_STAFF',

  // Loading and Error States
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',

  // Warehouse actions
  LOAD_WAREHOUSES = 'LOAD_WAREHOUSES',
  ADD_WAREHOUSE = 'ADD_WAREHOUSE',
  UPDATE_WAREHOUSE = 'UPDATE_WAREHOUSE',

  // Inventory Items
  LOAD_INVENTORY_ITEMS = 'LOAD_INVENTORY_ITEMS',
  ADD_INVENTORY_ITEM = 'ADD_INVENTORY_ITEM',
  UPDATE_INVENTORY_ITEM = 'UPDATE_INVENTORY_ITEM',
}

// Action interfaces
interface ProductAction {
  type: ActionTypes.ADD_PRODUCT | ActionTypes.UPDATE_PRODUCT;
  payload: Product;
}

interface DeleteProductAction {
  type: ActionTypes.DELETE_PRODUCT;
  payload: string;
}

interface UpdateStockAction {
  type: ActionTypes.UPDATE_STOCK;
  payload: { id: string; change: number };
}

interface OrderAction {
  type: ActionTypes.ADD_ORDER | ActionTypes.UPDATE_ORDER;
  payload: Order;
}

interface DeleteOrderAction {
  type: ActionTypes.DELETE_ORDER;
  payload: string;
}

interface UpdateOrderStatusAction {
  type: ActionTypes.UPDATE_ORDER_STATUS;
  payload: { id: string; status: string };
}

interface CustomerAction {
  type: ActionTypes.ADD_CUSTOMER | ActionTypes.UPDATE_CUSTOMER;
  payload: Customer;
}

interface DeleteCustomerAction {
  type: ActionTypes.DELETE_CUSTOMER;
  payload: string;
}

interface UpdateSettingsAction {
  type: ActionTypes.UPDATE_SETTINGS;
  payload: Partial<Settings>;
}

interface LoadDataAction {
  type: ActionTypes.LOAD_DATA;
  payload: Partial<DataState>;
}

interface SetDataLoadedAction {
  type: ActionTypes.SET_DATA_LOADED;
  payload: boolean;
}

interface ClearDataAction {
  type: ActionTypes.CLEAR_DATA;
}



// Staff Actions
interface StaffAction {
  type: ActionTypes.ADD_STAFF | ActionTypes.UPDATE_STAFF;
  payload: Staff;
}

interface DeleteStaffAction {
  type: ActionTypes.DELETE_STAFF;
  payload: string;
}

interface LoadStaffAction {
  type: ActionTypes.LOAD_STAFF;
  payload: Staff[];
}

// Removed inventory action interfaces - inventory handled separately

// Loading and Error Actions
interface SetLoadingAction {
  type: ActionTypes.SET_LOADING;
  payload: boolean;
}

interface SetErrorAction {
  type: ActionTypes.SET_ERROR;
  payload: string | null;
}

// Warehouse Actions
interface LoadWarehousesAction {
  type: ActionTypes.LOAD_WAREHOUSES;
  payload: Warehouse[];
}

interface AddWarehouseAction {
  type: ActionTypes.ADD_WAREHOUSE;
  payload: Warehouse;
}

interface UpdateWarehouseAction {
  type: ActionTypes.UPDATE_WAREHOUSE;
  payload: Warehouse;
}

// Inventory Item Actions
interface LoadInventoryItemsAction {
  type: ActionTypes.LOAD_INVENTORY_ITEMS;
  payload: InventoryItem[];
}

interface AddInventoryItemAction {
  type: ActionTypes.ADD_INVENTORY_ITEM;
  payload: InventoryItem;
}

interface UpdateInventoryItemAction {
  type: ActionTypes.UPDATE_INVENTORY_ITEM;
  payload: InventoryItem;
}

interface ProcessCustomersStartAction {
  type: ActionTypes.PROCESS_CUSTOMERS_START;
}
interface ProcessCustomersChunkAction {
  type: ActionTypes.PROCESS_CUSTOMERS_CHUNK;
  payload: EnhancedCustomer[];
}
interface ProcessCustomersCompleteAction {
  type: ActionTypes.PROCESS_CUSTOMERS_COMPLETE;
}

const enhanceCustomers = (customers: Customer[], orders: Order[]): EnhancedCustomer[] => {
  const customerOrdersMap = new Map<string, Order[]>();
  orders.forEach(order => {
    if (!customerOrdersMap.has(order.customerId)) {
      customerOrdersMap.set(order.customerId, []);
    }
    customerOrdersMap.get(order.customerId)?.push(order);
  });

  return customers.map(customer => {
    const customerOrders = customerOrdersMap.get(customer.id) || [];
    let totalSpent = 0;
    let totalOrders = 0;
    let activeOrders = 0;
    let lastOrderDate: Date | null = null;

    customerOrders.forEach(order => {
      totalSpent += order.totalAmount || 0;
      totalOrders++;
      // Assuming 'pending' and 'in_progress' are active statuses
      if (order.status === 'pending' || order.status === 'in_progress') {
        activeOrders++;
      }
      const currentOrderDate = order.orderDate ? new Date(order.orderDate) : null;
      if (currentOrderDate && (!lastOrderDate || currentOrderDate > lastOrderDate)) {
        lastOrderDate = currentOrderDate;
      }
    });

    return {
      ...customer,
      totalSpent,
      totalOrders,
      activeOrders,
      lastOrderDate,
    };
  });
};

type DataAction =
  | ProductAction
  | DeleteProductAction
  | UpdateStockAction
  | OrderAction
  | ProductAction
  | DeleteProductAction
  | UpdateStockAction
  | OrderAction
  | DeleteOrderAction
  | UpdateOrderStatusAction
  | CustomerAction
  | DeleteCustomerAction
  | UpdateSettingsAction
  | LoadDataAction
  | SetDataLoadedAction
  | ClearDataAction
  | StaffAction
  | DeleteStaffAction
  | LoadStaffAction
  | SetLoadingAction
  | SetErrorAction
  | LoadWarehousesAction
  | AddWarehouseAction
  | UpdateWarehouseAction
  | LoadInventoryItemsAction
  | AddInventoryItemAction
  | UpdateInventoryItemAction;

// Initial state with completely empty arrays - no dummy data
const initialState: DataState = {
  enhancedCustomers: [],
  products: [],
  orders: [],
  customers: [],
  measurements: [],
  settings: {
    storeName: 'TailorZap',
    ownerName: 'Business Owner',
    email: '<EMAIL>',
    phone: '***********',
    address: 'Business Address',
    taxRate: 0.08,
    currency: '৳',
    notifications: true,
    darkMode: false,
    autoBackup: true,
    storeHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true },
    },
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      card: { enabled: true, processingFee: 2.9 },
      digitalWallet: { enabled: false, processingFee: 2.5 },
      bankTransfer: { enabled: false, processingFee: 1.0 },
      giftCard: { enabled: true, processingFee: 0 },
    },
  },
  nextProductId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  isDataLoaded: false,
  // Inventory initial state
  warehouses: [],
  inventoryItems: [],
  // Tailor shop state
  staff: [],
  loading: false,
  error: null,
};

// Reducer function
const dataReducer = (state: DataState, action: DataAction): DataState => {
  switch (action.type) {
    case ActionTypes.ADD_PRODUCT:
      return {
        ...state,
        products: [
          ...(state.products || []),
          { ...action.payload, id: state.nextProductId.toString() },
        ],
        nextProductId: state.nextProductId + 1,
      };

    case ActionTypes.UPDATE_PRODUCT:
      return {
        ...state,
        products: (state.products || []).map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
      };

    case ActionTypes.DELETE_PRODUCT:
      return {
        ...state,
        products: (state.products || []).filter(product => product.id !== action.payload),
      };

    case ActionTypes.UPDATE_STOCK:
      return {
        ...state,
        products: (state.products || []).map(product =>
          product.id === action.payload.id ? { ...product } : product
        ),
      };

    case ActionTypes.ADD_ORDER:
      const newOrderId = String(state.nextOrderId).padStart(3, '0');
      return {
        ...state,
        orders: [...(state.orders || []), { ...action.payload, id: newOrderId }],
        nextOrderId: state.nextOrderId + 1,
      };

    case ActionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: (state.orders || []).map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
      };

    case ActionTypes.DELETE_ORDER:
      return {
        ...state,
        orders: (state.orders || []).filter(order => order.id !== action.payload),
      };

    case ActionTypes.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: (state.orders || []).map(order =>
          order.id === action.payload.id
            ? { ...order, status: action.payload.status as any }
            : order
        ),
      };

    case ActionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [
          ...(state.customers || []),
          { ...action.payload, id: state.nextCustomerId.toString() },
        ],
        nextCustomerId: state.nextCustomerId + 1,
      };

    case ActionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: (state.customers || []).map(customer =>
          customer.id === action.payload.id ? { ...customer, ...action.payload } : customer
        ),
      };

    case ActionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: (state.customers || []).filter(customer => customer.id !== action.payload),
      };

    case ActionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case ActionTypes.LOAD_DATA:
      return {
        ...state,
        ...action.payload,
        isDataLoaded: true,
      };

    case ActionTypes.SET_DATA_LOADED:
      return {
        ...state,
        isDataLoaded: action.payload,
      };

    case ActionTypes.CLEAR_DATA:
      return {
        ...initialState,
        isDataLoaded: false,
      };

    case ActionTypes.LOAD_STAFF:
      return {
        ...state,
        staff: action.payload,
      };

    case ActionTypes.ADD_STAFF:
      return {
        ...state,
        staff: [...(state.staff || []), action.payload],
      };

    case ActionTypes.UPDATE_STAFF:
      return {
        ...state,
        staff: (state.staff || []).map(staffMember =>
          staffMember.id === action.payload.id ? action.payload : staffMember
        ),
      };

    case ActionTypes.DELETE_STAFF:
      return {
        ...state,
        staff: (state.staff || []).filter(staffMember => staffMember.id !== action.payload),
      };

    // Inventory functionality moved to separate context

    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };

    case ActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };

    case ActionTypes.LOAD_WAREHOUSES:
      return {
        ...state,
        warehouses: action.payload,
      };

    case ActionTypes.ADD_WAREHOUSE:
      return {
        ...state,
        warehouses: [...(state.warehouses || []), action.payload],
      };

    case ActionTypes.UPDATE_WAREHOUSE:
      return {
        ...state,
        warehouses: (state.warehouses || []).map(warehouse =>
          warehouse.id === action.payload.id ? action.payload : warehouse
        ),
      };

    case ActionTypes.LOAD_INVENTORY_ITEMS:
      return {
        ...state,
        inventoryItems: action.payload,
      };

    case ActionTypes.ADD_INVENTORY_ITEM:
      return {
        ...state,
        inventoryItems: [...(state.inventoryItems || []), action.payload],
      };

    case ActionTypes.UPDATE_INVENTORY_ITEM:
      return {
        ...state,
        inventoryItems: (state.inventoryItems || []).map(item =>
          item.id === action.payload.id ? action.payload : item
        ),
      };

    

    default:
      return state;
  }
};

// Context interface
interface DataContextType {
  state: DataState;
  actions: {
    // Products
    addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Product>;
    updateProduct: (product: Product) => Promise<Product>;
    deleteProduct: (id: string) => Promise<void>;
    updateStock: (id: string, change: number) => Promise<void>;

    // Orders
    addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Order>;
    updateOrder: (order: Order) => Promise<Order>;
    deleteOrder: (id: string) => Promise<void>;
    updateOrderStatus: (id: string, status: string) => Promise<void>;

    // Customers
    addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Customer>;
    updateCustomer: (customer: Customer) => Promise<Customer>;
    deleteCustomer: (id: string) => Promise<void>;

    // Settings
    updateSettings: (settings: Partial<Settings>) => Promise<void>;

    

    // Data Management
    
    clearData: () => Promise<void>;
    restoreData: (backupData: any) => Promise<void>;
    forceSave: () => Promise<void>;
    reloadData: () => Promise<void>;
    resetAllData: () => Promise<void>;

    // Inventory functionality for compatibility
    getInventoryItems: () => InventoryItem[];
    addInventoryItem: (item: any) => Promise<any>;
    updateInventoryItem: (id: string, updates: any) => Promise<any>;
    deleteInventoryItem: (id: string) => Promise<void>;
    loadDashboardData: () => Promise<void>;
    setSearchQuery: (query: string) => void;
    setActiveFilters: (filters: any) => void;
    clearFilters: () => void;
    stockIn: (itemId: string, warehouseId: string, quantity: number, unit: string, performedBy: string, reference?: string, note?: string) => Promise<any>;
    stockOut: (itemId: string, warehouseId: string, quantity: number, unit: string, performedBy: string, reference?: string, note?: string) => Promise<any>;
    getStockByItem: (itemId: string) => Promise<any[]>;
    getStockByWarehouse: (warehouseId: string) => Promise<any[]>;
    transferStock: (itemId: string, fromWarehouseId: string, toWarehouseId: string, quantity: number, unit: string, performedBy: string) => Promise<any>;
    getTransactionHistory: (filters?: any) => Promise<any[]>;
    loadTransactions: (filters?: any) => Promise<void>;
    loadWarehouses: () => Promise<void>;
    addWarehouse: (warehouse: any) => Promise<any>;
    updateWarehouse: (id: string, updates: any) => Promise<any>;
    loadItems: (filters?: any) => Promise<void>;
    getItemById: (id: string) => Promise<any>;

    // Enterprise monitoring methods
    getPerformanceScore: () => number;
    getHealthStatus: () => any;
    generateHealthReport: () => any;
    getOverallAppScore: () => any;
    generateSampleData: () => Promise<void>;

    // Measurement actions - simplified
    saveMeasurement: (measurement: any) => Promise<any>;
    updateMeasurement: (id: string, updates: any) => Promise<any>;
    deleteMeasurement: (id: string) => Promise<void>;
    getMeasurementHistory: (customerId: string, serviceType?: any) => Promise<any[]>;
    getTemplateByServiceType: (serviceType: any) => Promise<any | null>;

    // Staff actions
    createStaff: (staff: any) => Promise<Staff>;
    addStaff: (staffData: Omit<Staff, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Staff>;
    updateStaff: (id: string, updates: Partial<Staff>) => Promise<Staff>;
    deleteStaff: (id: string) => Promise<void>;
    getStaffById: (id: string) => Promise<Staff | null>;
    getStaffByRole: (role: string) => Promise<Staff[]>;
    getActiveStaff: () => Promise<Staff[]>;
    getAvailableStaff: (outletId: string, skill?: string) => Promise<Staff[]>;

    // Staff analytics and performance
    updateWorkload: (staffId: string, workload: any) => Promise<void>;
    updatePerformance: (staffId: string, performance: any) => Promise<void>;
    getStaffPerformance: (staffId: string, period: string) => Promise<any>;
    analyzeWorkload: (staffId: string) => Promise<any>;
    generatePerformanceReport: (staffId: string) => Promise<any>;
  };
}

// Create context
const DataContext = createContext<DataContextType | undefined>(undefined);

// Provider component with performance optimizations
interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  LoggingService.debug('DataProvider rendered', 'DATA_CONTEXT');
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Note: Database initialization is handled by App.tsx to avoid race conditions
  // DataContext will use Master Database Service with AsyncStorage fallback

  // PRODUCTION FIX: Load data from unified database on app start - completely non-blocking
  useEffect(() => {
    // Set data as loaded immediately to prevent any blocking
    dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });

    // PRODUCTION FIX: Load data in background with significant delay to ensure UI is ready
    const backgroundLoader = () => {
      setTimeout(() => {
        loadData().catch(error => {
          LoggingService.warn('Background data loading failed - app will use empty state', 'DATA_CONTEXT', error);
        });
      }, 500); // Increased delay to ensure screens are fully rendered
    };
    
    backgroundLoader();
  }, []);

  

  // PRODUCTION FIX: Enhanced data loading with timeout protection and error recovery
  const loadData = useCallback(async () => {
    const startTime = Date.now();
    try {
      LoggingService.info('Loading data from storage...', 'DATA_CONTEXT');
      
      // PRODUCTION FIX: Add timeout protection to prevent hanging
      const loadTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Data load timeout')), 15000); // 15 second timeout
      });

      const loadPromise = loadDataInternal();
      
      await Promise.race([loadPromise, loadTimeout]);
      
      const loadTime = Date.now() - startTime;
      LoggingService.info(`Data loaded successfully in ${loadTime}ms`, 'DATA_CONTEXT');
    } catch (error) {
      LoggingService.error('Error loading data', 'DATA_CONTEXT', error as Error);
      // PRODUCTION FIX: Set minimal state to prevent app crashes
      recoverFromLoadError();
    }
  }, []);

  // PRODUCTION FIX: Internal data loading logic
  const loadDataInternal = async () => {
    // Try to load from Master Database Service first, fallback to AsyncStorage
    try {
      await loadDataFromMasterDatabaseSafely();
    } catch (error) {
      LoggingService.debug('Master Database not available, using AsyncStorage', 'DATA_CONTEXT');
      await loadDataFromAsyncStorageSafely();
    }
  };

  // PRODUCTION FIX: Recover from data load errors
  const recoverFromLoadError = () => {
    try {
      LoggingService.warn('Recovering from data load error - setting minimal state', 'DATA_CONTEXT');
      
      // Set minimal working state to prevent app crashes
      dispatch({ 
        type: ActionTypes.LOAD_DATA, 
        payload: {
          products: [],
          orders: [],
          customers: [],
          staff: [],
          nextProductId: 1,
          nextOrderId: 1,
          nextCustomerId: 1,
        }
      });
      
      dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });
    } catch (error) {
      LoggingService.error('Failed to recover from data load error', 'DATA_CONTEXT', error as Error);
    }
  };

  // PRODUCTION FIX: Safe AsyncStorage loading with timeout protection
  const loadDataFromAsyncStorage = async () => {
    try {
      let products: Product[] = [];
      let orders: Order[] = [];
      let customers: Customer[] = [];
      let staff: Staff[] = [];
      let nextProductId = initialState.nextProductId;
      let nextOrderId = initialState.nextOrderId;
      let nextCustomerId = initialState.nextCustomerId;

      // Consolidated load (preferred)
      const consolidatedData = await AsyncStorage.getItem('tailorShop/data');
      if (consolidatedData) {
        try {
          const parsed = JSON.parse(consolidatedData) as Partial<DataState>;
          products = parsed.products ?? [];
          orders = parsed.orders ?? [];
          customers = parsed.customers ?? [];
          staff = parsed.staff ?? [];
          nextProductId = parsed.nextProductId ?? initialState.nextProductId;
          nextOrderId = parsed.nextOrderId ?? initialState.nextOrderId;
          nextCustomerId = parsed.nextCustomerId ?? initialState.nextCustomerId;
        } catch (e) {
          LoggingService.warn(
            'Failed to parse consolidated tailorShopData; falling back to legacy keys',
            'DATA_CONTEXT',
            e as Error
          );
        }
      }

      // Backward-compatible legacy loads if consolidated not present or failed to parse
      if (!consolidatedData) {
        const [productsData, ordersData, customersData, staffData] = await Promise.all(
          [
            AsyncStorage.getItem('products'),
            AsyncStorage.getItem('orders'),
            AsyncStorage.getItem('customers'),
            AsyncStorage.getItem('staff'),
          ]
        );

        if (productsData) products = JSON.parse(productsData);
        if (ordersData) orders = JSON.parse(ordersData) as Order[];
        if (customersData) customers = JSON.parse(customersData);
        if (staffData) staff = JSON.parse(staffData);
      }

      // Load settings from dedicated key (wins over consolidated if both exist)
      const settingsData = await AsyncStorage.getItem('tailorShop/settings');
      if (settingsData) {
        const settings = JSON.parse(settingsData);
        dispatch({ type: ActionTypes.UPDATE_SETTINGS, payload: settings });
      }

      // Enhance customers after all data is loaded
      const enhancedCustomers = enhanceCustomers(customers, orders);

      const payload: Partial<DataState> = {
        products,
        orders,
        customers,
        enhancedCustomers,
        staff,
        nextProductId,
        nextOrderId,
        nextCustomerId,
      };
      dispatch({ type: ActionTypes.LOAD_DATA, payload });

      // Set data as loaded
      dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });
    } catch (error) {
      LoggingService.error(
        'Error loading data from AsyncStorage fallback',
        'DATA_CONTEXT',
        error as Error
      );
    }
  };

  // PRODUCTION FIX: Safe AsyncStorage loading with timeout protection
  const loadDataFromAsyncStorageSafely = async () => {
    try {
      const storageTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('AsyncStorage load timeout')), 8000); // 8 second timeout
      });

      const storagePromise = loadDataFromAsyncStorage();
      
      await Promise.race([storagePromise, storageTimeout]);
    } catch (error) {
      LoggingService.warn('AsyncStorage load timeout or error - using empty state', 'DATA_CONTEXT', error as Error);
      // Set empty state to prevent app crashes
      dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });
    }
  };

  // Master Database Service functions with timeout protection
  const loadDataFromMasterDatabase = async () => {
    try {
      LoggingService.info('Loading data from Master Database Service...', 'DATA_CONTEXT');

      const [products, orders, customers, staff, settings] = await Promise.all([
        masterDb.getProducts(),
        masterDb.getOrders(),
        masterDb.getCustomers({ is_active: true }),
        masterDb.getStaff({ is_active: true }),
        masterDb.getSettingsByCategory('app'),
      ]);

      const enhancedCustomers = enhanceCustomers(customers || [], orders || []);

      // SOLUTION: Combine all data into a single payload
      const payload: Partial<DataState> = {
          products: products || [],
          orders: (orders as Order[]) || [],
          customers: customers || [],
          enhancedCustomers: enhancedCustomers,
          staff: staff || [],
      };
      
      // Dispatch only ONCE with all the data
      dispatch({ type: ActionTypes.LOAD_DATA, payload });

      if (Object.keys(settings).length > 0) {
        dispatch({ type: ActionTypes.UPDATE_SETTINGS, payload: settings });
      }

      dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });
      LoggingService.info('Data loaded successfully from Master Database Service', 'DATA_CONTEXT');
    } catch (error) {
      LoggingService.error('Error loading data from Master Database Service', 'DATA_CONTEXT', error as Error);
      throw error; // Re-throw to trigger fallback
    }
  };

  // PRODUCTION FIX: Safe Master Database loading with timeout protection
  const loadDataFromMasterDatabaseSafely = async () => {
    try {
      const dbTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Master Database load timeout')), 10000); // 10 second timeout
      });

      const dbPromise = loadDataFromMasterDatabase();
      
      await Promise.race([dbPromise, dbTimeout]);
    } catch (error) {
      LoggingService.warn('Master Database load timeout or error - falling back to AsyncStorage', 'DATA_CONTEXT', error as Error);
      throw error; // Re-throw to trigger AsyncStorage fallback
    }
  };

  const saveDataToMasterDatabase = async () => {
    try {
      LoggingService.debug('Saving data to Master Database Service...', 'DATA_CONTEXT');

      // Save settings
      const settingsEntries = Object.entries(state.settings);
      for (const [key, value] of settingsEntries) {
        await masterDb.setSetting('app', key, value);
      }

      LoggingService.info('Data saved successfully to Master Database Service', 'DATA_CONTEXT');
    } catch (error) {
      LoggingService.error('Error saving data to Master Database Service', 'DATA_CONTEXT', error as Error);
      throw error; // Re-throw to trigger fallback
    }
  };

  // PRODUCTION FIX: Enhanced data saving with timeout protection
  const saveData = useCallback(async () => {
    try {
      LoggingService.debug('Saving data to storage...', 'DATA_CONTEXT');
      
      // PRODUCTION FIX: Add timeout protection to prevent hanging
      const saveTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Data save timeout')), 10000); // 10 second timeout
      });

      const savePromise = saveDataInternal();
      
      await Promise.race([savePromise, saveTimeout]);
      
      LoggingService.info('Data saved successfully', 'DATA_CONTEXT');
    } catch (error) {
      // PRODUCTION FIX: Don't crash app on save errors
      LoggingService.warn('Data save timeout or error - continuing without blocking', 'DATA_CONTEXT', error as Error);
    }
  }, [
    state.settings,
    state.isDataLoaded,
    state.products,
    state.orders,
    state.customers,
    state.staff,
    state.nextProductId,
    state.nextOrderId,
    state.nextCustomerId,
  ]);

  // PRODUCTION FIX: Internal data saving logic
  const saveDataInternal = async () => {
    // Try to save to Master Database Service first, fallback to AsyncStorage
    try {
      await saveDataToMasterDatabaseSafely();
    } catch (error) {
      LoggingService.debug('Master Database not available, using AsyncStorage', 'DATA_CONTEXT');
      await saveDataToAsyncStorageSafely();
    }
  };

  // PRODUCTION FIX: Safe Master Database saving
  const saveDataToMasterDatabaseSafely = async () => {
    try {
      const dbSaveTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Master Database save timeout')), 5000); // 5 second timeout
      });

      const dbSavePromise = saveDataToMasterDatabase();
      
      await Promise.race([dbSavePromise, dbSaveTimeout]);
    } catch (error) {
      LoggingService.warn('Master Database save timeout or error - falling back to AsyncStorage', 'DATA_CONTEXT', error as Error);
      throw error; // Re-throw to trigger AsyncStorage fallback
    }
  };

  // PRODUCTION FIX: Safe AsyncStorage saving
  const saveDataToAsyncStorageSafely = async () => {
    try {
      const storageSaveTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('AsyncStorage save timeout')), 3000); // 3 second timeout
      });

      const storageSavePromise = saveDataToAsyncStorage();
      
      await Promise.race([storageSavePromise, storageSaveTimeout]);
    } catch (error) {
      LoggingService.warn('AsyncStorage save timeout or error - data not persisted', 'DATA_CONTEXT', error as Error);
      // Don't throw error to prevent app crashes
    }
  };

  const saveDataToAsyncStorage = async () => {
    try {
      // Consolidated snapshot for all app data
      const snapshot: Partial<DataState> = {
        products: state.products,
        orders: state.orders,
        customers: state.customers,
        
        staff: state.staff,
        // Next IDs
        nextProductId: state.nextProductId,
        nextOrderId: state.nextOrderId,
        nextCustomerId: state.nextCustomerId,
      };
      await AsyncStorage.setItem('tailorShop/data', JSON.stringify(snapshot));

      LoggingService.info('Data snapshot saved to AsyncStorage successfully', 'DATA_CONTEXT');
    } catch (error) {
      LoggingService.error(
        'Error saving data to AsyncStorage fallback',
        'DATA_CONTEXT',
        error as Error
      );
      // Don't crash the app on save errors
    }
  };

  // Memoized action creators for performance with SQLite optimization
  const actions = useMemo(
    () => ({
      // Products - using Master Database Service
      addProduct: async (
        product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>
      ): Promise<Product> => {
        try {
          // Try Master Database Service first
          const savedProduct = await masterDb.createProduct({
            ...product,
            basePrice: product.price || 0,
            category: product.category || 'general',
          });

          dispatch({ type: ActionTypes.ADD_PRODUCT, payload: savedProduct });
          return savedProduct;
        } catch (error) {
          // Fallback to in-memory storage
          LoggingService.debug('Master Database not available, using in-memory storage', 'DATA_CONTEXT');
          const savedProduct: Product = {
            ...product,
            id: state.nextProductId.toString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          dispatch({ type: ActionTypes.ADD_PRODUCT, payload: savedProduct });
          return savedProduct;
        }
      },

      updateProduct: async (product: Product): Promise<Product> => {
        const updatedProduct: Product = {
          ...product,
          updatedAt: new Date().toISOString(),
        };
        dispatch({ type: ActionTypes.UPDATE_PRODUCT, payload: updatedProduct });
        return updatedProduct;
      },

      deleteProduct: async (id: string): Promise<void> => {
        // Remove from state only
        dispatch({ type: ActionTypes.DELETE_PRODUCT, payload: id });
      },

      updateStock: async (id: string, change: number): Promise<void> => {
        // Get current product from state - note: stock is not part of Product interface in UnifiedDatabaseService
        const product = state.products.find(p => p.id === id);
        if (product) {
          const updatedProduct = {
            ...product,
            updatedAt: new Date().toISOString(),
          };
          dispatch({ type: ActionTypes.UPDATE_PRODUCT, payload: updatedProduct });
        }
      },

      // Orders - now in memory only
      addOrder: async (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> => {
        const savedOrder: Order = {
          ...order,
          id: String(state.nextOrderId).padStart(3, '0'),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        dispatch({ type: ActionTypes.ADD_ORDER, payload: savedOrder });
        return savedOrder;
      },

      updateOrder: async (order: Order): Promise<Order> => {
        const updatedOrder: Order = {
          ...order,
          updatedAt: new Date().toISOString(),
        };
        dispatch({ type: ActionTypes.UPDATE_ORDER, payload: updatedOrder });
        return updatedOrder;
      },

      deleteOrder: async (id: string): Promise<void> => {
        // Remove from state only
        dispatch({ type: ActionTypes.DELETE_ORDER, payload: id });
      },

      updateOrderStatus: async (id: string, status: string): Promise<void> => {
        dispatch({ type: ActionTypes.UPDATE_ORDER_STATUS, payload: { id, status } });
      },

      // Customers - using Master Database Service
      addCustomer: async (
        customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>
      ): Promise<Customer> => {
        try {
          // Try Master Database Service first
          const savedCustomer = await masterDb.createCustomer({
            ...customer,
          });

          dispatch({ type: ActionTypes.ADD_CUSTOMER, payload: savedCustomer });
          return savedCustomer;
        } catch (error) {
          // Fallback to in-memory storage
          LoggingService.debug('Master Database not available, using in-memory storage', 'DATA_CONTEXT');
          const savedCustomer: Customer = {
            ...customer,
            id: state.nextCustomerId.toString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          dispatch({ type: ActionTypes.ADD_CUSTOMER, payload: savedCustomer });
          return savedCustomer;
        }
      },

      updateCustomer: async (customer: Customer): Promise<Customer> => {
        try {
          // Try Master Database Service first
          const updatedCustomer = await masterDb.updateCustomer(customer.id, {
            ...customer,
            updatedAt: new Date().toISOString(),
          });

          if (updatedCustomer) {
            dispatch({ type: ActionTypes.UPDATE_CUSTOMER, payload: updatedCustomer });
            LoggingService.info(`Customer ${customer.id} updated in Master Database.`, 'DATA_CONTEXT');
            return updatedCustomer;
          }
        } catch (error) {
          LoggingService.debug(`Failed to update customer ${customer.id} in Master Database, using fallback.`, 'DATA_CONTEXT', error as Error);
        }

        // Fallback to in-memory storage with AsyncStorage persistence
        const updatedCustomer: Customer = {
          ...customer,
          updatedAt: new Date().toISOString(),
        };
        dispatch({ type: ActionTypes.UPDATE_CUSTOMER, payload: updatedCustomer });
        await saveData(); // Persist the changes to AsyncStorage
        return updatedCustomer;
      },

      deleteCustomer: async (id: string): Promise<void> => {
        try {
          // Try Master Database Service first
          await masterDb.delete('customers', id);
          LoggingService.info(`Customer ${id} deleted from Master Database.`, 'DATA_CONTEXT');
        } catch (error) {
          LoggingService.debug(`Failed to delete customer ${id} from Master Database, attempting AsyncStorage fallback.`, 'DATA_CONTEXT', error as Error);
          // Fallback to in-memory state and then save to AsyncStorage
          // The actual deletion from AsyncStorage will happen when saveData() is called.
        }
        dispatch({ type: ActionTypes.DELETE_CUSTOMER, payload: id });
        await saveData(); // Persist the changes to AsyncStorage (and MasterDB if available)
      },

      

      // Settings
      updateSettings: async (settings: Partial<Settings>) => {
        LoggingService.debug('updateSettings: Dispatching UPDATE_SETTINGS action', 'DATA_CONTEXT', settings);
        dispatch({ type: ActionTypes.UPDATE_SETTINGS, payload: settings });
        LoggingService.debug('updateSettings: Awaiting saveData()', 'DATA_CONTEXT');
        await saveData();
        LoggingService.debug('updateSettings: saveData() completed', 'DATA_CONTEXT');
      },

      // Data Management
      

      clearData: async () => {
        try {
          await masterDb.clearAllData(); // Clear data from both SQLite and AsyncStorage fallback
          dispatch({ type: ActionTypes.CLEAR_DATA });
          LoggingService.info('All data cleared successfully', 'DATA_CONTEXT');
        } catch (error) {
          LoggingService.error('Error clearing data', 'DATA_CONTEXT', error as Error);
        }
      },

      restoreData: async (backupData: any) => {
        try {
          await AsyncStorage.setItem('tailorShop/data', JSON.stringify(backupData));
          LoggingService.info('Data restored successfully from backup', 'DATA_CONTEXT');
          dispatch({ type: ActionTypes.LOAD_DATA, payload: backupData });
        } catch (error) {
          LoggingService.error(
            'Failed to restore data from backup',
            'DATA_CONTEXT',
            error as Error
          );
          throw error;
        }
      },

      // Force save data immediately (for critical operations)
      forceSave: async () => {
        await saveData();
      },

      // Reload data from database
      reloadData: async () => {
        await loadData();
      },

      resetAllData: async () => {
        try {
          LoggingService.info('Resetting all data and database', 'DATA_CONTEXT');
          await masterDb.resetDatabase(); // Drop and recreate tables
          await AsyncStorage.clear(); // Clear all AsyncStorage
          dispatch({ type: ActionTypes.CLEAR_DATA }); // Reset Redux state
          LoggingService.info('All data and database reset successfully', 'DATA_CONTEXT');
        } catch (error) {
          LoggingService.error('Failed to reset all data', 'DATA_CONTEXT', error as Error);
          throw error;
        }
      },

      // Inventory functionality moved to separate context

      // Enterprise monitoring methods
      getPerformanceScore: () => {
        return 0; // Placeholder - performance monitoring removed
      },

      getHealthStatus: () => {
        return { status: 'OK', message: 'All services operational' }; // Placeholder - health monitoring removed
      },

      generateHealthReport: () => {
        return { report: 'Health report not implemented' }; // Placeholder - health monitoring removed
      },

      getOverallAppScore: () => {
        LoggingService.info('Overall app score calculated', 'ENTERPRISE', {
          performanceScore: 0,
          healthScore: 0,
          overallScore: 0,
        });

        return {
          overallScore: 0, // Placeholder
          breakdown: {
            performance: 0, // Placeholder
            health: 0, // Placeholder
          },
          grade: 'N/A', // Placeholder
          status: 'N/A', // Placeholder
        };
      },

      // Generate comprehensive sample data
      generateSampleData: async () => {
        try {
          LoggingService.info('Generating comprehensive sample data', 'DATA_CONTEXT');

          // Product categories and names
          const categories = ['Shirts', 'Pants', 'Dresses', 'Suits', 'Jackets', 'Skirts', 'Blouses', 'Coats'];
          const productNames = [
            'Cotton Shirt', 'Silk Blouse', 'Wool Jacket', 'Linen Pants', 'Denim Jeans',
            'Evening Dress', 'Business Suit', 'Casual Shirt', 'Formal Dress', 'Winter Coat',
            'Summer Dress', 'Polo Shirt', 'Blazer', 'Trousers', 'Cardigan', 'Sweater',
            'Maxi Dress', 'Pencil Skirt', 'Chinos', 'Hoodie', 'Tank Top', 'Midi Dress',
            'Tuxedo', 'Overcoat', 'Vest', 'Shorts', 'Jumpsuit', 'Kimono', 'Tunic', 'Kaftan'
          ];

          // Generate 100 products with unified structure
          const sampleProducts: Product[] = Array.from({ length: 100 }, (_, i) => {
            const category = categories[Math.floor(Math.random() * categories.length)];
            const baseName = productNames[Math.floor(Math.random() * productNames.length)];
            const materials = ['Cotton', 'Silk', 'Wool', 'Linen', 'Polyester', 'Viscose'];
            const material = materials[Math.floor(Math.random() * materials.length)];

            // Determine item type
            const itemTypes: ('product' | 'fabric' | 'accessory' | 'service')[] = ['product', 'fabric', 'accessory', 'service'];
            const itemType = itemTypes[Math.floor(Math.random() * itemTypes.length)];
            const isService = itemType === 'service';

            // Pricing
            const basePrice = Math.floor(Math.random() * 500) + 50; // ৳50-550
            const costPrice = isService ? 0 : Math.floor(basePrice * 0.6) + 20; // Provide a default 0 for services

            // Units based on item type
            const getUnitsForType = (type: string) => {
              switch (type) {
                case 'fabric': return ['meters', 'yards'];
                case 'accessory': return ['pieces', 'sets'];
                case 'service': return ['hours'];
                default: return ['pieces'];
              }
            };

            const availableUnits = getUnitsForType(itemType);
            const baseUnit = availableUnits[0];

            return {
              id: `product_${i + 1}`,
              name: `${material} ${baseName}`,
              description: `Premium quality ${material.toLowerCase()} ${baseName.toLowerCase()} with custom tailoring options. Perfect for ${category.toLowerCase()} collection.`,
              category,
              sku: `${itemType.toUpperCase().slice(0, 3)}-${String(i + 1).padStart(3, '0')}`,
              itemType,
              basePrice,
              costPrice,
              isService,
              requiresMeasurements: !isService && Math.random() > 0.3, // 70% of physical items require measurements
              estimatedTimeMinutes: isService ? Math.floor(Math.random() * 120) + 30 : undefined, // 30-150 minutes for services
              baseUnit: isService ? '' : baseUnit, // Provide a default empty string for services
              availableUnits: isService ? [] : availableUnits, // Provide a default empty array for services
              minimumStockLevel: isService ? 0 : Math.floor(Math.random() * 20) + 5, // Provide a default 0 for services
              currentStock: isService ? undefined : Math.floor(Math.random() * 100) + 10, // 10-110 current stock
              isActive: Math.random() > 0.1, // 90% active
              images: undefined,
              // Legacy compatibility
              price: basePrice,
              purchasePrice: costPrice, // Add purchasePrice
              sellingPrice: basePrice, // Add sellingPrice
              createdAt: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
              updatedAt: new Date().toISOString(),
            };
          });

          // Generate 100 customers
          const firstNames = ['Ahmed', 'Fatima', 'Mohammad', 'Ayesha', 'Hassan', 'Zainab', 'Ali', 'Khadija', 'Omar', 'Amina', 'Ibrahim', 'Maryam', 'Yusuf', 'Aisha', 'Abdul', 'Hafsa', 'Tariq', 'Ruqayyah', 'Bilal', 'Safiya'];
          const lastNames = ['Rahman', 'Khan', 'Ahmed', 'Ali', 'Hassan', 'Hussain', 'Shah', 'Malik', 'Chowdhury', 'Islam', 'Uddin', 'Begum', 'Khatun', 'Bibi', 'Sheikh', 'Miah', 'Haque', 'Alam', 'Siddique', 'Karim'];
          const areas = ['Dhanmondi', 'Gulshan', 'Banani', 'Uttara', 'Mirpur', 'Wari', 'Old Dhaka', 'Mohammadpur', 'Tejgaon', 'Ramna'];

          const sampleCustomers: Customer[] = Array.from({ length: 500 }, (_, i) => {
            const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
            const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
            const area = areas[Math.floor(Math.random() * areas.length)];

            return {
              id: `customer_${i + 1}`,
              outletId: 'default_outlet',
              name: `${firstName} ${lastName}`,
              email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${i + 1}@email.com`,
              phone: `01${Math.floor(Math.random() * 9) + 1}${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
              address: `${Math.floor(Math.random() * 999) + 1}, ${area}, Dhaka`,
              isActive: Math.random() > 0.05, // 95% active
              createdAt: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 180 days
              updatedAt: new Date().toISOString(),
            };
          });

          // Generate 50 orders
          const orderStatuses = ['pending', 'confirmed', 'in_progress', 'ready', 'delivered', 'cancelled'];
          const sampleOrders: Order[] = Array.from({ length: 500 }, (_, i) => {
            const customer = sampleCustomers[Math.floor(Math.random() * sampleCustomers.length)];
            const orderDate = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000); // Random date within last 60 days
            const deliveryDate = new Date(orderDate.getTime() + (Math.random() * 14 + 7) * 24 * 60 * 60 * 1000); // 7-21 days later

            // Generate 1-4 items per order
            const itemCount = Math.floor(Math.random() * 4) + 1;
            const orderItems = Array.from({ length: itemCount }, (_, j) => {
              const product = sampleProducts[Math.floor(Math.random() * sampleProducts.length)];
              const quantity = Math.floor(Math.random() * 3) + 1;
              const unitPrice = Math.max(product.basePrice + (Math.random() * 100 - 50), 20); // ±৳50 variation, minimum ৳20
              const totalPrice = unitPrice * quantity;

              return {
                id: `item_${i + 1}_${j + 1}`,
                orderId: `${i + 1}`, // Will be set correctly
                productId: product.id,
                productName: product.name,
                quantity,
                unitPrice,
                totalPrice,
                // Legacy compatibility
                price: unitPrice,
                total: totalPrice,
              };
            });

            const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);
            const tax = subtotal * 0.08; // 8% tax
            const total = subtotal + tax;

            return {
              id: `order_${i + 1}`,
              outletId: 'outlet_1', // Default outlet
              customerId: customer.id,
              orderNumber: `ORD-${String(i + 1).padStart(4, '0')}`,
              status: orderStatuses[Math.floor(Math.random() * orderStatuses.length)] as any,
              orderDate: orderDate.toISOString(),
              subtotal,
              taxAmount: tax,
              discountAmount: 0,
              totalAmount: total,
              paidAmount: Math.random() > 0.3 ? total : 0, // 70% paid
              paymentStatus: Math.random() > 0.3 ? 'paid' : 'pending',
              notes: Math.random() > 0.7 ? `Special instructions for order ${i + 1}` : '',
              items: orderItems,
              // Legacy compatibility fields
              customerName: customer.name,
              customerPhone: customer.phone,
              tax,
              total,
              createdAt: orderDate.toISOString(),
              updatedAt: new Date().toISOString(),
              deliveryDate: deliveryDate.toISOString(),
            };
          });

          dispatch({
            type: ActionTypes.LOAD_DATA,
            payload: {
              products: sampleProducts,
              customers: sampleCustomers,
              orders: sampleOrders,
              nextProductId: 101,
              nextCustomerId: 1001,
              nextOrderId: 51,
            }
          });

          LoggingService.info('Comprehensive sample data generated successfully', 'DATA_CONTEXT', {
            products: sampleProducts.length,
            customers: sampleCustomers.length,
            orders: sampleOrders.length,
          });
        } catch (error) {
          LoggingService.error('Failed to generate sample data', 'DATA_CONTEXT', error as Error);
          throw error;
        }
      },

      // Measurement actions - simplified stubs
      saveMeasurement: async (measurement: any) => {
        LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
        return { id: 'stub', ...measurement };
      },

      updateMeasurement: async (id: string, updates: any) => {
        LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
        return { id, ...updates };
      },

      deleteMeasurement: async (id: string) => {
        LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
      },

      getMeasurementHistory: async (customerId: string, serviceType?: any) => {
        LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
        return [];
      },

      getTemplateByServiceType: async (serviceType: any) => {
        LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
        return null;
      },

      // Staff actions
      createStaff: async (staff: any) => {
        try {
          const newStaff: Staff = {
            ...staff,
            id: `staff_${Date.now()}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          dispatch({ type: ActionTypes.ADD_STAFF, payload: newStaff });
          return newStaff;
        } catch (error) {
          LoggingService.error('Failed to create staff', 'DATA_CONTEXT', error as Error);
          throw error;
        }
      },

      addStaff: async (staffData: Omit<Staff, 'id' | 'createdAt' | 'updatedAt'>) => {
        const staff: Staff = {
          ...staffData,
          id: `staff_${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        dispatch({ type: ActionTypes.ADD_STAFF, payload: staff });
        return staff;
      },

      updateStaff: async (id: string, updates: Partial<Staff>) => {
        const existingStaff = state.staff.find(s => s.id === id);
        if (!existingStaff) {
          throw new Error(`Staff with ID ${id} not found`);
        }

        const updatedStaff: Staff = {
          ...existingStaff,
          ...updates,
          updatedAt: new Date().toISOString(),
        };

        dispatch({ type: ActionTypes.UPDATE_STAFF, payload: updatedStaff });
        return updatedStaff;
      },

      deleteStaff: async (id: string): Promise<void> => {
        const existingStaff = state.staff.find(s => s.id === id);
        if (!existingStaff) {
          throw new Error(`Staff with ID ${id} not found`);
        }

        // Soft delete - mark as inactive
        const deletedStaff: Staff = {
          ...existingStaff,
          isActive: false,
          updatedAt: new Date().toISOString(),
        };

        dispatch({ type: ActionTypes.UPDATE_STAFF, payload: deletedStaff });
      },

      getStaffById: async (id: string): Promise<Staff | null> => {
        return state.staff.find(s => s.id === id) || null;
      },

      getStaffByRole: async (role: string): Promise<Staff[]> => {
        return state.staff.filter(s => s.role === role && s.isActive);
      },

      getActiveStaff: async (): Promise<Staff[]> => {
        return state.staff.filter(s => s.isActive);
      },

      getAvailableStaff: async (skill?: string) => {
        try {
          return state.staff.filter(s => s.isActive);
        } catch (error) {
          LoggingService.error('Failed to get available staff', 'DATA_CONTEXT', error as Error);
          throw error;
        }
      },

      // Staff analytics and performance
      updateWorkload: async (staffId: string, workload: any) => {
        try {
          LoggingService.info('Update workload not implemented', 'DATA_CONTEXT');
        } catch (error) {
          LoggingService.error('Failed to update workload', 'DATA_CONTEXT', error as Error);
          throw error;
        }
      },

      updatePerformance: async (staffId: string, performance: any) => {
        try {
          LoggingService.info('Update performance not implemented', 'DATA_CONTEXT');
        } catch (error) {
          LoggingService.error('Failed to update performance', 'DATA_CONTEXT', error as Error);
          throw error;
        }
      },

      getStaffPerformance: async (staffId: string, period: string): Promise<any> => {
        const staff = state.staff.find(s => s.id === staffId);
        if (!staff) {
          throw new Error(`Staff with ID ${staffId} not found`);
        }

        // Calculate performance metrics based on orders
        const staffOrders = state.orders.filter(o => (o as any).assignedTo === staffId);
        const completedOrders = staffOrders.filter(o => o.status === 'Completed');
        const totalRevenue = completedOrders.reduce(
          (sum, order) => sum + (order.total || 0),
          0
        );

        return {
          staffId,
          staffName: staff.name,
          totalOrders: staffOrders.length,
          completedOrders: completedOrders.length,
          completionRate:
            staffOrders.length > 0 ? (completedOrders.length / staffOrders.length) * 100 : 0,
          totalRevenue,
          averageOrderValue: completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0,
        };
      },

      analyzeWorkload: async (staffId: string): Promise<any> => {
        const staff = state.staff.find(s => s.id === staffId);
        if (!staff) {
          throw new Error(`Staff with ID ${staffId} not found`);
        }

        // Analyze current workload
        const activeOrders = state.orders.filter(
          o => (o as any).assignedTo === staffId && ['pending', 'in_progress'].includes(o.status)
        );

        const estimatedCompletionTime = activeOrders.reduce((total, order) => {
          // Simple estimation based on order complexity
          const complexity = (order as any).items?.length || 1;
          return total + complexity * 2; // 2 hours per item as rough estimate
        }, 0);

        return {
          staffId,
          staffName: staff.name,
          activeOrders: activeOrders.length,
          estimatedCompletionTime,
          workloadLevel:
            activeOrders.length <= 2 ? 'low' : activeOrders.length <= 5 ? 'medium' : 'high',
          recommendations:
            activeOrders.length > 5 ? 'Consider redistributing workload' : 'Workload manageable',
        };
      },

      generatePerformanceReport: async (staffId: string) => {
        try {
          LoggingService.info('Generate performance report not implemented', 'DATA_CONTEXT');
          return { staffId, report: 'Not implemented' };
        } catch (error) {
          LoggingService.error(
            'Failed to generate performance report',
            'DATA_CONTEXT',
            error as Error
          );
          throw error;
        }
      },

      // Inventory interface - now unified with products
      getInventoryItems: () => {
        return state.products.filter(product => !product.isService).map(product => ({
          ...product,
          // Ensure inventory fields are present
          baseUnit: product.baseUnit || 'pieces',
          availableUnits: product.availableUnits || ['pieces'],
          purchasePrice: product.costPrice || product.basePrice * 0.8,
          sellingPrice: product.basePrice,
          minimumStockLevel: product.minimumStockLevel || 10,
          images: product.images,
        }));
      },

      addInventoryItem: async (item: any) => {
        // Convert inventory item to unified product structure
        return actions.addProduct({
          name: item.name,
          description: item.description || '',
          category: item.category || 'General',
          sku: item.sku || '',
          itemType: item.itemType || 'product',
          basePrice: item.sellingPrice || item.purchasePrice || 0,
          costPrice: item.purchasePrice,
          purchasePrice: item.purchasePrice, // Add purchasePrice
          sellingPrice: item.sellingPrice || item.basePrice || 0, // Add sellingPrice
          baseUnit: item.baseUnit || 'pieces',
          availableUnits: item.availableUnits || ['pieces'],
          minimumStockLevel: item.minimumStockLevel || 10,
          currentStock: 0,
          isService: false,
          requiresMeasurements: item.requiresMeasurements || false,
          isActive: item.isActive !== false,
          images: item.imageUri ? [item.imageUri] : undefined,
          // Legacy compatibility
          price: item.sellingPrice || item.purchasePrice || 0,
          stock: 0,
        });
      },

      updateInventoryItem: async (id: string, updates: any) => {
        const existingProduct = state.products.find(p => p.id === id);
        if (!existingProduct) {
          throw new Error('Item not found');
        }

        const updatedProduct = {
          ...existingProduct,
          name: updates.name || existingProduct.name,
          description: updates.description || existingProduct.description,
          category: updates.category || existingProduct.category,
          sku: updates.sku || existingProduct.sku,
          basePrice: updates.sellingPrice || updates.basePrice || existingProduct.basePrice,
          costPrice: updates.purchasePrice || updates.costPrice || existingProduct.costPrice,
          baseUnit: updates.baseUnit || existingProduct.baseUnit,
          availableUnits: updates.availableUnits || existingProduct.availableUnits,
          minimumStockLevel: updates.minimumStockLevel || existingProduct.minimumStockLevel,
          isActive: updates.isActive !== undefined ? updates.isActive : existingProduct.isActive,
          images: updates.imageUri ? [updates.imageUri] : existingProduct.images,
          updatedAt: new Date().toISOString(),
          // Legacy compatibility
          price: updates.sellingPrice || updates.basePrice || existingProduct.basePrice,
          image: updates.imageUri ? updates.imageUri : (existingProduct.images && existingProduct.images.length > 0 ? existingProduct.images[0] : undefined),
        };

        return actions.updateProduct(updatedProduct);
      },

      deleteInventoryItem: async (id: string) => {
        return actions.deleteProduct(id);
      },

      // Inventory dashboard functions
      loadDashboardData: async () => {
        // Dashboard data is already loaded through DataContext
        LoggingService.info('Dashboard data loaded from DataContext', 'INVENTORY');
      },

      // Search and filter functions
      setSearchQuery: (query: string) => {
        // Search handled by components
        LoggingService.debug(`Search query set: ${query}`, 'INVENTORY');
      },

      setActiveFilters: (filters: any) => {
        // Filters handled by components
        LoggingService.debug('Filters set', 'INVENTORY', filters);
      },

      clearFilters: () => {
        // Filters handled by components
        LoggingService.debug('Filters cleared', 'INVENTORY');
      },

      // Stock operations (simplified)
      stockIn: async (itemId: string, warehouseId: string, quantity: number, unit: string, performedBy: string, reference?: string, note?: string) => {
        LoggingService.info(`Stock in: ${quantity} ${unit} for item ${itemId}`, 'INVENTORY');
        return { success: true, newStockLevel: quantity, message: 'Stock in recorded' };
      },

      stockOut: async (itemId: string, warehouseId: string, quantity: number, unit: string, performedBy: string, reference?: string, note?: string) => {
        LoggingService.info(`Stock out: ${quantity} ${unit} for item ${itemId}`, 'INVENTORY');
        return { success: true, newStockLevel: 0, message: 'Stock out recorded' };
      },

      getStockByItem: async (itemId: string) => {
        return await masterDb.getInventoryStockByItem(itemId);
      },

      getStockByWarehouse: async (warehouseId: string) => {
        // Return empty stock array for now
        return [];
      },

      // Transfer operations
      transferStock: async (itemId: string, fromWarehouseId: string, toWarehouseId: string, quantity: number, unit: string, performedBy: string) => {
        LoggingService.info(`Stock transfer: ${quantity} ${unit} from ${fromWarehouseId} to ${toWarehouseId}`, 'INVENTORY');
        return { success: true, fromStockLevel: 0, toStockLevel: quantity, message: 'Transfer completed' };
      },

      // Transaction history
      getTransactionHistory: async (filters?: any) => {
        // Return empty transactions for now
        return [];
      },

      loadTransactions: async (filters?: any) => {
        // Transactions loaded (empty for now)
        LoggingService.debug('Transactions loaded', 'INVENTORY');
      },

      // Warehouse operations
      loadWarehouses: async () => {
        // Warehouses loaded (empty for now)
        LoggingService.debug('Warehouses loaded', 'INVENTORY');
      },

      addWarehouse: async (warehouse: any) => {
        const newWarehouse = {
          id: `warehouse_${Date.now()}`,
          name: warehouse.name,
          location: warehouse.location,
          isActive: true,
          isDefault: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        LoggingService.info(`Warehouse added: ${newWarehouse.name}`, 'INVENTORY');
        return newWarehouse;
      },

      updateWarehouse: async (id: string, updates: any) => {
        const updatedWarehouse = {
          id,
          ...updates,
          updatedAt: new Date().toISOString(),
        };
        LoggingService.info(`Warehouse updated: ${id}`, 'INVENTORY');
        return updatedWarehouse;
      },

      // Item loading
      loadItems: async (filters?: any) => {
        // Items are already loaded through DataContext
        LoggingService.debug('Items loaded from DataContext', 'INVENTORY');
      },

      getItemById: async (id: string) => {
        const product = state.products.find(p => p.id === id);
        if (product) {
          return {
            ...product,
            baseUnit: 'pieces',
            availableUnits: ['pieces'],
            purchasePrice: product.basePrice * 0.8,
            sellingPrice: product.basePrice,
            minimumStockLevel: 10,
            imageUri: undefined,
          };
        }
        return null;
      },
    }),
    [
      dispatch,
      state.products,
      state.orders,
      state.customers,
      state.settings,
      state.nextOrderId,
      saveData,
      loadData,
    ]
  );

  return <DataContext.Provider value={{ state, actions }}>{children}</DataContext.Provider>;
};

// Custom hook to use the context
export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;