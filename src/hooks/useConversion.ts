import { useMemo } from 'react';

import LoggingService from '../services/LoggingService';
import UnitConverter from '../utils/UnitConverter';

interface UseConversionProps {
  quantity: number;
  fromUnit: string;
  toUnit: string;
  precision?: number;
}

export const useConversion = ({ quantity, fromUnit, toUnit, precision = 2 }: UseConversionProps) => {
  return useMemo(() => {
    if (!isFinite(quantity) || quantity <= 0) {
      return { error: 'Quantity must be a positive number.' };
    }

    try {
      const forwardValue = UnitConverter.convertBetweenUnits(quantity, fromUnit, toUnit);
      const backwardValue = UnitConverter.convertBetweenUnits(quantity, toUnit, fromUnit);
      const conversionRate = UnitConverter.getConversionRate(fromUnit, toUnit);

      return {
        forwardResult: UnitConverter.formatQuantity(forwardValue, toUnit, precision),
        backwardResult: UnitConverter.formatQuantity(backwardValue, fromUnit, precision),
        formattedQuantity: UnitConverter.formatQuantity(quantity, fromUnit, precision),
        conversionRate,
        fromUnitInfo: {
          name: UnitConverter.getUnitDisplayName(fromUnit),
          abbr: UnitConverter.getUnitAbbreviation(fromUnit),
        },
        toUnitInfo: {
          name: UnitConverter.getUnitDisplayName(toUnit),
          abbr: UnitConverter.getUnitAbbreviation(toUnit),
        },
        error: null,
      };
    } catch (error) {
      LoggingService.warn('Conversion calculation failed', 'CONVERSION_PREVIEW', error as Error);
      return { error: (error as Error).message };
    }
  }, [quantity, fromUnit, toUnit, precision]);
};