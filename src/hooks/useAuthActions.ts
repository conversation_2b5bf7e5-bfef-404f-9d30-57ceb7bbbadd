import { useCallback } from 'react';

import { AuthAction } from '../context/AuthContext';
import AuthService, { LoginCredentials } from '../services/AuthService';
import LoggingService from '../services/LoggingService';

export const useAuthActions = (dispatch: React.Dispatch<AuthAction>) => {
  // PRODUCTION FIX: Enhanced session info update with timeout protection
  const updateSessionInfo = useCallback(async () => {
    try {
      // Add timeout protection to prevent hanging
      const sessionInfoTimeout = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Session info timeout')), 5000); // 5 second timeout
      });

      const sessionInfoPromise = AuthService.getSessionInfo();
      const sessionInfo = await Promise.race([sessionInfoPromise, sessionInfoTimeout]);
      
      if (sessionInfo.isAuthenticated && sessionInfo.session) {
        dispatch({
          type: 'UPDATE_SESSION_INFO',
          payload: {
            timeUntilExpiry: sessionInfo.timeUntilExpiry,
            lastActivity: sessionInfo.session.lastActivity,
          },
        });
      }
    } catch (error) {
      // Don't log timeout errors as errors, just warnings
      if ((error as Error).message.includes('timeout')) {
        LoggingService.warn('Session info update timeout - continuing silently', 'AUTH_ACTIONS');
      } else {
        LoggingService.error('Failed to update session info', 'AUTH_ACTIONS', error as Error);
      }
    }
  }, [dispatch]);

  // PRODUCTION FIX: Enhanced auth status check with timeout protection
  const checkAuthStatus = useCallback(async () => {
    try {
      dispatch({ type: 'AUTH_LOADING', payload: true });
      
      // Add timeout protection to prevent hanging during startup
      const authCheckTimeout = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Auth check timeout')), 10000); // 10 second timeout
      });

      const sessionPromise = AuthService.getCurrentSession();
      const session = await Promise.race([sessionPromise, authCheckTimeout]);
      
      if (session) {
        dispatch({ type: 'AUTH_SUCCESS', payload: session });
        // Update session info in background without blocking
        updateSessionInfo().catch((error) => {
          LoggingService.warn('Session info update failed during auth check', 'AUTH_ACTIONS', error as Error);
        });
      } else {
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    } catch (error) {
      if ((error as Error).message.includes('timeout')) {
        LoggingService.warn('Auth status check timeout - logging out for safety', 'AUTH_ACTIONS');
      } else {
        LoggingService.error('Failed to check auth status', 'AUTH_ACTIONS', error as Error);
      }
      dispatch({ type: 'AUTH_LOGOUT' });
    } finally {
      dispatch({ type: 'AUTH_LOADING', payload: false });
    }
  }, [dispatch, updateSessionInfo]);

  // PRODUCTION FIX: Enhanced login with timeout protection
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      dispatch({ type: 'AUTH_LOADING', payload: true });
      
      // Add timeout protection for login
      const loginTimeout = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Login timeout')), 15000); // 15 second timeout
      });

      const loginPromise = AuthService.login(credentials);
      const session = await Promise.race([loginPromise, loginTimeout]);
      
      dispatch({ type: 'AUTH_SUCCESS', payload: session });
      
      // Update session info in background
      updateSessionInfo().catch((error) => {
        LoggingService.warn('Session info update failed after login', 'AUTH_ACTIONS', error as Error);
      });
    } catch (error) {
      const errorMessage = (error as Error).message.includes('timeout') 
        ? 'Login timeout - please try again'
        : error instanceof Error ? error.message : 'Login failed';
      
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  }, [dispatch, updateSessionInfo]);

  // PRODUCTION FIX: Enhanced logout with timeout protection
  const logout = useCallback(async () => {
    try {
      dispatch({ type: 'AUTH_LOADING', payload: true });
      
      // Add timeout protection for logout
      const logoutTimeout = new Promise<void>((resolve) => {
        setTimeout(() => {
          LoggingService.warn('Logout timeout - forcing local logout', 'AUTH_ACTIONS');
          resolve();
        }, 5000); // 5 second timeout
      });

      const logoutPromise = AuthService.logout();
      await Promise.race([logoutPromise, logoutTimeout]);
      
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      LoggingService.error('Logout failed', 'AUTH_ACTIONS', error as Error);
      // Always logout locally even if server logout fails
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  }, [dispatch]);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, [dispatch]);

  return { checkAuthStatus, login, logout, clearError, updateSessionInfo };
};