import { renderHook } from '@testing-library/react-native';

import { UserSession } from '../../services/AuthService';
import { usePermissions } from '../usePermissions';

describe('usePermissions', () => {
  const createMockUser = (role: 'admin' | 'manager' | 'user', permissions: string[] = []): UserSession => ({
    userId: 'test_user',
    username: 'testuser',
    role,
    sessionId: 'session_123',
    accessToken: 'token_123',
    expiresAt: Date.now() + 3600000,
    createdAt: Date.now(),
    lastActivity: Date.now(),
    deviceInfo: { platform: 'ios', deviceId: 'device_123', appVersion: '1.0.0' },
    permissions,
  });

  describe('hasRole', () => {
    it('should return true for admin user checking admin role', () => {
      const adminUser = createMockUser('admin');
      const { result } = renderHook(() => usePermissions(adminUser));

      expect(result.current.hasRole('admin')).toBe(true);
    });

    it('should return true for admin user checking lower roles', () => {
      const adminUser = createMockUser('admin');
      const { result } = renderHook(() => usePermissions(adminUser));

      expect(result.current.hasRole('manager')).toBe(true);
      expect(result.current.hasRole('user')).toBe(true);
    });

    it('should return true for manager user checking manager and user roles', () => {
      const managerUser = createMockUser('manager');
      const { result } = renderHook(() => usePermissions(managerUser));

      expect(result.current.hasRole('manager')).toBe(true);
      expect(result.current.hasRole('user')).toBe(true);
    });

    it('should return false for manager user checking admin role', () => {
      const managerUser = createMockUser('manager');
      const { result } = renderHook(() => usePermissions(managerUser));

      expect(result.current.hasRole('admin')).toBe(false);
    });

    it('should return true for user checking user role', () => {
      const regularUser = createMockUser('user');
      const { result } = renderHook(() => usePermissions(regularUser));

      expect(result.current.hasRole('user')).toBe(true);
    });

    it('should return false for user checking higher roles', () => {
      const regularUser = createMockUser('user');
      const { result } = renderHook(() => usePermissions(regularUser));

      expect(result.current.hasRole('manager')).toBe(false);
      expect(result.current.hasRole('admin')).toBe(false);
    });

    it('should return false when no user is provided', () => {
      const { result } = renderHook(() => usePermissions(null));

      expect(result.current.hasRole('user')).toBe(false);
      expect(result.current.hasRole('manager')).toBe(false);
      expect(result.current.hasRole('admin')).toBe(false);
    });
  });

  describe('hasPermission', () => {
    it('should return true when user has specific permission', () => {
      const user = createMockUser('user', ['read:orders', 'write:orders']);
      const { result } = renderHook(() => usePermissions(user));

      expect(result.current.hasPermission('read:orders')).toBe(true);
      expect(result.current.hasPermission('write:orders')).toBe(true);
    });

    it('should return false when user does not have specific permission', () => {
      const user = createMockUser('user', ['read:orders']);
      const { result } = renderHook(() => usePermissions(user));

      expect(result.current.hasPermission('write:orders')).toBe(false);
      expect(result.current.hasPermission('delete:orders')).toBe(false);
    });

    it('should return true for admin user regardless of specific permissions', () => {
      const adminUser = createMockUser('admin', []);
      const { result } = renderHook(() => usePermissions(adminUser));

      expect(result.current.hasPermission('read:orders')).toBe(true);
      expect(result.current.hasPermission('write:orders')).toBe(true);
      expect(result.current.hasPermission('delete:orders')).toBe(true);
      expect(result.current.hasPermission('any:permission')).toBe(true);
    });

    it('should return false when no user is provided', () => {
      const { result } = renderHook(() => usePermissions(null));

      expect(result.current.hasPermission('read:orders')).toBe(false);
      expect(result.current.hasPermission('write:orders')).toBe(false);
    });

    it('should handle undefined permissions array', () => {
      const userWithoutPermissions = {
        ...createMockUser('user'),
        permissions: undefined as any,
      };
      const { result } = renderHook(() => usePermissions(userWithoutPermissions));

      expect(result.current.hasPermission('read:orders')).toBe(false);
    });
  });

  describe('isAdmin', () => {
    it('should return true for admin user', () => {
      const adminUser = createMockUser('admin');
      const { result } = renderHook(() => usePermissions(adminUser));

      expect(result.current.isAdmin()).toBe(true);
    });

    it('should return false for manager user', () => {
      const managerUser = createMockUser('manager');
      const { result } = renderHook(() => usePermissions(managerUser));

      expect(result.current.isAdmin()).toBe(false);
    });

    it('should return false for regular user', () => {
      const regularUser = createMockUser('user');
      const { result } = renderHook(() => usePermissions(regularUser));

      expect(result.current.isAdmin()).toBe(false);
    });

    it('should return false when no user is provided', () => {
      const { result } = renderHook(() => usePermissions(null));

      expect(result.current.isAdmin()).toBe(false);
    });
  });

  describe('isManagerOrHigher', () => {
    it('should return true for admin user', () => {
      const adminUser = createMockUser('admin');
      const { result } = renderHook(() => usePermissions(adminUser));

      expect(result.current.isManagerOrHigher()).toBe(true);
    });

    it('should return true for manager user', () => {
      const managerUser = createMockUser('manager');
      const { result } = renderHook(() => usePermissions(managerUser));

      expect(result.current.isManagerOrHigher()).toBe(true);
    });

    it('should return false for regular user', () => {
      const regularUser = createMockUser('user');
      const { result } = renderHook(() => usePermissions(regularUser));

      expect(result.current.isManagerOrHigher()).toBe(false);
    });

    it('should return false when no user is provided', () => {
      const { result } = renderHook(() => usePermissions(null));

      expect(result.current.isManagerOrHigher()).toBe(false);
    });
  });

  describe('callback stability', () => {
    it('should return stable function references when user does not change', () => {
      const user = createMockUser('user', ['read:orders']);
      const { result, rerender } = renderHook(() => usePermissions(user));

      const firstRender = result.current;
      
      rerender({});
      
      const secondRender = result.current;

      expect(firstRender.hasRole).toBe(secondRender.hasRole);
      expect(firstRender.hasPermission).toBe(secondRender.hasPermission);
      expect(firstRender.isAdmin).toBe(secondRender.isAdmin);
      expect(firstRender.isManagerOrHigher).toBe(secondRender.isManagerOrHigher);
    });

    it('should update function references when user changes', () => {
      const user1 = createMockUser('user', ['read:orders']);
      const user2 = createMockUser('admin', ['read:all']);
      
      const { result, rerender } = renderHook(
        (props: { user: any }) => usePermissions(props.user),
        { initialProps: { user: user1 } }
      );

      const firstRender = result.current;
      
      rerender({ user: user2 });
      
      const secondRender = result.current;

      // Functions should be new instances
      expect(firstRender.hasRole).not.toBe(secondRender.hasRole);
      
      // But should work correctly with new user
      expect(secondRender.hasRole('admin')).toBe(true);
      expect(secondRender.isAdmin()).toBe(true);
    });
  });

  describe('role hierarchy validation', () => {
    it('should respect role hierarchy consistently', () => {
      // Test all role combinations
      const testCases = [
        { userRole: 'user', checkRole: 'user', expected: true },
        { userRole: 'user', checkRole: 'manager', expected: false },
        { userRole: 'user', checkRole: 'admin', expected: false },
        { userRole: 'manager', checkRole: 'user', expected: true },
        { userRole: 'manager', checkRole: 'manager', expected: true },
        { userRole: 'manager', checkRole: 'admin', expected: false },
        { userRole: 'admin', checkRole: 'user', expected: true },
        { userRole: 'admin', checkRole: 'manager', expected: true },
        { userRole: 'admin', checkRole: 'admin', expected: true },
      ] as const;

      testCases.forEach(({ userRole, checkRole, expected }) => {
        const user = createMockUser(userRole);
        const { result } = renderHook(() => usePermissions(user));
        
        expect(result.current.hasRole(checkRole)).toBe(expected);
      });
    });
  });

  describe('edge cases', () => {
    it('should handle user with empty permissions array', () => {
      const user = createMockUser('user', []);
      const { result } = renderHook(() => usePermissions(user));

      expect(result.current.hasPermission('read:orders')).toBe(false);
      expect(result.current.hasRole('user')).toBe(true);
    });

    it('should handle user with null/undefined email', () => {
      const user = {
        ...createMockUser('user'),
        email: undefined,
      };
      const { result } = renderHook(() => usePermissions(user));

      expect(result.current.hasRole('user')).toBe(true);
      expect(result.current.isAdmin()).toBe(false);
    });

    it('should be case-sensitive for permissions', () => {
      const user = createMockUser('user', ['read:orders']);
      const { result } = renderHook(() => usePermissions(user));

      expect(result.current.hasPermission('read:orders')).toBe(true);
      expect(result.current.hasPermission('READ:ORDERS')).toBe(false);
      expect(result.current.hasPermission('Read:Orders')).toBe(false);
    });
  });
});