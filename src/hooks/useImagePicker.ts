import * as ExpoImagePicker from 'expo-image-picker';
import { useCallback } from 'react';
import { Alert } from 'react-native';

import LoggingService from '../services/LoggingService';

interface ImagePickerOptions {
  onImageSelected: (uri: string) => void;
  allowsEditing?: boolean;
  aspectRatio?: number;
  quality?: number;
}

export const useImagePicker = ({ onImageSelected, allowsEditing = true, aspectRatio = 1, quality = 1 }: ImagePickerOptions) => {
  const pickImage = useCallback(async (source: 'camera' | 'gallery') => {
    const isCamera = source === 'camera';
    const permissionRequest = isCamera ? ExpoImagePicker.requestCameraPermissionsAsync : ExpoImagePicker.requestMediaLibraryPermissionsAsync;
    const launchPicker = isCamera ? ExpoImagePicker.launchCameraAsync : ExpoImagePicker.launchImageLibraryAsync;

    try {
      const { status } = await permissionRequest();
      if (status !== 'granted') {
        Alert.alert('Permission Required', `Permission to access the ${source} is required. Please enable it in your device settings.`);
        LoggingService.warn(`${source} permission denied`, 'IMAGE_PICKER');
        return;
      }

      const result = await launchPicker({
        mediaTypes: ExpoImagePicker.MediaTypeOptions.Images,
        allowsEditing,
        aspect: aspectRatio ? [aspectRatio, 1] : undefined,
        quality,
      });

      if (!result.canceled && result.assets?.[0]?.uri) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', `Could not select image from ${source}. Please try again.`);
      LoggingService.error(`${source} launch failed`, 'IMAGE_PICKER', error as Error);
    }
  }, [onImageSelected, allowsEditing, aspectRatio, quality]);

  return { pickImage };
};