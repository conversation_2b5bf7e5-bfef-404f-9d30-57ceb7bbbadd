import { useCallback, useState, useEffect } from 'react';

import { useToast } from '../context/ToastContext';
import LoggingService from '../services/LoggingService';
import { getSecurityService } from '../services/SecurityService';

export interface SecurityValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
}

export interface SecurityHookOptions {
  enableRealTimeValidation?: boolean;
  enableAuditLogging?: boolean;
  customValidator?: (value: string) => string | null;
}

/**
 * Custom hook for security features integration
 * Provides input validation, sanitization, and security monitoring capabilities
 */
export const useSecurity = (options: SecurityHookOptions = {}) => {
  const { 
    enableRealTimeValidation = true, 
    enableAuditLogging = false,
    customValidator 
  } = options;
  
  const { showError } = useToast();
  const [securityService, setSecurityService] = useState<any>(null);
  const [securityScore, setSecurityScore] = useState<number>(0);
  const [securityIssues, setSecurityIssues] = useState<string[]>([]);

  // Initialize security service
  useEffect(() => {
    const initSecurity = async () => {
      try {
        const service = await getSecurityService();
        setSecurityService(service);
        
        // Perform initial security check
        const securityCheck = service.performSecurityCheck();
        setSecurityScore(securityCheck.score);
        setSecurityIssues(securityCheck.issues);
        
        if (securityCheck.score < 80) {
          LoggingService.warn('Security score below threshold', 'SECURITY_HOOK', {
            score: securityCheck.score,
            issues: securityCheck.issues
          });
        }
      } catch (error) {
        LoggingService.error('Failed to initialize security service', 'SECURITY_HOOK', error as Error);
      }
    };

    initSecurity();
  }, []);

  /**
   * Validate and sanitize input with comprehensive security checks
   */
  const validateSecureInput = useCallback(async (
    value: string,
    type: 'email' | 'phone' | 'text' | 'number' = 'text'
  ): Promise<SecurityValidationResult> => {
    if (!securityService) {
      return { isValid: false, sanitizedValue: value, errors: ['Security service not initialized'] };
    }

    const errors: string[] = [];
    let sanitizedValue = value;

    try {
      // Basic validation
      const isValidType = securityService.validateInput(value, type);
      if (!isValidType) {
        errors.push(`Invalid ${type} format`);
      }

      // Sanitize input for XSS and injection attacks
      sanitizedValue = securityService.sanitizeInput(value);
      if (sanitizedValue !== value) {
        errors.push('Input contained potentially dangerous content and was sanitized');
        
        if (enableAuditLogging) {
          securityService.auditLog('INPUT_SANITIZED', {
            type,
            originalLength: value.length,
            sanitizedLength: sanitizedValue.length,
            timestamp: new Date().toISOString()
          });
        }
      }

      // Custom validation if provided
      if (customValidator) {
        const customError = customValidator(sanitizedValue);
        if (customError) {
          errors.push(customError);
        }
      }

      // SQL injection check for database-bound inputs
      if (type === 'text' || type === 'email') {
        const sqlSanitized = securityService.sanitizeSQLInput(sanitizedValue);
        if (sqlSanitized !== sanitizedValue) {
          sanitizedValue = sqlSanitized;
          errors.push('Input contained SQL injection patterns and was sanitized');
          
          if (enableAuditLogging) {
            securityService.auditLog('SQL_INJECTION_ATTEMPT', {
              type,
              originalValue: value,
              timestamp: new Date().toISOString()
            });
          }
        }
      }

      return {
        isValid: errors.length === 0,
        sanitizedValue,
        errors
      };
    } catch (error) {
      LoggingService.error('Security validation failed', 'SECURITY_HOOK', error as Error);
      return { 
        isValid: false, 
        sanitizedValue: value, 
        errors: ['Security validation failed'] 
      };
    }
  }, [securityService, customValidator, enableAuditLogging]);

  /**
   * Secure data encryption for sensitive information
   */
  const encryptSensitiveData = useCallback(async (data: string): Promise<string | null> => {
    if (!securityService) {
      LoggingService.error('Security service not available for encryption', 'SECURITY_HOOK');
      return null;
    }

    try {
      const encrypted = await securityService.encrypt(data);
      
      if (enableAuditLogging) {
        securityService.auditLog('DATA_ENCRYPTED', {
          dataLength: data.length,
          timestamp: new Date().toISOString()
        });
      }
      
      return encrypted;
    } catch (error) {
      LoggingService.error('Data encryption failed', 'SECURITY_HOOK', error as Error);
      showError('Failed to secure sensitive data');
      return null;
    }
  }, [securityService, enableAuditLogging, showError]);

  /**
   * Secure data decryption
   */
  const decryptSensitiveData = useCallback(async (encryptedData: string): Promise<string | null> => {
    if (!securityService) {
      LoggingService.error('Security service not available for decryption', 'SECURITY_HOOK');
      return null;
    }

    try {
      const decrypted = await securityService.decrypt(encryptedData);
      
      if (enableAuditLogging) {
        securityService.auditLog('DATA_DECRYPTED', {
          timestamp: new Date().toISOString()
        });
      }
      
      return decrypted;
    } catch (error) {
      LoggingService.error('Data decryption failed', 'SECURITY_HOOK', error as Error);
      return null;
    }
  }, [securityService, enableAuditLogging]);

  /**
   * Store data securely using the security service
   */
  const storeSecurely = useCallback(async (key: string, value: string): Promise<boolean> => {
    if (!securityService) {
      LoggingService.error('Security service not available for secure storage', 'SECURITY_HOOK');
      return false;
    }

    try {
      await securityService.secureStore(key, value);
      
      if (enableAuditLogging) {
        securityService.auditLog('SECURE_STORAGE', {
          key,
          timestamp: new Date().toISOString()
        });
      }
      
      return true;
    } catch (error) {
      LoggingService.error('Secure storage failed', 'SECURITY_HOOK', error as Error);
      showError('Failed to store data securely');
      return false;
    }
  }, [securityService, enableAuditLogging, showError]);

  /**
   * Retrieve securely stored data
   */
  const retrieveSecurely = useCallback(async (key: string): Promise<string | null> => {
    if (!securityService) {
      LoggingService.error('Security service not available for secure retrieval', 'SECURITY_HOOK');
      return null;
    }

    try {
      const value = await securityService.secureRetrieve(key);
      
      if (enableAuditLogging && value) {
        securityService.auditLog('SECURE_RETRIEVAL', {
          key,
          timestamp: new Date().toISOString()
        });
      }
      
      return value;
    } catch (error) {
      LoggingService.error('Secure retrieval failed', 'SECURITY_HOOK', error as Error);
      return null;
    }
  }, [securityService, enableAuditLogging]);

  /**
   * Perform comprehensive security audit
   */
  const performSecurityAudit = useCallback(async () => {
    if (!securityService) {
      return { score: 0, issues: ['Security service not available'], recommendations: [] };
    }

    try {
      const auditResult = securityService.performSecurityCheck();
      setSecurityScore(auditResult.score);
      setSecurityIssues(auditResult.issues);

      if (enableAuditLogging) {
        securityService.auditLog('SECURITY_AUDIT', {
          score: auditResult.score,
          issuesCount: auditResult.issues.length,
          timestamp: new Date().toISOString()
        });
      }

      return auditResult;
    } catch (error) {
      LoggingService.error('Security audit failed', 'SECURITY_HOOK', error as Error);
      return { score: 0, issues: ['Security audit failed'], recommendations: [] };
    }
  }, [securityService, enableAuditLogging]);

  /**
   * Log security events for monitoring
   */
  const logSecurityEvent = useCallback((event: string, details: Record<string, any> = {}) => {
    if (!securityService) {
      LoggingService.warn('Security service not available for audit logging', 'SECURITY_HOOK');
      return;
    }

    securityService.auditLog(event, {
      ...details,
      timestamp: new Date().toISOString(),
      source: 'useSecurity_hook'
    });
  }, [securityService]);

  return {
    // Validation functions
    validateSecureInput,
    
    // Encryption functions
    encryptSensitiveData,
    decryptSensitiveData,
    
    // Secure storage functions
    storeSecurely,
    retrieveSecurely,
    
    // Security monitoring
    performSecurityAudit,
    logSecurityEvent,
    
    // Security status
    securityScore,
    securityIssues,
    securityService,
    isSecurityReady: !!securityService
  };
};

export default useSecurity;