import { useCallback } from 'react';

import { UserSession } from '../services/AuthService';

const roleHierarchy = { admin: 3, manager: 2, user: 1 } as const;

export const usePermissions = (user: UserSession | null) => {
  const hasRole = useCallback((requiredRole: keyof typeof roleHierarchy): boolean => {
    if (!user) {return false;}
    return roleHierarchy[user.role] >= roleHierarchy[requiredRole];
  }, [user]);

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) {return false;}
    return user.permissions?.includes(permission) || user.role === 'admin';
  }, [user]);

  const isAdmin = useCallback((): boolean => hasRole('admin'), [hasRole]);
  const isManagerOrHigher = useCallback((): boolean => hasRole('manager'), [hasRole]);

  return { hasRole, hasPermission, isAdmin, isManagerOrHigher };
};