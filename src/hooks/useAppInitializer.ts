import { useEffect } from 'react';

import { getConfig } from '../config/masterDatabase.config';
import LoggingService from '../services/LoggingService';
import masterDb from '../services/MasterDatabaseService';
import { NotificationService } from '../services/notificationService';

export const useAppInitializer = () => {
  useEffect(() => {
    // PRODUCTION FIX: Run initialization in background without blocking UI
    const initializeInBackground = () => {
      // Use setTimeout to defer initialization and prevent blocking
      setTimeout(async () => {
        if (!__DEV__) {return;}

        const initialize = async () => {
          // PRODUCTION FIX: Add timeout protection for database initialization
          try {
            LoggingService.info('Initializing Master Database Service...', 'MASTER_DB');
            const config = getConfig('development');
            
            // Add timeout for database initialization
            const dbInitTimeout = new Promise<void>((_, reject) => {
              setTimeout(() => reject(new Error('Database initialization timeout')), 8000);
            });
            
            const dbInitPromise = masterDb.initialize({ databaseName: config.database.name });
            
            await Promise.race([dbInitPromise, dbInitTimeout]);
            LoggingService.info('Master DB initialized successfully.', 'MASTER_DB');
          } catch (error) {
            LoggingService.error('Failed to initialize Master DB', 'MASTER_DB', error as Error);
          }

          // PRODUCTION FIX: Seed notification data with timeout protection
          try {
            const seedTimeout = new Promise<void>((_, reject) => {
              setTimeout(() => reject(new Error('Notification seeding timeout')), 5000);
            });
            
            const seedPromise = NotificationService.seedDummyData();
            
            await Promise.race([seedPromise, seedTimeout]);
          } catch (error: any) {
            LoggingService.warn('Notification seeding failed or timed out', 'APP', error);
            if (error.message?.includes('too large')) {
              LoggingService.warn('Storage overflow, clearing notification data', 'APP');
              NotificationService.clearAllNotificationData().catch(clearError => 
                LoggingService.error('Failed to clear notification data', 'APP', clearError)
              );
            }
          }
        };

        initialize().catch(error => {
          LoggingService.error('App initialization failed', 'APP', error);
        });
      }, 100); // Small delay to let UI render first
    };

    initializeInBackground();
  }, []);
};