import { useState, useReducer, useMemo, useEffect } from 'react';

import { InventoryItem, InventoryFilters } from '../types/inventory';

type FilterAction =
  | { type: 'SET_SORT'; payload: { sortBy: string; sortOrder: 'asc' | 'desc' } }
  | { type: 'TOGGLE_ACTIVE' }
  | { type: 'CLEAR' };

const initialFilters = { isActive: true, sortBy: 'name', sortOrder: 'asc' as 'asc' | 'desc' };

function filtersReducer(state: typeof initialFilters, action: FilterAction) {
  switch (action.type) {
    case 'SET_SORT':
      return { ...state, sortBy: action.payload.sortBy, sortOrder: action.payload.sortOrder };
    case 'TOGGLE_ACTIVE':
      return { ...state, isActive: !state.isActive };
    case 'CLEAR':
      return initialFilters;
    default:
      return state;
  }
}

export const useInventoryFilter = (items: InventoryItem[], onFiltersChange?: (filters: InventoryFilters) => void) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [filters, dispatch] = useReducer(filtersReducer, initialFilters);

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedQuery(searchQuery), 300);
    return () => clearTimeout(handler);
  }, [searchQuery]);

  const filteredItems = useMemo(() => {
    let filtered = [...items];
    const query = debouncedQuery.toLowerCase().trim();

    if (query) {
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(query) || item.category.toLowerCase().includes(query)
      );
    }

    if (filters.isActive) {
      filtered = filtered.filter(item => item.isActive);
    }

    filtered.sort((a, b) => {
      const aValue = a[filters.sortBy as keyof InventoryItem] ?? '';
      const bValue = b[filters.sortBy as keyof InventoryItem] ?? '';
      if (aValue < bValue) {return filters.sortOrder === 'asc' ? -1 : 1;}
      if (aValue > bValue) {return filters.sortOrder === 'asc' ? 1 : -1;}
      return 0;
    });

    return filtered;
  }, [items, debouncedQuery, filters]);

  useEffect(() => {
    onFiltersChange?.({ search: debouncedQuery, isActive: filters.isActive });
  }, [debouncedQuery, filters, onFiltersChange]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (searchQuery.trim()) {count++;}
    if (!filters.isActive) {count++;}
    return count;
  }, [searchQuery, filters.isActive]);

  const clearFilters = () => {
    setSearchQuery('');
    dispatch({ type: 'CLEAR' });
  };

  return { searchQuery, setSearchQuery, filteredItems, filters, dispatch, activeFilterCount, clearFilters };
};