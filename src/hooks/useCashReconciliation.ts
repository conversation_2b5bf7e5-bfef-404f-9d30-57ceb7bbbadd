import { useState, useCallback, useMemo } from 'react';

import { FINANCIAL_CONFIG } from '../config/constants';
import { useFinancial } from '../context/FinancialContext';
import { ExpectedCashData } from '../types/financial';
import { PhosphorIconName } from '../utils/phosphorIconRegistry';

// --- Types ---
interface ReconciliationForm {
  date: string;
  actualCash: string;
  notes: string;
  performedBy: string;
}

interface ReconciliationState {
  form: ReconciliationForm;
  expectedCashData: ExpectedCashData | null;
  isLoading: boolean;
  isCalculating: boolean;
  error: string | null;
  errors: Record<string, string>;
}

// --- Initial State ---
const INITIAL_FORM_STATE: ReconciliationForm = {
  date: new Date().toISOString().split('T')[0],
  actualCash: '',
  notes: '',
  performedBy: 'Manager', // Or get from auth context
};

const INITIAL_STATE: ReconciliationState = {
  form: INITIAL_FORM_STATE,
  expectedCashData: null,
  isLoading: false,
  isCalculating: false,
  error: null,
  errors: {},
};

// --- Helper Functions ---
const validateNumber = (val: string): boolean => !isNaN(Number(val)) && Number(val) >= 0;

// --- The Hook ---
export const useCashReconciliation = () => {
  const { performReconciliation, calculateDailyCashExpected } = useFinancial();
  const [state, setState] = useState<ReconciliationState>(INITIAL_STATE);

  // Load expected cash data when the date changes
  const loadExpectedCash = useCallback(
    async (date: string) => {
      setState(s => ({ ...s, isCalculating: true, expectedCashData: null, error: null }));
      try {
        const data = await calculateDailyCashExpected(date);
        setState(s => ({ ...s, expectedCashData: data, isCalculating: false }));
      } catch (err) {
        const errorMessage = (err as Error).message || 'Could not calculate expected cash.';
        setState(s => ({ ...s, error: errorMessage, isCalculating: false }));
      }
    },
    [calculateDailyCashExpected]
  );

  // Handle form input changes
  const handleInputChange = (field: keyof ReconciliationForm, value: string) => {
    setState(s => ({
      ...s,
      form: { ...s.form, [field]: value },
      errors: { ...s.errors, [field]: '' }, // Clear error on change
    }));
  };

  // Memoized calculations
  const difference = useMemo(() => {
    const actual = parseFloat(state.form.actualCash) || 0;
    const expected = state.expectedCashData?.expectedClosingCash || 0;
    return actual - expected;
  }, [state.form.actualCash, state.expectedCashData]);

  const differenceStatus = useMemo(() => {
    const isBalanced = Math.abs(difference) <= FINANCIAL_CONFIG.RECONCILIATION_TOLERANCE;
    return isBalanced
      ? { status: 'balanced', color: '#4CAF50', icon: 'check-circle' as PhosphorIconName }
      : { status: 'discrepancy', color: '#F44336', icon: 'warning-circle' as PhosphorIconName };
  }, [difference]);

  // Reset to initial state
  const reset = useCallback(() => {
    setState(INITIAL_STATE);
  }, []);

  // Submit the reconciliation
  const submitReconciliation = useCallback(async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};
    if (!validateNumber(state.form.actualCash)) {
      newErrors.actualCash = 'Please enter a valid cash amount';
    }
    if (!state.form.performedBy.trim()) {
      newErrors.performedBy = 'Name is required';
    }

    if (Object.keys(newErrors).length > 0) {
      setState(s => ({ ...s, errors: newErrors }));
      return false;
    }

    setState(s => ({ ...s, isLoading: true, error: null }));
    try {
      const reconciliationData = {
        ...state.form,
        actualCash: parseFloat(state.form.actualCash),
        expectedCash: state.expectedCashData?.expectedClosingCash || 0,
        difference,
        status: differenceStatus.status,
      };

      await performReconciliation(reconciliationData);
      setState(s => ({ ...s, isLoading: false }));
      return true;
    } catch (err) {
      const errorMessage = (err as Error).message || 'Failed to perform reconciliation.';
      setState(s => ({ ...s, isLoading: false, error: errorMessage }));
      return false;
    }
  }, [state, difference, differenceStatus.status, performReconciliation]);

  return {
    state,
    difference,
    differenceStatus,
    loadExpectedCash,
    handleInputChange,
    submitReconciliation,
    reset,
  };
};