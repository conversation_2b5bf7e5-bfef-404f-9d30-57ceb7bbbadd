import { useCallback, Dispatch } from 'react';

import { FinancialService } from '../services/financialService';
import {
  ActionTypes,
  CashReconciliation,
  Expense,
  ExpectedCashData,
  FinancialAction,
  PaymentAnalytics,
  ProfitLossData,
  TaxSummary,
} from '../types/financial';

export const useFinancialActions = (dispatch: Dispatch<FinancialAction>) => {
  const loadExpenses = useCallback(async (filters: Record<string, any> = {}): Promise<void> => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const expenses = await FinancialService.getExpenses(filters);
      dispatch({ type: ActionTypes.SET_EXPENSES, payload: expenses });
    } catch (error) {
      dispatch({ type: ActionTypes.SET_ERROR, payload: 'Failed to load expenses' });
    }
  }, [dispatch]);

  const addExpense = useCallback(async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> => {
    const newExpense = await FinancialService.addExpense(expenseData);
    dispatch({ type: ActionTypes.ADD_EXPENSE, payload: newExpense });
    return newExpense;
  }, [dispatch]);

  const updateExpense = useCallback(async (id: string, updates: Partial<Expense>): Promise<Expense> => {
    const updatedExpense = await FinancialService.updateExpense(id, updates);
    dispatch({ type: ActionTypes.UPDATE_EXPENSE, payload: updatedExpense });
    return updatedExpense;
  }, [dispatch]);

  const deleteExpense = useCallback(async (id: string): Promise<void> => {
    await FinancialService.deleteExpense(id);
    dispatch({ type: ActionTypes.DELETE_EXPENSE, payload: id });
  }, [dispatch]);

  const loadReconciliations = useCallback(async (filters: Record<string, any> = {}): Promise<void> => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      const reconciliations = await FinancialService.getCashReconciliations(filters);
      dispatch({ type: ActionTypes.SET_RECONCILIATIONS, payload: reconciliations });
    } catch (error) {
      dispatch({ type: ActionTypes.SET_ERROR, payload: 'Failed to load reconciliations' });
    }
  }, [dispatch]);

  const performReconciliation = useCallback(async (
    reconciliationData: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<CashReconciliation> => {
    const newReconciliation = await FinancialService.performCashReconciliation(reconciliationData);
    dispatch({ type: ActionTypes.ADD_RECONCILIATION, payload: newReconciliation });
    return newReconciliation;
  }, [dispatch]);

  const calculateDailyCashExpected = useCallback(async (date: string, outletId?: string): Promise<ExpectedCashData> => {
    return await FinancialService.getExpectedCashBreakdown(date, outletId);
  }, []);

  const generateProfitLossStatement = useCallback(async (startDate: string, endDate: string): Promise<ProfitLossData> => {
    const profitLoss = await FinancialService.generateProfitLossStatement(startDate, endDate);
    dispatch({ type: ActionTypes.SET_PROFIT_LOSS, payload: profitLoss });
    return profitLoss;
  }, [dispatch]);

  const getPaymentMethodAnalytics = useCallback(async (startDate: string, endDate: string): Promise<PaymentAnalytics> => {
    const analytics = await FinancialService.getPaymentMethodAnalytics(startDate, endDate);
    dispatch({ type: ActionTypes.SET_PAYMENT_ANALYTICS, payload: analytics });
    return analytics;
  }, [dispatch]);

  const getTaxSummary = useCallback(async (startDate: string, endDate: string): Promise<TaxSummary> => {
    const taxSummary = await FinancialService.getTaxSummary(startDate, endDate);
    dispatch({ type: ActionTypes.SET_TAX_SUMMARY, payload: taxSummary });
    return taxSummary;
  }, [dispatch]);

  const clearError = useCallback((): void => {
    dispatch({ type: ActionTypes.SET_ERROR, payload: null });
  }, [dispatch]);

  const clearData = useCallback((): void => {
    dispatch({ type: ActionTypes.CLEAR_DATA });
  }, [dispatch]);

  return {
    loadExpenses,
    addExpense,
    updateExpense,
    deleteExpense,
    loadReconciliations,
    performReconciliation,
    calculateDailyCashExpected,
    generateProfitLossStatement,
    getPaymentMethodAnalytics,
    getTaxSummary,
    clearError,
    clearData,
  };
};