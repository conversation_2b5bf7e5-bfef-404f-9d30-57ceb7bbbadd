import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { Order as OrderDetailsOrder } from './order';

import { Order as AppOrder, Customer } from './index';

// Root Stack Navigator Param List
export type RootStackParamList = {
  // Auth
  Login: undefined;
  RoleSelection: undefined;
  AdminLogin: undefined;
  StaffLogin: undefined;

  // Main App
  Main: undefined;
  TabNavigator: undefined;

  // Business
  Dashboard: undefined;
  Orders: undefined;
    OrderDetails: { order: OrderDetailsOrder };
  AddOrder: undefined;
  CreateOrder: { order?: OrderDetailsOrder, isDuplicate?: boolean, mode?: 'edit', orderId?: string } | undefined;
  AddItem: { itemId?: string; itemType?: 'product' | 'fabric' | 'accessory' | 'service' } | undefined;
  AddProduct: undefined; // Legacy - redirects to AddItem
  AddFabricScreen: undefined;
  Products: undefined;
  Customers: undefined;
  CustomerDetails: { customerId: string };
  AddCustomer: { isEditing?: boolean; customerData?: any };
  Financial: undefined;
  Reports: undefined;
  PaymentAnalytics: undefined;
  ProfitLoss: undefined;
  TaxSummary: undefined;

  // Service Types Management
  ServiceTypes: undefined;
    AddService: { id?: string } | undefined;

  // Inventory
  InventoryDashboard: undefined;
  InventoryItems: undefined;
  AddEditInventoryItem: { itemId?: string; mode?: 'edit' };
  InventoryItemDetail: { itemId: string };
  StockOperations: { operation: 'in' | 'out'; itemId: string };
  StockTransfer: undefined;
  TransactionHistory: undefined;

  // Management
  StaffManagement: undefined;
  AddStaff: undefined;
  StaffDetails: { staffId: string };
  
  WarehouseManagement: undefined;
  PaymentMethods: undefined;

  // Settings
  Profile: undefined;
  EditProfile: { profileData?: any };
  Notifications: undefined;
  ActivityLog: undefined;
  CacheManagement: undefined;
  DataManagement: undefined;

  // Support
  HelpFAQ: undefined;
  ContactSupport: undefined;
  About: undefined;
  Search: undefined;
  AppStatus: undefined;
  ToastDemo: undefined;

  // Order flows
  OrderSuccess: { order: AppOrder, customer?: Customer };
  QRScanner: undefined;
  QROrderDetails: { orderInfo: any; qrData: any; scannedAt: string };

  // Utils
  
};

// Navigation Props
export type RootStackNavigationProp = StackNavigationProp<RootStackParamList>;

// Screen-specific navigation props
export type AddFabricScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'AddFabricScreen'
>;
export type StaffManagementScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'StaffManagement'
>;
export type SearchScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Search'>;
export type AddStaffScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AddStaff'>;
export type StaffDetailsScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'StaffDetails'
>;

export type FinancialScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Financial'
>;


// Screen-specific route props
export type AddFabricScreenRouteProp = RouteProp<RootStackParamList, 'AddFabricScreen'>;
export type StaffManagementScreenRouteProp = RouteProp<RootStackParamList, 'StaffManagement'>;
export type SearchScreenRouteProp = RouteProp<RootStackParamList, 'Search'>;
export type AddStaffScreenRouteProp = RouteProp<RootStackParamList, 'AddStaff'>;
export type StaffDetailsScreenRouteProp = RouteProp<RootStackParamList, 'StaffDetails'>;

// Screen Props Interfaces

export interface AddFabricScreenProps {
  navigation: AddFabricScreenNavigationProp;
  route: AddFabricScreenRouteProp;
}

export interface StaffManagementScreenProps {
  navigation: StaffManagementScreenNavigationProp;
  route: StaffManagementScreenRouteProp;
}

export interface SearchScreenProps {
  navigation: SearchScreenNavigationProp;
  route: SearchScreenRouteProp;
}

export interface AddStaffScreenProps {
  navigation: AddStaffScreenNavigationProp;
  route: AddStaffScreenRouteProp;
}

export interface StaffDetailsScreenProps {
  navigation: StaffDetailsScreenNavigationProp;
  route: StaffDetailsScreenRouteProp;
}

export interface FinancialScreenProps {
  navigation: FinancialScreenNavigationProp;
  route: RouteProp<RootStackParamList, 'Financial'>;
}

// Generic screen props for screens that don't need specific typing
export interface GenericScreenProps {
  navigation?: RootStackNavigationProp;
  route?: RouteProp<RootStackParamList, keyof RootStackParamList>;
}

// Missing navigation types
export interface AboutScreenProps {
  navigation: RootStackNavigationProp;
  route?: RouteProp<RootStackParamList, 'About'>;
}

export interface DataScreenProps {
  navigation: RootStackNavigationProp;
  route?: RouteProp<RootStackParamList, 'DataManagement'>;
}

export interface StockOperationsScreenProps {
  navigation: RootStackNavigationProp;
  route?: RouteProp<RootStackParamList, 'StockOperations'>;
}

export interface StockTransferScreenProps {
  navigation: RootStackNavigationProp;
  route?: RouteProp<RootStackParamList, 'StockTransfer'>;
}

export interface PaymentMethodsScreenProps {
  navigation: RootStackNavigationProp;
  route?: RouteProp<RootStackParamList, 'PaymentMethods'>;
}


