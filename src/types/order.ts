import { OrderType } from './index'; // Import OrderType

export type OrderStatus =
  | 'Pending'
  | 'In Progress'
  | 'Ready'
  | 'Completed'
  | 'Delivered'
  | 'Cancelled'
  | 'On Hold';

export interface OrderItem {
  id: string | number;
  name?: string; // Added
  // Canonical field
  serviceType?: string;
  service_type?: string;
  quantity: number;
  status?: OrderStatus;
  assignedTo?: string;
  productName?: string; // Added
  unitPrice?: number; // Added
  price?: number; // Added (for legacy/compatibility)
}

export interface Payment {
  id: string | number;
  amount: number;
  method?: string;
  reference?: string;
  date?: string;
  timestamp?: string;
  user?: string;
}

export interface Order {
  id: string;
  customerId: string; // Added from index.ts
  staffId?: string; // Added from index.ts
  outletId?: string; // Added from index.ts
  orderNumber: string; // Added from index.ts
  status: OrderStatus;
  orderDate: string; // Added from index.ts
  dueDate?: string;
  completedDate?: string; // Added from index.ts
  subtotal: number; // Added from index.ts
  taxAmount: number; // Added from index.ts
  discountAmount: number; // Added from index.ts
  totalAmount: number; // Added from index.ts
  paidAmount: number;
  paymentStatus: string; // Added from index.ts
  notes?: string;
  metadata?: string; // Added from index.ts
  items: OrderItem[];
  payments?: Payment[];
  customerName?: string;
  customer?: any;
  phone?: string;
  email?: string;
  address?: string;
  date?: string;
  time?: string; // Added from index.ts
  orderType?: OrderType; // Added from index.ts
  tax?: number; // Added from index.ts
  discount?: number; // Added from index.ts
  total?: number;
  image?: string; // Added from index.ts
  assignedTo?: string; // Added from index.ts
  createdAt: string;
  updatedAt: string;
  estimatedTime?: number; // Added
  // Properties from original order.ts that are still relevant
  deliveryNotes?: string;
  customer_phone?: string;
  customer_email?: string;
  created_at?: string;
  due_date?: string;
  amount?: number;
  advance_paid?: number;
  balanceAmount?: number;
  isVIP?: boolean;
  deliveryStatus?: 'pending' | 'scheduled' | 'out_for_delivery' | 'delivered';
  paymentDate?: string;
}

export interface TimelineEvent {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp?: string;
  user: string;
  color?: string;
}