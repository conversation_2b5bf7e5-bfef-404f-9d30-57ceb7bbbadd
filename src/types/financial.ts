import { Order } from './business';

// Core Financial Types
export interface Expense {
  id: string;
  category: string;
  amount: number;
  description: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  outletId?: string; // For multi-outlet support
}

export interface CashReconciliation {
  id: string;
  date: string;
  expectedCash: number;
  actualCash: number;
  difference: number;
  status: 'balanced' | 'discrepancy' | string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  outletId?: string; // For multi-outlet support
}

// Data structure for expected cash calculation
export interface ExpectedCashData {
  openingCash: number;
  cashSales: number;
  orderCount: number;
  cashExpenses: number;
  expectedClosingCash: number;
}

// Filter Types
export interface ExpenseFilters {
  category?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  outletId?: string;
}

export interface ReconciliationFilters {
  startDate?: string;
  endDate?: string;
  status?: string;
  outletId?: string;
}

// Analytics and Report Types
export interface ProfitLossData {
  revenue: number;
  expenses: number;
  grossProfit: number;
  netProfit: number;
  profitMargin: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

export interface PaymentAnalytics {
  totalTransactions: number;
  totalAmount: number;
  paymentMethods: Record<
    string,
    {
      count: number;
      amount: number;
      percentage: number;
    }
  >;
  trends: Array<{
    date: string;
    amount: number;
    method: string;
  }>;
}

export interface TaxSummary {
  totalTaxableAmount: number;
  totalTaxCollected: number;
  taxRate: number;
  period: {
    startDate: string;
    endDate: string;
  };
  breakdown: Record<
    string,
    {
      amount: number;
      tax: number;
    }
  >;
}

// State and Context Types
export interface DerivedData {
  totalExpenses: number;
  expensesByCategory: Record<string, number>;
  recentReconciliations: CashReconciliation[];
  reconciliationTrend: Array<{
    date: string;
    difference: number;
    status: string;
  }>;
}

export interface FinancialState {
  expenses: Expense[];
  reconciliations: CashReconciliation[];
  profitLossData: ProfitLossData | null;
  paymentAnalytics: PaymentAnalytics | null;
  taxSummary: TaxSummary | null;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// Action related types
export enum ActionTypes {
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  SET_EXPENSES = 'SET_EXPENSES',
  ADD_EXPENSE = 'ADD_EXPENSE',
  UPDATE_EXPENSE = 'UPDATE_EXPENSE',
  DELETE_EXPENSE = 'DELETE_EXPENSE',
  SET_RECONCILIATIONS = 'SET_RECONCILIATIONS',
  ADD_RECONCILIATION = 'ADD_RECONCILIATION',
  SET_PROFIT_LOSS = 'SET_PROFIT_LOSS',
  SET_PAYMENT_ANALYTICS = 'SET_PAYMENT_ANALYTICS',
  SET_TAX_SUMMARY = 'SET_TAX_SUMMARY',
  CLEAR_DATA = 'CLEAR_DATA',
}

export type FinancialAction =
  | { type: ActionTypes.SET_LOADING; payload: boolean }
  | { type: ActionTypes.SET_ERROR; payload: string | null }
  | { type: ActionTypes.SET_EXPENSES; payload: Expense[] }
  | { type: ActionTypes.ADD_EXPENSE; payload: Expense }
  | { type: ActionTypes.UPDATE_EXPENSE; payload: Expense }
  | { type: ActionTypes.DELETE_EXPENSE; payload: string }
  | { type: ActionTypes.SET_RECONCILIATIONS; payload: CashReconciliation[] }
  | { type: ActionTypes.ADD_RECONCILIATION; payload: CashReconciliation }
  | { type: ActionTypes.SET_PROFIT_LOSS; payload: ProfitLossData }
  | { type: ActionTypes.SET_PAYMENT_ANALYTICS; payload: PaymentAnalytics }
  | { type: ActionTypes.SET_TAX_SUMMARY; payload: TaxSummary }
  | { type: ActionTypes.CLEAR_DATA };