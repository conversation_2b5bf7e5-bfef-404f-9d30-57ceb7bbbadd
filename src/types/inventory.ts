/**
 * Type definitions for Inventory Management System
 */

// Core inventory item interface
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  baseUnit: string;
  availableUnits: string[]; // JSON array stored as string in DB
  purchasePrice: number;
  sellingPrice: number;
  minimumStockLevel: number;
  imageUri?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Inventory stock interface
export interface InventoryStock {
  id: string;
  itemId: string;
  warehouseId: string;
  quantity: number;
  unit: string;
  reservedQuantity: number;
  updatedAt: string;
}

// Warehouse interface
export interface Warehouse {
  id: string;
  name: string;
  location: string;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Inventory transaction interface
export interface InventoryTransaction {
  id: string;
  itemId: string;
  warehouseId: string;
  type: 'IN' | 'OUT' | 'TRANSFER' | 'ADJUSTMENT';
  quantity: number;
  unit: string;
  convertedQuantity: number; // Quantity in base unit
  reference?: string;
  note?: string;
  performedBy: string;
  date: string;
  createdAt: string;
}

// Stock transfer interface
export interface StockTransfer {
  id: string;
  itemId: string;
  fromWarehouseId: string;
  toWarehouseId: string;
  quantity: number;
  unit: string;
  convertedQuantity: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  completedAt?: string;
}

// Database row interfaces (for SQLite results)
export interface InventoryItemRow {
  id: string;
  name: string;
  category: string;
  base_unit: string;
  available_units: string; // JSON string
  purchase_price: number;
  selling_price: number;
  minimum_stock_level: number;
  image_uri?: string;
  is_active: number; // SQLite boolean as integer
  created_at: string;
  updated_at: string;
}

export interface InventoryStockRow {
  id: string;
  item_id: string;
  warehouse_id: string;
  quantity: number;
  unit: string;
  reserved_quantity: number;
  updated_at: string;
}

export interface WarehouseRow {
  id: string;
  name: string;
  location: string;
  is_default: number; // SQLite boolean as integer
  is_active: number; // SQLite boolean as integer
  created_at: string;
  updated_at: string;
}

export interface InventoryTransactionRow {
  id: string;
  item_id: string;
  warehouse_id: string;
  type: string;
  quantity: number;
  unit: string;
  converted_quantity: number;
  reference?: string;
  note?: string;
  performed_by: string;
  created_at: string;
}

export interface StockTransferRow {
  id: string;
  item_id: string;
  from_warehouse_id: string;
  to_warehouse_id: string;
  quantity: number;
  unit: string;
  converted_quantity: number;
  status: string;
  created_at: string;
  completed_at?: string;
}

// Filter interfaces
export interface InventoryFilters {
  isActive?: boolean;
  category?: string;
  search?: string;
  warehouseId?: string;
  lowStock?: boolean;
}

export interface TransactionFilters {
  itemId?: string;
  warehouseId?: string;
  type?: InventoryTransaction['type'];
  dateFrom?: string;
  dateTo?: string;
  performedBy?: string;
}

export interface StockFilters {
  itemId?: string;
  warehouseId?: string;
  hasStock?: boolean;
}

// Aggregated data interfaces
export interface ItemStockSummary {
  itemId: string;
  itemName: string;
  totalStock: number;
  baseUnit: string;
  stockByWarehouse: {
    warehouseId: string;
    warehouseName: string;
    quantity: number;
    unit: string;
    reservedQuantity: number;
  }[];
  isLowStock: boolean;
  minimumLevel: number;
}

export interface WarehouseStockSummary {
  warehouseId: string;
  warehouseName: string;
  totalItems: number;
  lowStockItems: number;
  items: {
    itemId: string;
    itemName: string;
    quantity: number;
    unit: string;
    isLowStock: boolean;
  }[];
}

// Operation result interfaces
export interface StockOperationResult {
  success: boolean;
  transactionId?: string;
  newStockLevel: number;
  message: string;
  error?: string;
}

export interface TransferOperationResult {
  success: boolean;
  transferId?: string;
  fromStockLevel: number;
  toStockLevel: number;
  message: string;
  error?: string;
}

// Validation interfaces
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface StockValidation {
  hasEnoughStock: boolean;
  availableQuantity: number;
  requestedQuantity: number;
  message: string;
}

// Dashboard data interfaces
export interface InventoryDashboardData {
  totalItems: number;
  totalWarehouses: number;
  lowStockItems: InventoryItem[];
  recentTransactions: InventoryTransaction[];
  topCategories: {
    category: string;
    itemCount: number;
    totalValue: number;
  }[];
  stockValue: {
    totalPurchaseValue: number;
    totalSellingValue: number;
    potentialProfit: number;
  };
}

// Search result interface
export interface InventorySearchResult {
  items: InventoryItem[];
  warehouses: Warehouse[];
  transactions: InventoryTransaction[];
  totalResults: number;
}
