/**
 * Simple logging service for development and debugging
 * In production, this could be replaced with a more sophisticated logging solution
 */
class LoggingService {
  private static instance: LoggingService;
  private isDevelopment = __DEV__;

  static getInstance(): LoggingService {
    if (!LoggingService.instance) {
      LoggingService.instance = new LoggingService();
    }
    return LoggingService.instance;
  }

  info(message: string, category?: string, data?: any): void {
    if (this.isDevelopment) {
      // Production environments should use a proper logging service
      if (__DEV__) {
        console.log(`[INFO]${category ? ` [${category}]` : ''}: ${message}`, data || '');
      }
    }
  }

  warn(message: string, category?: string, data?: any): void {
    if (this.isDevelopment) {
      console.warn(`[WARN]${category ? ` [${category}]` : ''}: ${message}`, data || '');
    }
  }

  error(message: string, category?: string, error?: Error | any): void {
    if (this.isDevelopment) {
      console.error(`[ERROR]${category ? ` [${category}]` : ''}: ${message}`, error || '');
    }
  }

  debug(message: string, category?: string, data?: any): void {
    if (this.isDevelopment) {
      console.debug(`[DEBUG]${category ? ` [${category}]` : ''}: ${message}`, data || '');
    }
  }
}

export default LoggingService.getInstance();
