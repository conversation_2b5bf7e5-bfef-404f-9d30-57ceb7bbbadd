import React from 'react';

import { AppError } from '../utils/errorHandler';

import LoggingService from './LoggingService';
import { StorageService } from './StorageService';


// ---------------- Types ----------------
export interface NotificationTemplate {
  id: string;
  name: string;
  message: string;
  category: NotificationCategory;
  isActive: boolean;
  createdAt: string;
}

export type NotificationCategory =
  | "order_status"
  | "payment"
  | "pickup"
  | "reminder"
  | "general";

export interface NotificationPreference {
  category: NotificationCategory;
  enabled: boolean;
}

export interface NotificationLog {
  id: string;
  category: NotificationCategory;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  data?: { action?: () => void; actionType?: string; actionText?: string };
}

export interface PickupReminder {
  orderId: string;
  reminderType: "ready" | "reminder" | "final";
  scheduledFor: string;
  sent: boolean;
}

// ---------------- Config ----------------
const REMINDER_OFFSETS = {
  payment: { due: -1, overdue: +1, final: +7 }, // days relative to dueDate
  pickup: { ready: 0, reminder: +3, final: +7 }, // days relative to readyDate
};

// ---------------- Service ----------------
export class NotificationService {
    static async clearAllNotificationData(): Promise<void> {
    try {
      await this.clearAll();
      return Promise.resolve();
    } catch (error) {
      return this.handleError("clear all notification data", error);
    }
  }
  static getNotificationStorageInfo() {
    throw new Error('Method not implemented.');
  }
  // Centralized error handler
  private static handleError(action: string, error: unknown): Promise<never> {
    LoggingService.error(`Failed to ${action}:`, "NOTIFICATION", error as Error);
    return Promise.reject(new AppError(`Failed to ${action}`));
  }

  // ---------- Templates ----------
  static async createTemplate(template: Omit<NotificationTemplate, "id" | "createdAt">) {
    try {
      const templates = (await StorageService.get<NotificationTemplate[]>("notificationTemplates")) || [];
      const newTemplate: NotificationTemplate = {
        ...template,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      };
      templates.push(newTemplate);
      await StorageService.set("notificationTemplates", templates);
      return newTemplate;
    } catch (error) {
      throw this.handleError("create notification template", error);
    }
  }

  static async getTemplates() {
    return (await StorageService.get<NotificationTemplate[]>("notificationTemplates")) || [];
  }

  // ---------- Preferences ----------
  static async getPreferences() {
    return (await StorageService.get<NotificationPreference[]>("notificationPreferences")) || [];
  }

  static async updatePreference(category: NotificationCategory, enabled: boolean) {
    try {
      const prefs = (await this.getPreferences()) || [];
      const idx = prefs.findIndex((p) => p.category === category);
      if (idx > -1) {prefs[idx].enabled = enabled;}
      else {prefs.push({ category, enabled });}
      await StorageService.set("notificationPreferences", prefs);
    } catch (error) {
      throw this.handleError("update preferences", error);
    }
  }

  // ---------- Logs ----------
  static async logNotification(log: Omit<NotificationLog, "id" | "timestamp" | "read">) {
    try {
      const logs = (await StorageService.get<NotificationLog[]>("notificationLogs")) || [];
      const newLog: NotificationLog = {
        ...log,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
      };
      logs.unshift(newLog); // newest first
      await StorageService.set("notificationLogs", logs);
      return newLog;
    } catch (error) {
      throw this.handleError("log notification", error);
    }
  }

  static async getLogs() {
    return (await StorageService.get<NotificationLog[]>("notificationLogs")) || [];
  }

  static async markAsRead(id: string) {
    try {
      const logs = (await this.getLogs()) || [];
      const idx = logs.findIndex((l) => l.id === id);
      if (idx > -1) {logs[idx].read = true;}
      await StorageService.set("notificationLogs", logs);
    } catch (error) {
      throw this.handleError("mark notification as read", error);
    }
  }

  static async deleteNotification(id: string) {
    try {
      let logs = (await this.getLogs()) || [];
      logs = logs.filter((l) => l.id !== id);
      await StorageService.set("notificationLogs", logs);
    } catch (error) {
      throw this.handleError("delete notification", error);
    }
  }

  // ---------- Reminders ----------
  static calculateReminderTime(type: "payment" | "pickup", baseDate: Date, reminder: string) {
    const offset =
      type === "payment"
        ? REMINDER_OFFSETS.payment[reminder as keyof typeof REMINDER_OFFSETS.payment]
        : REMINDER_OFFSETS.pickup[reminder as keyof typeof REMINDER_OFFSETS.pickup];
    const scheduled = new Date(baseDate);
    scheduled.setDate(baseDate.getDate() + offset);
    return scheduled;
  }

  static async schedulePickupReminder(orderId: string, readyDate: string, reminderType: "ready" | "reminder" | "final") {
    try {
      const readyDateTime = new Date(readyDate);
      const scheduledFor = this.calculateReminderTime("pickup", readyDateTime, reminderType);
      const reminders = (await StorageService.get<PickupReminder[]>("pickupReminders")) || [];
      const newReminder: PickupReminder = {
        orderId,
        reminderType,
        scheduledFor: scheduledFor.toISOString(),
        sent: false,
      };
      reminders.push(newReminder);
      await StorageService.set("pickupReminders", reminders);
      return newReminder;
    } catch (error) {
      throw this.handleError("schedule pickup reminder", error);
    }
  }

  // ---------- Seeding ----------
  static async seedDummyData(): Promise<void> {
    try {
      const templates: Omit<NotificationTemplate, "id" | "createdAt">[] = [
        { name: "Order Ready", message: "Your order is ready for pickup!", category: "pickup", isActive: true },
        { name: "Payment Reminder", message: "Your payment is due soon.", category: "payment", isActive: true },
      ];
      await Promise.all(templates.map((t) => this.createTemplate(t)));

      const prefs: NotificationPreference[] = [
        { category: "pickup", enabled: true },
        { category: "payment", enabled: true },
        { category: "order_status", enabled: true },
      ];
      await StorageService.set("notificationPreferences", prefs);
      return Promise.resolve();
    } catch (error) {
      return this.handleError("seed dummy data", error);
    }
  }

  static async generateDummyNotifications(): Promise<void> {
    try {
      const dummyNotifications: Omit<NotificationLog, "id" | "timestamp" | "read">[] = [
        {
          category: "order_status",
          title: "Order Ready for Pickup",
          message: "Order #001 is ready for pickup. Customer: John Doe",
          data: { actionType: "view_order", actionText: "View Order" }
        },
        {
          category: "payment",
          title: "Payment Received",
          message: "Payment of $150.00 received for Order #002",
          data: { actionType: "view_payment", actionText: "View Payment" }
        },
        {
          category: "pickup",
          title: "Pickup Reminder",
          message: "Reminder: Order #003 has been ready for pickup for 2 days",
          data: { actionType: "view_order", actionText: "Contact Customer" }
        },
        {
          category: "general",
          title: "Low Stock Alert",
          message: "Cotton fabric stock is running low (5 units remaining)",
          data: { actionType: "restock", actionText: "Manage Inventory" }
        },
        {
          category: "reminder",
          title: "Daily Backup Reminder",
          message: "Don't forget to backup your data today",
          data: { actionType: "system_update", actionText: "Backup Now" }
        }
      ];

      for (const notification of dummyNotifications) {
        await this.logNotification(notification);
      }

      LoggingService.info('Dummy notifications generated successfully', 'NOTIFICATION_SERVICE');
    } catch (error) {
      throw this.handleError("generate dummy notifications", error);
    }
  }

  static async clearAll() {
    try {
      await StorageService.remove("notificationTemplates");
      await StorageService.remove("notificationPreferences");
      await StorageService.remove("notificationLogs");
      await StorageService.remove("pickupReminders");
    } catch (error) {
      throw this.handleError("clear all notification data", error);
    }
  }

  // ---------- Hook Integration ----------
  static async getPendingReminders() {
    try {
      const reminders = (await StorageService.get<PickupReminder[]>("pickupReminders")) || [];
      const now = new Date();
      return reminders.filter(r => !r.sent && new Date(r.scheduledFor) <= now);
    } catch (error) {
      LoggingService.error("Failed to get pending reminders", "NOTIFICATION", error as Error);
      return [];
    }
  }

  static async markReminderSent(orderId: string, reminderType: PickupReminder['reminderType']) {
    try {
      const reminders = (await StorageService.get<PickupReminder[]>("pickupReminders")) || [];
      const idx = reminders.findIndex(r => r.orderId === orderId && r.reminderType === reminderType);
      if (idx > -1) {
        reminders[idx].sent = true;
        await StorageService.set("pickupReminders", reminders);
      }
    } catch (error) {
      throw this.handleError("mark reminder as sent", error);
    }
  }

  // ---------- Analytics ----------
  static async getNotificationStats() {
    try {
      const logs = await this.getLogs();
      const total = logs.length;
      const read = logs.filter(l => l.read).length;
      const unread = total - read;
      
      const byCategory = logs.reduce((acc, log) => {
        acc[log.category] = (acc[log.category] || 0) + 1;
        return acc;
      }, {} as Record<NotificationCategory, number>);

      return { total, read, unread, byCategory };
    } catch (error) {
      LoggingService.error("Failed to get notification stats", "NOTIFICATION", error as Error);
      return { total: 0, read: 0, unread: 0, byCategory: {} };
    }
  }
}

// ---------- Hook ----------
export const useNotifications = () => {
  const [notifications, setNotifications] = React.useState<NotificationLog[]>([]);
  const [templates, setTemplates] = React.useState<NotificationTemplate[]>([]);
  const [preferences, setPreferences] = React.useState<NotificationPreference[]>([]);
  const [unreadCount, setUnreadCount] = React.useState(0);
  const [loading, setLoading] = React.useState(true);

  const loadData = React.useCallback(async () => {
    try {
      setLoading(true);
      const [logs, temps, prefs] = await Promise.all([
        NotificationService.getLogs(),
        NotificationService.getTemplates(),
        NotificationService.getPreferences(),
      ]);
      setNotifications(logs);
      setTemplates(temps);
      setPreferences(prefs);
      setUnreadCount(logs.filter(l => !l.read).length);
    } catch (error) {
      LoggingService.error("Failed to load notification data", "NOTIFICATION_HOOK", error as Error);
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    loadData();
  }, [loadData]);

  const markAsRead = React.useCallback(async (id: string) => {
    try {
      await NotificationService.markAsRead(id);
      await loadData(); // Refresh data
    } catch (error) {
      LoggingService.error("Failed to mark notification as read", "NOTIFICATION_HOOK", error as Error);
    }
  }, [loadData]);

  const deleteNotification = React.useCallback(async (id: string) => {
    try {
      await NotificationService.deleteNotification(id);
      await loadData(); // Refresh data
    } catch (error) {
      LoggingService.error("Failed to delete notification", "NOTIFICATION_HOOK", error as Error);
    }
  }, [loadData]);

  const createNotification = React.useCallback(async (notification: Omit<NotificationLog, "id" | "timestamp" | "read">) => {
    try {
      await NotificationService.logNotification(notification);
      await loadData(); // Refresh data
    } catch (error) {
      LoggingService.error("Failed to create notification", "NOTIFICATION_HOOK", error as Error);
    }
  }, [loadData]);

  const generateDummyNotifications = React.useCallback(async () => {
    try {
      await NotificationService.generateDummyNotifications();
      await loadData(); // Refresh data after generating
    } catch (error) {
      LoggingService.error("Failed to generate dummy notifications", "NOTIFICATION_HOOK", error as Error);
    }
  }, [loadData]);

  return {
    notifications,
    templates,
    preferences,
    unreadCount,
    loading,
    markAsRead,
    deleteNotification,
    createNotification,
    generateDummyNotifications,
    refresh: loadData,
  };
};