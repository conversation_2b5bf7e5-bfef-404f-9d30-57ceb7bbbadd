import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';

import SecurityService, { getSecurityService } from '../SecurityService';

// Mock dependencies
jest.mock('expo-secure-store', () => ({
  setItemAsync: jest.fn(),
  getItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

jest.mock('expo-crypto', () => ({
  getRandomBytesAsync: jest.fn(),
}));

jest.mock('react-native', () => ({
  Platform: { OS: 'ios' },
}));

jest.mock('../LoggingService', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

const mockSecureStore = SecureStore as jest.Mocked<typeof SecureStore>;
const mockCrypto = Crypto as jest.Mocked<typeof Crypto>;

describe('SecurityService', () => {
  let securityService: SecurityService;

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Mock random bytes generation
    mockCrypto.getRandomBytesAsync.mockResolvedValue(new Uint8Array([
      0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
      0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
      0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
      0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20
    ]));

    securityService = await getSecurityService();
  });

  describe('getInstance', () => {
    it('should return singleton instance', async () => {
      const instance1 = await getSecurityService();
      const instance2 = await getSecurityService();
      
      expect(instance1).toBe(instance2);
    });

    it('should initialize encryption key on first access', async () => {
      const instance = await getSecurityService();
      expect(instance).toBeDefined();
      expect(mockCrypto.getRandomBytesAsync).toHaveBeenCalledWith(32);
    });
  });

  describe('encryption and decryption', () => {
    it('should encrypt and decrypt data successfully', async () => {
      const originalData = 'sensitive information';
      
      const encrypted = await securityService.encrypt(originalData);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(originalData);
      expect(encrypted).toContain(':'); // Should contain IV separator

      const decrypted = await securityService.decrypt(encrypted);
      expect(decrypted).toBe(originalData);
    });

    it('should generate different encrypted values for same input', async () => {
      const data = 'test data';
      
      // Mock different IV for each call
      mockCrypto.getRandomBytesAsync
        .mockResolvedValueOnce(new Uint8Array(12).fill(1))
        .mockResolvedValueOnce(new Uint8Array(12).fill(2));

      const encrypted1 = await securityService.encrypt(data);
      const encrypted2 = await securityService.encrypt(data);
      
      expect(encrypted1).not.toBe(encrypted2);
      
      const decrypted1 = await securityService.decrypt(encrypted1);
      const decrypted2 = await securityService.decrypt(encrypted2);
      
      expect(decrypted1).toBe(data);
      expect(decrypted2).toBe(data);
    });

    it('should handle empty string encryption', async () => {
      const encrypted = await securityService.encrypt('');
      const decrypted = await securityService.decrypt(encrypted);
      
      expect(decrypted).toBe('');
    });

    it('should handle special characters in encryption', async () => {
      const specialData = 'special chars: !@#$%^&*()_+{}|:"<>?[]\\;\'.,/`~';
      
      const encrypted = await securityService.encrypt(specialData);
      const decrypted = await securityService.decrypt(encrypted);
      
      expect(decrypted).toBe(specialData);
    });

    it('should throw error for invalid encrypted data format', async () => {
      await expect(securityService.decrypt('invalid-format'))
        .rejects.toThrow('Failed to decrypt data');
      
      await expect(securityService.decrypt('no-colon-separator'))
        .rejects.toThrow('Failed to decrypt data');
    });

    it('should throw error for corrupted encrypted data', async () => {
      const validEncrypted = await securityService.encrypt('test');
      const corruptedData = validEncrypted.replace(/.$/, 'X'); // Change last character
      
      await expect(securityService.decrypt(corruptedData))
        .rejects.toThrow('Failed to decrypt data');
    });
  });

  describe('secure storage', () => {
    it('should store and retrieve data securely on mobile', async () => {
      const key = 'test-key';
      const value = 'test-value';
      
      mockSecureStore.setItemAsync.mockResolvedValue();
      mockSecureStore.getItemAsync.mockImplementation(async (storageKey) => {
        // Return the encrypted value that would be stored
        if (storageKey === key) {
          return await securityService.encrypt(value);
        }
        return null;
      });

      await securityService.secureStore(key, value);
      const retrieved = await securityService.secureRetrieve(key);

      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        key,
        expect.any(String),
        expect.objectContaining({
          requireAuthentication: false,
          keychainService: 'tailorza-keychain',
        })
      );
      expect(retrieved).toBe(value);
    });

    it('should return null for non-existent keys', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);
      
      const result = await securityService.secureRetrieve('non-existent-key');
      expect(result).toBeNull();
    });

    it('should handle storage errors gracefully', async () => {
      mockSecureStore.setItemAsync.mockRejectedValue(new Error('Storage error'));
      
      await expect(securityService.secureStore('test-key', 'test-value'))
        .rejects.toThrow('Failed to store data securely');
    });

    it('should handle retrieval errors gracefully', async () => {
      mockSecureStore.getItemAsync.mockRejectedValue(new Error('Retrieval error'));
      
      const result = await securityService.secureRetrieve('test-key');
      expect(result).toBeNull();
    });

    it('should handle corrupted stored data gracefully', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue('corrupted-data');
      
      const result = await securityService.secureRetrieve('test-key');
      expect(result).toBeNull();
    });
  });

  describe('input validation', () => {
    describe('email validation', () => {
      it('should validate correct email addresses', () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        validEmails.forEach(email => {
          expect(securityService.validateInput(email, 'email')).toBe(true);
        });
      });

      it('should reject invalid email addresses', () => {
        const invalidEmails = [
          'invalid.email',
          '@domain.com',
          'user@',
          'user@domain',
          'user <EMAIL>',
          'user@domain .com',
          '',
        ];

        invalidEmails.forEach(email => {
          expect(securityService.validateInput(email, 'email')).toBe(false);
        });
      });
    });

    describe('phone validation', () => {
      it('should validate correct phone numbers', () => {
        const validPhones = [
          '1234567890',
          '+1234567890',
          '+8801234567890',
          '01234567890',
        ];

        validPhones.forEach(phone => {
          expect(securityService.validateInput(phone, 'phone')).toBe(true);
        });
      });

      it('should reject invalid phone numbers', () => {
        const invalidPhones = [
          '************',
          'phone number',
          '++1234567890',
          '+12345678901234567890', // Too long
          '',
          '0',
        ];

        invalidPhones.forEach(phone => {
          expect(securityService.validateInput(phone, 'phone')).toBe(false);
        });
      });
    });

    describe('text validation', () => {
      it('should validate safe text input', () => {
        const validTexts = [
          'Hello World',
          'Product-Name_123',
          'This is safe text.',
          'Question? Answer!',
          'Text with, commas',
        ];

        validTexts.forEach(text => {
          expect(securityService.validateInput(text, 'text')).toBe(true);
        });
      });

      it('should reject potentially dangerous text', () => {
        const dangerousTexts = [
          '<script>alert("xss")</script>',
          'SELECT * FROM users',
          'DROP TABLE orders;',
          'text with <tags>',
          'text with & symbols',
        ];

        dangerousTexts.forEach(text => {
          expect(securityService.validateInput(text, 'text')).toBe(false);
        });
      });
    });

    describe('number validation', () => {
      it('should validate correct numbers', () => {
        const validNumbers = [
          '123',
          '123.45',
          '0',
          '0.01',
          '999999.99',
        ];

        validNumbers.forEach(number => {
          expect(securityService.validateInput(number, 'number')).toBe(true);
        });
      });

      it('should reject invalid numbers', () => {
        const invalidNumbers = [
          'abc',
          '123abc',
          '12.34.56',
          '',
          '123,456',
          '+123',
          '-123',
        ];

        invalidNumbers.forEach(number => {
          expect(securityService.validateInput(number, 'number')).toBe(false);
        });
      });
    });
  });

  describe('input sanitization', () => {
    it('should sanitize SQL injection attempts', () => {
      const maliciousInputs = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'--",
        "' UNION SELECT * FROM users --",
      ];

      maliciousInputs.forEach(input => {
        const sanitized = securityService.sanitizeInput(input);
        expect(sanitized).not.toContain("'");
        expect(sanitized).not.toContain(';');
        expect(sanitized).not.toContain('--');
        expect(sanitized).not.toContain('DROP');
        expect(sanitized).not.toContain('UNION');
      });
    });

    it('should sanitize XSS attempts', () => {
      const xssInputs = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")',
        '<svg onload="alert(1)">',
      ];

      xssInputs.forEach(input => {
        const sanitized = securityService.sanitizeInput(input);
        expect(sanitized).not.toContain('<script>');
        expect(sanitized).not.toContain('<img');
        expect(sanitized).not.toContain('javascript:');
        expect(sanitized).not.toContain('onload');
        expect(sanitized).not.toContain('onerror');
      });
    });

    it('should preserve safe content during sanitization', () => {
      const safeInputs = [
        'Normal text content',
        'Product Name 123',
        'Email: <EMAIL>',
        'Price: $123.45',
      ];

      safeInputs.forEach(input => {
        const sanitized = securityService.sanitizeInput(input);
        expect(sanitized.toLowerCase()).toContain(input.toLowerCase().replace(/[^a-z0-9\s]/g, ''));
      });
    });
  });

  describe('audit logging', () => {
    it('should log audit events with required fields', () => {
      const event = 'USER_LOGIN';
      const metadata = { userId: 'user123', timestamp: new Date().toISOString() };

      securityService.auditLog(event, metadata);

      // Since we're not testing LoggingService directly, just ensure no errors
      expect(true).toBe(true);
    });

    it('should handle audit logging with complex metadata', () => {
      const event = 'DATA_ACCESS';
      const complexMetadata = {
        userId: 'admin123',
        action: 'READ',
        resource: 'customer_data',
        filters: { status: 'active', role: 'premium' },
        resultCount: 25,
        duration: 150,
      };

      expect(() => {
        securityService.auditLog(event, complexMetadata);
      }).not.toThrow();
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle crypto service failures gracefully', async () => {
      mockCrypto.getRandomBytesAsync.mockRejectedValue(new Error('Crypto service unavailable'));
      
      // Create a new instance to test initialization failure handling
      const newService = await getSecurityService();
      
      // Should still be able to encrypt/decrypt with fallback key
      const encrypted = await newService.encrypt('test data');
      const decrypted = await newService.decrypt(encrypted);
      
      expect(decrypted).toBe('test data');
    });

    it('should validate encryption key availability', async () => {
      // Test edge case where encryption key might not be available
      const testService = await getSecurityService();
      
      const encrypted = await testService.encrypt('test');
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
    });

    it('should handle concurrent encryption operations', async () => {
      const data = 'concurrent test data';
      
      // Perform multiple concurrent encryptions
      const promises = Array.from({ length: 5 }, () => 
        securityService.encrypt(data)
      );
      
      const results = await Promise.all(promises);
      
      // All should succeed and be different (due to different IVs)
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
      });
      
      // Decrypt all results
      const decrypted = await Promise.all(
        results.map(result => securityService.decrypt(result))
      );
      
      decrypted.forEach(result => {
        expect(result).toBe(data);
      });
    });
  });
});