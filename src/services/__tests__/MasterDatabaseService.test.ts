import AsyncStorage from '@react-native-async-storage/async-storage';

import LoggingService from '../LoggingService';
import { MasterDatabaseService } from '../MasterDatabaseService';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
}));

// Mock LoggingService
jest.mock('../LoggingService', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
}));

// Mock expo-sqlite
jest.mock('expo-sqlite', () => ({
  openDatabaseAsync: jest.fn(() => Promise.resolve(null)), // Force fallback to AsyncStorage
}));

describe('MasterDatabaseService', () => {
  let dbService: MasterDatabaseService;
  
  beforeEach(async () => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
    
    dbService = await MasterDatabaseService.getInstance();
  });

  describe('AsyncStorage Fallback', () => {
    describe('SQL Parsing', () => {
      it('should extract table names correctly', () => {
        const service = dbService as any; // Access private methods
        
        expect(service.extractTableFromSQL('SELECT * FROM users WHERE id = ?')).toBe('users');
        expect(service.extractTableFromSQL('INSERT INTO products (name, price) VALUES (?, ?)')).toBe('products');
        expect(service.extractTableFromSQL('UPDATE customers SET name = ? WHERE id = ?')).toBe('customers');
        expect(service.extractTableFromSQL('DELETE FROM orders WHERE id = ?')).toBe('orders');
        expect(service.extractTableFromSQL('CREATE TABLE IF NOT EXISTS staff (id TEXT)')).toBe('staff');
      });

      it('should extract operations correctly', () => {
        const service = dbService as any;
        
        expect(service.extractOperationFromSQL('SELECT * FROM users')).toBe('SELECT');
        expect(service.extractOperationFromSQL('INSERT INTO users VALUES (?)')).toBe('INSERT');
        expect(service.extractOperationFromSQL('UPDATE users SET name = ?')).toBe('UPDATE');
        expect(service.extractOperationFromSQL('DELETE FROM users')).toBe('DELETE');
        expect(service.extractOperationFromSQL('CREATE TABLE users')).toBe('CREATE');
      });
    });

    describe('Record Operations', () => {
      it('should build records from INSERT SQL correctly', () => {
        const service = dbService as any;
        const sql = 'INSERT INTO users (name, email) VALUES (?, ?)';
        const params = ['John Doe', '<EMAIL>'];
        
        const record = service.buildRecordFromInsertSQL(sql, params);
        
        expect(record).toHaveProperty('id');
        expect(record).toHaveProperty('created_at');
        expect(record).toHaveProperty('updated_at');
        expect(record.name).toBe('John Doe');
        expect(record.email).toBe('<EMAIL>');
      });

      it('should handle INSERT operations in AsyncStorage', async () => {
        const testData = [{ id: '1', name: 'Existing User' }];
        (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(testData));
        
        const sql = 'INSERT INTO users (name, email) VALUES (?, ?)';
        const params = ['New User', '<EMAIL>'];
        
        await (dbService as any).executeAsyncStorageNonQuery(sql, params);
        
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(
          'table_users',
          expect.stringContaining('New User')
        );
      });

      it('should handle UPDATE operations in AsyncStorage', async () => {
        const testData = [
          { id: '1', name: 'Old Name', email: '<EMAIL>' },
          { id: '2', name: 'Other User', email: '<EMAIL>' }
        ];
        (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(testData));
        
        const sql = 'UPDATE users SET name = ? WHERE id = ?';
        const params = ['New Name', '1'];
        
        await (dbService as any).executeAsyncStorageNonQuery(sql, params);
        
        const savedData = (AsyncStorage.setItem as jest.Mock).mock.calls[0][1];
        const updatedRecords = JSON.parse(savedData);
        
        expect(updatedRecords[0].name).toBe('New Name');
        expect(updatedRecords[0].id).toBe('1');
        expect(updatedRecords[1].name).toBe('Other User'); // Unchanged
      });

      it('should handle DELETE operations in AsyncStorage', async () => {
        const testData = [
          { id: '1', name: 'User 1' },
          { id: '2', name: 'User 2' },
          { id: '3', name: 'User 3' }
        ];
        (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(testData));
        
        const sql = 'DELETE FROM users WHERE id = ?';
        const params = ['2'];
        
        await (dbService as any).executeAsyncStorageNonQuery(sql, params);
        
        const savedData = (AsyncStorage.setItem as jest.Mock).mock.calls[0][1];
        const remainingRecords = JSON.parse(savedData);
        
        expect(remainingRecords).toHaveLength(2);
        expect(remainingRecords.find((r: any) => r.id === '2')).toBeUndefined();
      });
    });

    describe('Query Filtering', () => {
      const testData = [
        { id: '1', name: 'Alice', role: 'admin', age: 30 },
        { id: '2', name: 'Bob', role: 'user', age: 25 },
        { id: '3', name: 'Charlie', role: 'admin', age: 35 }
      ];

      it('should filter by ID correctly', () => {
        const service = dbService as any;
        const sql = 'SELECT * FROM users WHERE id = ?';
        const params = ['2'];
        
        const filtered = service.applyAdvancedFiltering(testData, sql, params);
        
        expect(filtered).toHaveLength(1);
        expect(filtered[0].name).toBe('Bob');
      });

      it('should filter by column value correctly', () => {
        const service = dbService as any;
        const sql = 'SELECT * FROM users WHERE role = ?';
        const params = ['admin'];
        
        const filtered = service.applyAdvancedFiltering(testData, sql, params);
        
        expect(filtered).toHaveLength(2);
        expect(filtered.map((u: any) => u.name)).toEqual(['Alice', 'Charlie']);
      });

      it('should handle LIKE queries correctly', () => {
        const service = dbService as any;
        const sql = 'SELECT * FROM users WHERE name LIKE ?';
        const params = ['%a%']; // Names containing 'a'
        
        const filtered = service.applyAdvancedFiltering(testData, sql, params);
        
        expect(filtered).toHaveLength(2); // Alice, Charlie
        expect(filtered.map((u: any) => u.name)).toEqual(['Alice', 'Charlie']);
      });
    });

    describe('Query Ordering and Limiting', () => {
      const testData = [
        { id: '1', name: 'Charlie', age: 35 },
        { id: '2', name: 'Alice', age: 30 },
        { id: '3', name: 'Bob', age: 25 }
      ];

      it('should order results correctly', () => {
        const service = dbService as any;
        
        // Order by name ASC
        const orderedAsc = service.applySQLOrdering([...testData], 'SELECT * FROM users ORDER BY name ASC');
        expect(orderedAsc.map((u: any) => u.name)).toEqual(['Alice', 'Bob', 'Charlie']);
        
        // Order by age DESC
        const orderedDesc = service.applySQLOrdering([...testData], 'SELECT * FROM users ORDER BY age DESC');
        expect(orderedDesc.map((u: any) => u.age)).toEqual([35, 30, 25]);
      });

      it('should apply LIMIT correctly', () => {
        const service = dbService as any;
        
        const limited = service.applySQLLimit(testData, 'SELECT * FROM users LIMIT 2');
        expect(limited).toHaveLength(2);
        
        const limitedWithOffset = service.applySQLLimit(testData, 'SELECT * FROM users LIMIT 1 OFFSET 1');
        expect(limitedWithOffset).toHaveLength(1);
        expect(limitedWithOffset[0].name).toBe('Alice');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle AsyncStorage errors gracefully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      
      const result = await (dbService as any).executeAsyncStorageQuery('SELECT * FROM users', []);
      
      expect(result).toEqual([]);
      expect(LoggingService.error).toHaveBeenCalledWith(
        'AsyncStorage query failed for table users',
        'MASTER_DB',
        expect.any(Error)
      );
    });

    it('should handle malformed SQL gracefully', () => {
      const service = dbService as any;
      
      expect(() => service.extractTableFromSQL('INVALID SQL')).not.toThrow();
      expect(service.extractTableFromSQL('INVALID SQL')).toBe('unknown');
    });

    it('should handle missing WHERE clause in DELETE safely', () => {
      const service = dbService as any;
      const testData = [{ id: '1', name: 'User 1' }];
      
      const result = service.applyDeleteToRecords(testData, 'DELETE FROM users', []);
      
      expect(result).toEqual(testData); // No deletion should occur
      expect(LoggingService.warn).toHaveBeenCalledWith(
        'DELETE without WHERE clause - skipping for safety',
        'MASTER_DB'
      );
    });
  });

  describe('High-level CRUD Operations', () => {
    it('should create records correctly', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('[]');
      
      const newUser = await dbService.create('users', {
        name: 'Test User',
        email: '<EMAIL>'
      });
      
      expect(newUser).toHaveProperty('id');
      expect(newUser).toHaveProperty('created_at');
      expect(newUser).toHaveProperty('updated_at');
      expect(newUser.name).toBe('Test User');
      expect(newUser.email).toBe('<EMAIL>');
    });

    it('should find records by ID correctly', async () => {
      const testData = [
        { id: 'user1', name: 'User 1' },
        { id: 'user2', name: 'User 2' }
      ];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(testData));
      
      const user = await dbService.findById('users', 'user1');
      
      expect(user).toBeTruthy();
      expect((user as any)?.name).toBe('User 1');
    });

    it('should update records correctly', async () => {
      const testData = [
        { id: 'user1', name: 'Old Name', email: '<EMAIL>' }
      ];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(testData));
      
      const updated = await dbService.update('users', 'user1', {
        name: 'New Name',
        email: '<EMAIL>'
      });
      
      expect(updated).toBeTruthy();
      expect((updated as any)?.name).toBe('New Name');
      expect((updated as any)?.email).toBe('<EMAIL>');
    });

    it('should handle errors gracefully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      
      const result = await dbService.findAll('users', {});
      
      expect(result).toEqual([]);
    });
  });
});