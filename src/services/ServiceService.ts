import LoggingService from './LoggingService';
import { StorageService } from './StorageService';

export interface Service {
  id: string;
  name: string;
  price: number;
  measurementFields: string[];
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Service for managing service types using AsyncStorage via StorageService
 * This avoids adding a new DB table and keeps the UI responsive.
 */
export class ServiceTypeService {
  private static readonly STORAGE_KEY = 'serviceTypes';

  /** Default seed used only when storage is empty */
  private static readonly DEFAULT_TEMPLATES: Service[] = [
    {
      id: 'gt_1',
      name: 'Shirt',
      price: 1200,
      measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length'],
    },
    {
      id: 'gt_2',
      name: 'Pants',
      price: 1000,
      measurementFields: ['waist', 'hip', 'length', 'inseam'],
    },
    {
      id: 'gt_3',
      name: 'Suit',
      price: 3500,
      measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length', 'inseam'],
    },
    {
      id: 'gt_4',
      name: 'Dress',
      price: 1800,
      measurementFields: ['bust', 'waist', 'hip', 'length'],
    },
    {
      id: 'gt_5',
      name: 'Kurta',
      price: 1500,
      measurementFields: ['chest', 'waist', 'shoulder', 'sleeve', 'length'],
    },
  ];

  /** Generate a reasonably unique id */
  private static generateId(prefix: string = 'gt'): string {
    const random = Math.random().toString(36).slice(2, 8);
    return `${prefix}_${Date.now()}_${random}`;
  }

  /** Ensure storage has data; seed defaults if empty */
  static async seedIfEmpty(): Promise<void> {
    const existing = await StorageService.get<Service[]>(this.STORAGE_KEY);
    if (!existing || existing.length === 0) {
      const now = new Date().toISOString();
      const seeded = this.DEFAULT_TEMPLATES.map(t => ({
        ...t,
        createdAt: now,
        updatedAt: now,
      }));
      await StorageService.set(this.STORAGE_KEY, seeded);
      LoggingService.info('Seeded default service types', 'SERVICE_TYPES');
    }
  }

  /** Get all service types */
  static async list(): Promise<Service[]> {
    const templates = (await StorageService.get<Service[]>(this.STORAGE_KEY)) || [];
    return Array.isArray(templates) ? templates : [];
  }

  /** Get by id */
  static async getById(id: string): Promise<Service | null> {
    const templates = await this.list();
    return templates.find(t => t.id === id) || null;
  }

  /** Create a new service type */
  static async create(
    input: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Service> {
    const templates = await this.list();
    const now = new Date().toISOString();
    const newItem: Service = {
      ...input,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
    };

    const next = [...templates, newItem];
    await StorageService.set(this.STORAGE_KEY, next);
    LoggingService.info(`Created service type: ${newItem.name}`, 'SERVICE_TYPES');
    return newItem;
  }

  /** Update a service type */
  static async update(
    id: string,
    updates: Partial<Omit<Service, 'id' | 'createdAt'>>
  ): Promise<Service> {
    const templates = await this.list();
    const idx = templates.findIndex(t => t.id === id);
    if (idx === -1) {
      throw new Error('Service type not found');
    }

    const now = new Date().toISOString();
    const updated: Service = {
      ...templates[idx],
      ...updates,
      updatedAt: now,
    };

    const next = [...templates];
    next[idx] = updated;
    await StorageService.set(this.STORAGE_KEY, next);
    LoggingService.info(`Updated service type: ${updated.name}`, 'SERVICE_TYPES');
    return updated;
  }

  /** Delete a service type */
  static async remove(id: string): Promise<void> {
    const templates = await this.list();
    const filtered = templates.filter(t => t.id !== id);
    await StorageService.set(this.STORAGE_KEY, filtered);
    LoggingService.info(`Deleted service type: ${id}`, 'SERVICE_TYPES');
  }

  /** Replace all (use with caution) */
  static async replaceAll(items: Service[]): Promise<void> {
    await StorageService.set(this.STORAGE_KEY, items);
  }
}

export default ServiceTypeService;
