import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';

import LoggingService from './LoggingService';

/**
 * Saves an image to the app's document directory for local persistence
 * @param sourceUri The local URI of the image to save
 * @param filename Optional filename, will generate one if not provided
 * @returns The permanent local file URI
 */
const saveImageLocally = async (sourceUri: string, filename?: string): Promise<string> => {
  try {
    // Ensure the images directory exists
    const imagesDir = `${FileSystem.documentDirectory}images/`;
    const dirInfo = await FileSystem.getInfoAsync(imagesDir);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(imagesDir, { intermediates: true });
    }

    // Generate filename if not provided
    const finalFilename = filename || `profile_${Date.now()}.jpg`;
    const destinationUri = `${imagesDir}${finalFilename}`;

    // Copy the file to the documents directory
    await FileSystem.copyAsync({ from: sourceUri, to: destinationUri });
    
    LoggingService.info('Image saved locally', 'ImageProcessingService', { 
      sourceUri, 
      destinationUri 
    });
    
    return destinationUri;
  } catch (error) {
    LoggingService.error('Failed to save image locally', 'ImageProcessingService', error as Error);
    throw error;
  }
};

/**
 * PLACEHOLDER: Upload file to server
 * TODO: Replace with actual server upload implementation
 * @param optimizedUri The local URI of the optimized image file.
 * @returns The permanent, remote URL of the uploaded image.
 */
const uploadFileToServer = async (optimizedUri: string): Promise<string> => {
  LoggingService.info('Uploading file...', 'ImageProcessingService', { uri: optimizedUri });

  // For now, save locally instead of uploading to server
  // In a real app, you would use fetch, axios, etc. to upload the file
  // to your server and return the permanent URL.
  const localUri = await saveImageLocally(optimizedUri);
  
  LoggingService.info('Image stored locally (server upload not implemented)', 'ImageProcessingService', { 
    localUri 
  });
  
  return localUri;
};

/**
 * Optimizes and saves an image locally for immediate use
 * @param localUri The local URI of the image to process.
 * @returns The permanent local URI of the processed image.
 */
const optimizeAndSaveLocally = async (localUri: string): Promise<string> => {
  try {
    // Skip optimization if already optimized or if it's already in our documents directory
    if (FileSystem.documentDirectory && localUri.includes(FileSystem.documentDirectory)) {
      return localUri;
    }

    const optimized = await ImageManipulator.manipulateAsync(
      localUri,
      [{ resize: { width: 1200, height: 1200 } }],
      { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
    );
    
    const savedUri = await saveImageLocally(optimized.uri);
    return savedUri;
  } catch (error) {
    LoggingService.error('Image optimization and local save failed', 'ImageProcessingService', error as Error);
    throw error;
  }
};

/**
 * Optimizes and uploads a single image file.
 * @param localUri The local URI of the image to process.
 * @returns The permanent, remote URL of the uploaded image.
 */
const optimizeAndUpload = async (localUri: string): Promise<string> => {
  try {
    const optimized = await ImageManipulator.manipulateAsync(
      localUri,
      [{ resize: { width: 1200, height: 1200 } }],
      { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
    );
    const remoteUrl = await uploadFileToServer(optimized.uri);
    return remoteUrl;
  } catch (error) {
    LoggingService.error('Image optimization and upload failed', 'ImageProcessingService', error as Error);
    throw error; // Re-throw to be caught by the calling screen
  }
};

interface ProcessImageAndUpdateParams<T> {
  localUri: string;
  entityId: T;
  updateAction: (id: T, remoteUrl: string) => Promise<void>;
}

/**
 * A generic function to process an image in the background.
 * It optimizes the image, uploads it, and then runs a callback to update the original record.
 * This function is designed to be "fire-and-forget" and not block the UI.
 */
const processImageAndUpdate = async <T>({
  localUri,
  entityId,
  updateAction,
}: ProcessImageAndUpdateParams<T>) => {
  try {
    // 1. Optimize Image
    LoggingService.info('Optimizing image in background...', 'ImageProcessingService', { entityId });
    const optimized = await ImageManipulator.manipulateAsync(
      localUri,
      [{ resize: { width: 1200, height: 1200 } }], // Sensible default for quality
      { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
    );

    // 2. Upload Optimized Image
    const remoteUrl = await uploadFileToServer(optimized.uri);

    // 3. Update the record with the permanent URL
    await updateAction(entityId, remoteUrl);

    LoggingService.info('Background image processing successful', 'ImageProcessingService', {
      entityId,
      remoteUrl,
    });
  } catch (error) {
    LoggingService.error('Background image processing failed', 'ImageProcessingService', error as Error);
    // Here you might want to add logic to retry or notify the user of the background failure.
  }
};

export const ImageProcessingService = {
  processImageAndUpdate,
  optimizeAndUpload,
  optimizeAndSaveLocally,
  saveImageLocally,
};