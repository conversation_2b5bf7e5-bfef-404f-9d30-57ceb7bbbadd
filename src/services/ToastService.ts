import { createRef } from 'react';

import Toast from '@/components/ui/Toast';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default';
export type Position = 'top' | 'bottom';

export interface ToastActions {
  showToast: (message: string, type?: ToastType, duration?: number, position?: Position) => void;
  showSuccess: (message: string, duration?: number, position?: Position) => void;
  showError: (message: string, duration?: number, position?: Position) => void;
  showWarning: (message: string, duration?: number, position?: Position) => void;
  showInfo: (message: string, duration?: number, position?: Position) => void;
  hideAllToasts?: () => void;
}

export const toastRef = createRef<ToastActions>();

const ToastService = {
  show: (message: string, type?: ToastType, duration?: number, position?: Position) => toastRef.current?.showToast(message, type, duration, position),
  success: (message: string, duration?: number, position?: Position) => toastRef.current?.showSuccess(message, duration, position),
  error: (message: string, duration?: number, position?: Position) => toastRef.current?.showError(message, duration, position),
  warning: (message: string, duration?: number, position?: Position) => toastRef.current?.showWarning(message, duration, position),
  info: (message: string, duration?: number, position?: Position) => toastRef.current?.showInfo(message, duration, position),
  hideAll: () => toastRef.current?.hideAllToasts?.(),
};

export default ToastService;