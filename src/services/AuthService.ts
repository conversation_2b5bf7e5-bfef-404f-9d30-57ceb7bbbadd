import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

import LoggingService from './LoggingService';
import SecurityService, { getSecurityService } from './SecurityService';

/**
 * AuthService - Comprehensive authentication and session management
 *
 * Features:
 * - Session management with secure tokens
 * - Automatic session expiration
 * - Secure storage of authentication data
 * - Session validation and refresh
 * - Multi-device session tracking
 * - Comprehensive audit logging
 *
 * @class AuthService
 * @version 1.0.0
 */

export interface UserSession {
  userId: string;
  username: string;
  email?: string;
  role: 'admin' | 'user' | 'manager';
  sessionId: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
  createdAt: number;
  lastActivity: number;
  selectedOutlet?: string;
  deviceInfo: {
    platform: string;
    deviceId: string;
    appVersion: string;
  };
  permissions: string[];
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  userType?: 'user' | 'admin' | 'staff';
  adminCode?: string;
}

export interface SessionValidationResult {
  isValid: boolean;
  session?: UserSession;
  reason?: 'expired' | 'invalid' | 'not_found' | 'revoked';
}

class AuthService {
  private static instance: AuthService;
  private currentSession: UserSession | null = null;
  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private readonly SESSION_KEY = 'user_session';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly REFRESH_THRESHOLD = 2 * 60 * 60 * 1000; // 2 hours before expiry

  private constructor() {
    this.initializeSessionMonitoring();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Initialize session monitoring
   */
  private initializeSessionMonitoring(): void {
    // PRODUCTION FIX: Check session validity every 15 minutes to reduce overhead
    this.sessionCheckInterval = setInterval(
      () => {
        this.validateCurrentSessionSafely();
      },
      15 * 60 * 1000 // Increased from 5 to 15 minutes
    );

    LoggingService.info('Session monitoring initialized', 'AUTH');
  }

  /**
   * Generate a secure session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Generate device information
   */
  private getDeviceInfo(): UserSession['deviceInfo'] {
    return {
      platform: Platform.OS,
      deviceId: `${Platform.OS}_${Date.now()}`, // In real app, use device-specific ID
      appVersion: '1.0.0', // Should come from app config
    };
  }

  /**
   * Create a new user session
   */
  private async createSession(
    userId: string,
    username: string,
    email?: string,
    role: UserSession['role'] = 'user'
  ): Promise<UserSession> {
    const now = Date.now();
    const sessionId = this.generateSessionId();
    const securityService = await getSecurityService();
    const accessToken = await securityService.encrypt(`${userId}_${sessionId}_${now}`);

    const session: UserSession = {
      userId,
      username,
      email,
      role,
      sessionId,
      accessToken,
      expiresAt: now + this.SESSION_DURATION,
      createdAt: now,
      lastActivity: now,
      deviceInfo: this.getDeviceInfo(),
      permissions: this.getDefaultPermissions(role),
    };

    // Store session securely
    await this.storeSession(session);
    this.currentSession = session;

    LoggingService.info(`Session created for user: ${username}`, 'AUTH', {
      userId,
      sessionId,
      role,
      expiresAt: new Date(session.expiresAt).toISOString(),
    });

    securityService.auditLog('SESSION_CREATED', {
      userId,
      username,
      sessionId,
      deviceInfo: session.deviceInfo,
    });

    return session;
  }

  /**
   * Get default permissions based on role
   */
  private getDefaultPermissions(role: UserSession['role']): string[] {
    const permissions = {
      admin: [
        'read:all',
        'write:all',
        'delete:all',
        'manage:users',
        'manage:settings',
        'view:analytics',
        'export:data',
      ],
      manager: [
        'read:all',
        'write:orders',
        'write:products',
        'write:customers',
        'view:analytics',
        'export:data',
      ],
      user: ['read:orders', 'read:products', 'read:customers', 'write:orders'],
    };

    return permissions[role] || permissions.user;
  }

  /**
   * Store session securely
   */
  private async storeSession(session: UserSession): Promise<void> {
    try {
      const securityService = await getSecurityService();
      const encryptedSession = await securityService.encrypt(JSON.stringify(session));
      await securityService.secureStore(this.SESSION_KEY, encryptedSession);

      if (session.refreshToken) {
        await securityService.secureStore(this.REFRESH_TOKEN_KEY, session.refreshToken);
      }

      LoggingService.info('Session stored securely', 'AUTH');
    } catch (error) {
      LoggingService.error('Failed to store session', 'AUTH', error as Error);
      throw new Error('Failed to store session securely');
    }
  }

  /**
   * Retrieve stored session with encryption error recovery
   */
  private async retrieveStoredSession(): Promise<UserSession | null> {
    try {
      const securityService = await getSecurityService();
      const encryptedSession = await securityService.secureRetrieve(this.SESSION_KEY);
      if (!encryptedSession) {
        return null;
      }

      const sessionData = await securityService.decrypt(encryptedSession);
      
      // Handle case where decryption returns null (invalid key)
      if (sessionData === null) {
        LoggingService.warn('Session decryption failed - resetting encryption key', 'AUTH');
        await securityService.resetEncryptionKey();
        return null;
      }
      
      const session: UserSession = JSON.parse(sessionData);

      LoggingService.info('Session retrieved from storage', 'AUTH');
      return session;
    } catch (error) {
      LoggingService.error('Failed to retrieve session', 'AUTH', error as Error);
      
      // If encryption errors persist, try resetting the security system
      if ((error as Error).message.includes('decrypt') || (error as Error).message.includes('encryption')) {
        try {
          const securityService = await getSecurityService();
          LoggingService.warn('Attempting encryption key reset due to persistent errors', 'AUTH');
          await securityService.resetEncryptionKey();
        } catch (resetError) {
          LoggingService.error('Failed to reset encryption key', 'AUTH', resetError as Error);
        }
      }
      
      return null;
    }
  }

  /**
   * PRODUCTION FIX: Safe session retrieval with timeout protection
   */
  private async retrieveStoredSessionSafely(): Promise<UserSession | null> {
    try {
      const retrievalTimeout = new Promise<UserSession | null>((_, reject) => {
        setTimeout(() => reject(new Error('Session retrieval timeout')), 5000); // 5 second timeout
      });

      const retrievalPromise = this.retrieveStoredSession();
      
      return await Promise.race([retrievalPromise, retrievalTimeout]);
    } catch (error) {
      LoggingService.warn('Session retrieval timeout or error - returning null', 'AUTH', error as Error);
      return null;
    }
  }

  /**
   * Validate session
   */
  private validateSession(session: UserSession): SessionValidationResult {
    const now = Date.now();

    // Check if session is expired
    if (session.expiresAt <= now) {
      return {
        isValid: false,
        reason: 'expired',
      };
    }

    // Check if session is too old (security measure)
    const maxSessionAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    if (now - session.createdAt > maxSessionAge) {
      return {
        isValid: false,
        reason: 'expired',
      };
    }

    // Session is valid
    return {
      isValid: true,
      session,
    };
  }

  /**
   * Update session activity
   */
  private async updateSessionActivity(session: UserSession): Promise<void> {
    session.lastActivity = Date.now();
    await this.storeSession(session);
    this.currentSession = session;
  }

  /**
   * PRODUCTION FIX: Safe session activity update without blocking
   */
  private updateSessionActivitySafely(session: UserSession): void {
    try {
      // Update activity timestamp immediately
      session.lastActivity = Date.now();
      this.currentSession = session;
      
      // Store session asynchronously without blocking
      this.storeSessionSafely(session);
    } catch (error) {
      LoggingService.warn('Session activity update failed', 'AUTH', error as Error);
    }
  }

  /**
   * PRODUCTION FIX: Safe session storage without blocking
   */
  private async storeSessionSafely(session: UserSession): Promise<void> {
    try {
      const storageTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Session storage timeout')), 3000); // 3 second timeout
      });

      const storagePromise = this.storeSession(session);
      
      await Promise.race([storagePromise, storageTimeout]);
    } catch (error) {
      LoggingService.warn('Session storage timeout or error - continuing without blocking', 'AUTH', error as Error);
    }
  }

  /**
   * PRODUCTION FIX: Safe session data clearing without blocking
   */
  private clearSessionDataSafely(): void {
    try {
      // Clear session data asynchronously without blocking
      this.clearSessionData().catch((error) => {
        LoggingService.warn('Session data clearing failed - non-blocking', 'AUTH', error as Error);
      });
    } catch (error) {
      LoggingService.warn('Session data clearing initialization failed', 'AUTH', error as Error);
    }
  }

  /**
   * Login user with credentials
   */
  public async login(credentials: LoginCredentials): Promise<UserSession> {
    try {
      LoggingService.info(`Login attempt for email: ${credentials.email}`, 'AUTH');

      // Validate credentials with enhanced multi-user support
      const validationResult = await this.validateCredentials(credentials);
      const securityService = await getSecurityService();

      if (!validationResult.isValid) {
        securityService.auditLog('LOGIN_FAILED', {
          email: credentials.email,
          userType: credentials.userType || 'user',
          reason: 'invalid_credentials',
        });
        throw new Error('Invalid credentials');
      }

      // Create session for authenticated user
      const session = await this.createSession(
        validationResult.userId!,
        validationResult.username!,
        credentials.email,
        validationResult.userRole!
      );

      LoggingService.info(`Login successful for email: ${credentials.email}`, 'AUTH');
      securityService.auditLog('LOGIN_SUCCESS', {
        userId: session.userId,
        username: session.username,
        email: session.email,
        sessionId: session.sessionId,
      });

      return session;
    } catch (error) {
      LoggingService.error('Login failed', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Validate credentials with email-based authentication
   */
  private async validateCredentials(credentials: LoginCredentials): Promise<{
    isValid: boolean;
    userRole?: UserSession['role'];
    userId?: string;
    username?: string;
  }> {
    // Mock user database - in real app, this would be a database query
    const users: Record<
      string,
      { password: string; role: UserSession['role']; username: string; requiresAdminCode?: boolean }
    > = {
      // Business Owner accounts
      '<EMAIL>': {
        password: 'owner123',
        role: 'admin' as const,
        username: 'owner',
      },
      '<EMAIL>': {
        password: 'business123',
        role: 'admin' as const,
        username: 'business',
      },
      
      // Admin/Manager accounts
      '<EMAIL>': {
        password: 'admin123',
        role: 'manager' as const,
        username: 'admin',
        requiresAdminCode: true,
      },
      '<EMAIL>': {
        password: 'manager123',
        role: 'manager' as const,
        username: 'manager',
        requiresAdminCode: true,
      },
      '<EMAIL>': {
        password: 'admin123',
        role: 'manager' as const,
        username: 'admin.john',
        requiresAdminCode: true,
      },
      
      // Staff accounts
      '<EMAIL>': {
        password: 'staff123',
        role: 'user' as const,
        username: 'staff',
      },
      '<EMAIL>': {
        password: 'staff123',
        role: 'user' as const,
        username: 'staff001',
      },
      '<EMAIL>': {
        password: 'employee123',
        role: 'user' as const,
        username: 'employee',
      },
      '<EMAIL>': {
        password: 'staff123',
        role: 'user' as const,
        username: 'staff.mary',
      },
      
      // Demo accounts
      '<EMAIL>': {
        password: 'demo123',
        role: 'user' as const,
        username: 'demo',
      },
      '<EMAIL>': {
        password: 'test123',
        role: 'user' as const,
        username: 'test',
      },
    };

    const user = users[credentials.email.toLowerCase()];

    if (!user || user.password !== credentials.password) {
      return { isValid: false };
    }

    // Check admin code if required (for admin/manager roles)
    if (user.requiresAdminCode && credentials.userType === 'admin') {
      const validAdminCodes = ['ADMIN2024', 'MANAGER2024'];
      if (!credentials.adminCode || !validAdminCodes.includes(credentials.adminCode)) {
        return { isValid: false };
      }
    }

    // Auto-detect user type based on email if not provided
    let detectedUserType = credentials.userType;
    if (!detectedUserType) {
      const emailLower = credentials.email.toLowerCase();
      if (emailLower.includes('admin') || emailLower.includes('manager')) {
        detectedUserType = 'admin';
      } else if (emailLower.includes('staff') || emailLower.includes('employee')) {
        detectedUserType = 'staff';
      } else {
        detectedUserType = 'user';
      }
    }

    return {
      isValid: true,
      userRole: user.role,
      userId: `${detectedUserType}_${user.username}_${Date.now()}`,
      username: user.username,
    };
  }

  /**
   * Logout user and clear session
   */
  public async logout(): Promise<void> {
    try {
      const currentSession = this.currentSession || (await this.retrieveStoredSession());
      const securityService = await getSecurityService();

      if (currentSession) {
        LoggingService.info(`Logout initiated for user: ${currentSession.username}`, 'AUTH');

        securityService.auditLog('LOGOUT_INITIATED', {
          userId: currentSession.userId,
          username: currentSession.username,
          sessionId: currentSession.sessionId,
          sessionDuration: Date.now() - currentSession.createdAt,
        });
      }

      await this.clearSessionData();

      // Clear current session immediately
      this.currentSession = null;

      LoggingService.info('Logout completed successfully', 'AUTH');

      if (currentSession) {
        securityService.auditLog('LOGOUT_SUCCESS', {
          userId: currentSession.userId,
          username: currentSession.username,
          sessionId: currentSession.sessionId,
        });
      }
    } catch (error) {
      LoggingService.error('Logout failed', 'AUTH', error as Error);
      // Don't throw error to prevent app freeze - logout should always succeed
      // The error is logged but we proceed with clearing the session anyway
      this.currentSession = null;
    }
  }

  /**
   * Clear all session data
   */
  private async clearSessionData(): Promise<void> {
    try {
      const securityService = await getSecurityService();
      
      // Remove session data from secure storage
      await AsyncStorage.multiRemove([
        this.SESSION_KEY,
        this.REFRESH_TOKEN_KEY,
        'auth_token',
        'user_preferences',
        'temp_session_data',
      ]).catch((error: any) => {
        LoggingService.warn('Non-critical error clearing session keys', 'AUTH', error as Error);
      });

      // Clear any cached authentication data
      await securityService.secureStore(this.SESSION_KEY, '').catch((error: any) => {
        LoggingService.warn('Non-critical error clearing session key', 'AUTH', error as Error);
      });
      
      await securityService.secureStore(this.REFRESH_TOKEN_KEY, '').catch((error: any) => {
        LoggingService.warn('Non-critical error clearing refresh token', 'AUTH', error as Error);
      });

      LoggingService.info('Session data cleared successfully', 'AUTH');
    } catch (error) {
      LoggingService.error('Failed to clear session data', 'AUTH', error as Error);
      // Don't throw error to prevent app freeze
    }
  }

  /**
   * Get current session with timeout protection
   */
  public async getCurrentSession(): Promise<UserSession | null> {
    try {
      // PRODUCTION FIX: Add timeout protection for session retrieval
      const sessionTimeout = new Promise<UserSession | null>((_, reject) => {
        setTimeout(() => reject(new Error('Get session timeout')), 8000); // 8 second timeout
      });

      const sessionPromise = this.getCurrentSessionInternal();
      
      return await Promise.race([sessionPromise, sessionTimeout]);
    } catch (error) {
      LoggingService.error('Failed to get current session', 'AUTH', error as Error);
      // PRODUCTION FIX: Return null instead of throwing to prevent app crashes
      return null;
    }
  }

  /**
   * Internal session retrieval logic
   */
  private async getCurrentSessionInternal(): Promise<UserSession | null> {
    try {
      // Return cached session if available and valid
      if (this.currentSession) {
        const validation = this.validateSession(this.currentSession);
        if (validation.isValid) {
          // PRODUCTION FIX: Don't block on activity update, do it in background
          this.updateSessionActivitySafely(this.currentSession);
          return this.currentSession;
        } else {
          // Clear invalid cached session
          this.currentSession = null;
        }
      }

      // Try to retrieve session from storage with timeout
      const storedSession = await this.retrieveStoredSessionSafely();
      if (storedSession) {
        const validation = this.validateSession(storedSession);
        if (validation.isValid) {
          this.currentSession = storedSession;
          this.updateSessionActivitySafely(storedSession);
          return storedSession;
        } else {
          // Session is invalid, clear it asynchronously
          this.clearSessionDataSafely();
          LoggingService.warn(`Session invalid: ${validation.reason}`, 'AUTH');
        }
      }

      return null;
    } catch (error) {
      LoggingService.error('Internal session retrieval failed', 'AUTH', error as Error);
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    const session = await this.getCurrentSession();
    return session !== null;
  }

  /**
   * Validate current session with timeout protection
   */
  private async validateCurrentSession(): Promise<void> {
    try {
      const session = await this.getCurrentSession();
      if (!session) {
        LoggingService.info('No valid session found during validation', 'AUTH');
        return;
      }

      // Check if session needs refresh
      const now = Date.now();
      const timeUntilExpiry = session.expiresAt - now;

      if (timeUntilExpiry <= this.REFRESH_THRESHOLD) {
        LoggingService.info('Session approaching expiry, attempting refresh', 'AUTH');
        await this.refreshSession(session);
      }
    } catch (error) {
      LoggingService.error('Session validation failed', 'AUTH', error as Error);
    }
  }

  /**
   * PRODUCTION FIX: Safe session validation with timeout and error recovery
   */
  private async validateCurrentSessionSafely(): Promise<void> {
    try {
      // Add timeout protection to prevent hanging
      const validationTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Session validation timeout')), 10000); // 10 second timeout
      });

      const validationPromise = this.validateCurrentSession();
      
      await Promise.race([validationPromise, validationTimeout]);
    } catch (error) {
      LoggingService.warn('Session validation timeout or error - recovering gracefully', 'AUTH', error as Error);
      // Don't throw error to prevent app freeze
    }
  }

  /**
   * Refresh session with timeout protection
   */
  private async refreshSession(session: UserSession): Promise<UserSession> {
    try {
      // PRODUCTION FIX: Add timeout protection for session refresh
      const refreshTimeout = new Promise<UserSession>((_, reject) => {
        setTimeout(() => reject(new Error('Session refresh timeout')), 10000); // 10 second timeout
      });

      const refreshPromise = this.refreshSessionInternal(session);
      
      return await Promise.race([refreshPromise, refreshTimeout]);
    } catch (error) {
      LoggingService.error('Session refresh failed', 'AUTH', error as Error);
      // PRODUCTION FIX: Don't throw error to prevent app freeze
      // Instead, return the original session and try again later
      LoggingService.warn('Returning original session due to refresh failure', 'AUTH');
      return session;
    }
  }

  /**
   * Internal session refresh logic
   */
  private async refreshSessionInternal(session: UserSession): Promise<UserSession> {
    try {
      const securityService = await getSecurityService();
      
      // Extend session expiry
      const now = Date.now();
      session.expiresAt = now + this.SESSION_DURATION;
      session.lastActivity = now;

      // Generate new access token
      session.accessToken = await securityService.encrypt(
        `${session.userId}_${session.sessionId}_${now}`
      );

      await this.storeSession(session);
      this.currentSession = session;

      LoggingService.info(`Session refreshed for user: ${session.username}`, 'AUTH');
      securityService.auditLog('SESSION_REFRESHED', {
        userId: session.userId,
        sessionId: session.sessionId,
        newExpiresAt: new Date(session.expiresAt).toISOString(),
      });

      return session;
    } catch (error) {
      LoggingService.error('Internal session refresh failed', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Force logout (for security purposes)
   */
  public async forceLogout(reason: string = 'security'): Promise<void> {
    try {
      const currentSession = this.currentSession || (await this.retrieveStoredSession());
      const securityService = await getSecurityService();

      if (currentSession) {
        securityService.auditLog('FORCE_LOGOUT', {
          userId: currentSession.userId,
          username: currentSession.username,
          sessionId: currentSession.sessionId,
          reason,
        });
      }

      await this.logout();
      LoggingService.warn(`Force logout executed: ${reason}`, 'AUTH');
    } catch (error) {
      LoggingService.error('Force logout failed', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Get session info for debugging
   */
  public async getSessionInfo(): Promise<{
    isAuthenticated: boolean;
    session?: Partial<UserSession>;
    timeUntilExpiry?: number;
  }> {
    try {
      const session = await this.getCurrentSession();

      if (!session) {
        return { isAuthenticated: false };
      }

      const now = Date.now();
      return {
        isAuthenticated: true,
        session: {
          userId: session.userId,
          username: session.username,
          role: session.role,
          sessionId: session.sessionId,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          expiresAt: session.expiresAt,
          deviceInfo: session.deviceInfo,
        },
        timeUntilExpiry: session.expiresAt - now,
      };
    } catch (error) {
      LoggingService.error('Failed to get session info', 'AUTH', error as Error);
      return { isAuthenticated: false };
    }
  }

  /**
   * Cleanup on app termination
   */
  public cleanup(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
    LoggingService.info('AuthService cleanup completed', 'AUTH');
  }
}

export default AuthService.getInstance();