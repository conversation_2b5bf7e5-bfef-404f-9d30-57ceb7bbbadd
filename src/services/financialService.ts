import { Order } from '../types/business';
import {
  Expense,
  CashReconciliation,
  ProfitLossData,
  PaymentAnalytics,
  TaxSummary,
  ExpenseFilters,
  ReconciliationFilters,
  ExpectedCashData,
} from '../types/financial';
import { AppError } from '../utils/errorHandler';

import { StorageService } from './StorageService';

export class FinancialService {
  private static STORAGE_KEYS = {
    ORDERS: 'orders',
    EXPENSES: 'financial_expenses',
    RECONCILIATIONS: 'cash_reconciliations',
  };

  private static async getOrders(
    startDate?: string,
    endDate?: string,
    outletId?: string
  ): Promise<Order[]> {
    const orders: Order[] = (await StorageService.get(this.STORAGE_KEYS.ORDERS)) || [];
    let filtered = orders;
    if (startDate) {
      filtered = filtered.filter(o => new Date(o.createdAt) >= new Date(startDate));
    }
    if (endDate) {
      filtered = filtered.filter(o => new Date(o.createdAt) <= new Date(endDate));
    }
    if (outletId) {
      filtered = filtered.filter(o => o.outletId === outletId);
    }
    return filtered;
  }

  // --- Functions like addExpense, getExpenses, etc. ---
  static async addExpense(expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> {
    const expenses = await this.getExpenses();
    const newExpense: Expense = { id: `exp_${Date.now()}`, ...expense, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
    expenses.push(newExpense);
    await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);
    return newExpense;
  }
  static async getExpenses(filters: ExpenseFilters = {}): Promise<Expense[]> {
    const expenses: Expense[] = (await StorageService.get(this.STORAGE_KEYS.EXPENSES)) || [];
    return expenses.filter(e => {
        if (filters.outletId && e.outletId !== filters.outletId) {return false;}
        if (filters.category && e.category !== filters.category) {return false;}
        return true;
      }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }
  static async updateExpense(id: string, updates: Partial<Expense>): Promise<Expense> {
    const expenses = await this.getExpenses();
    const index = expenses.findIndex(e => e.id === id);
    if (index === -1) {throw new AppError('Expense not found');}
    const updatedExpense = { ...expenses[index], ...updates, updatedAt: new Date().toISOString() };
    expenses[index] = updatedExpense;
    await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);
    return updatedExpense;
  }
  static async deleteExpense(id: string): Promise<void> {
    const expenses = await this.getExpenses();
    const filtered = expenses.filter(e => e.id !== id);
    await StorageService.set(this.STORAGE_KEYS.EXPENSES, filtered);
  }
  static async getCashReconciliations(filters: ReconciliationFilters = {}): Promise<CashReconciliation[]> {
    const reconciliations: CashReconciliation[] = (await StorageService.get(this.STORAGE_KEYS.RECONCILIATIONS)) || [];
    const filtered = reconciliations.filter(r => {
      if (filters.startDate && new Date(r.date) < new Date(filters.startDate)) {return false;}
      if (filters.endDate && new Date(r.date) > new Date(filters.endDate)) {return false;}
      if (filters.status && r.status !== filters.status) {return false;}
      if (filters.outletId && r.outletId !== filters.outletId) {return false;}
      return true;
    });
    return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }
  static async performCashReconciliation(data: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>): Promise<CashReconciliation> {
    const reconciliations = await this.getCashReconciliations();
    const newReconciliation: CashReconciliation = { id: `rec_${Date.now()}`, ...data, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
    reconciliations.unshift(newReconciliation);
    await StorageService.set(this.STORAGE_KEYS.RECONCILIATIONS, reconciliations);
    return newReconciliation;
  }

  // --- Main Calculation Functions ---

  static async getExpectedCashBreakdown(date: string, outletId?: string): Promise<ExpectedCashData> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const allRecons = await this.getCashReconciliations({ outletId });
    const lastRecon = allRecons.find(r => new Date(r.date) < startOfDay);
    const openingCash = lastRecon ? lastRecon.actualCash : 0;

    const dayOrders = await this.getOrders(date, date, outletId);
    const dayExpenses = await this.getExpenses({ startDate: date, endDate: date, outletId });

    // Determine "cash" orders based on available fields. We don't have a payment method field,
    // so treat orders with a paid amount or paymentStatus === 'paid' as contributing to cash.
    const cashOrders = dayOrders.filter(order => {
      const paid = typeof order.paidAmount === 'number' ? order.paidAmount > 0 : false;
      const isPaidStatus = typeof order.paymentStatus === 'string' ? order.paymentStatus.toLowerCase() === 'paid' : false;
      return paid || isPaidStatus;
    });

    const cashSales = cashOrders.reduce((sum, order) => sum + (order.paidAmount || 0), 0);

    const cashExpenses = dayExpenses.reduce((sum, e) => sum + e.amount, 0);
    const expectedClosingCash = openingCash + cashSales - cashExpenses;

    return { openingCash, cashSales, orderCount: cashOrders.length, cashExpenses, expectedClosingCash };
  }

  static async generateProfitLossStatement(
    startDate: string,
    endDate: string,
    outletId?: string
  ): Promise<ProfitLossData> {
    const periodOrders = await this.getOrders(startDate, endDate, outletId);
    
    // Calculate revenue from paid orders only
    const revenue = periodOrders.reduce((sum, order) => sum + (order.paidAmount || 0), 0);

    const periodExpenses = await this.getExpenses({ startDate, endDate, outletId });
    const totalExpenses = periodExpenses.reduce((sum, expense) => sum + expense.amount, 0);

    const netProfit = revenue - totalExpenses;
    const profitMargin = revenue > 0 ? netProfit / revenue : 0;

    return { revenue, expenses: totalExpenses, grossProfit: revenue, netProfit, profitMargin, period: { startDate, endDate }};
  }
  
  static async getPaymentMethodAnalytics(
    startDate: string,
    endDate: string,
    outletId?: string
  ): Promise<PaymentAnalytics> {
    const orders = await this.getOrders(startDate, endDate, outletId);
    const analytics: PaymentAnalytics = { totalTransactions: 0, totalAmount: 0, paymentMethods: {}, trends: [] };

    orders.forEach(order => {
        // We don't track payment method on Order; group under 'unknown' and use paidAmount for totals
        const method = 'unknown';
        const amount = order.paidAmount || 0;
        
        analytics.totalTransactions++;
        analytics.totalAmount += amount;
        
        if (!analytics.paymentMethods[method]) {
            analytics.paymentMethods[method] = { count: 0, amount: 0, percentage: 0 };
        }
        analytics.paymentMethods[method].count++;
        analytics.paymentMethods[method].amount += amount;
    });

    for(const method in analytics.paymentMethods){
        analytics.paymentMethods[method].percentage = analytics.totalAmount > 0 ? (analytics.paymentMethods[method].amount / analytics.totalAmount) * 100 : 0;
    }
    
    return analytics;
  }
  
  static async getTaxSummary(
    startDate: string,
    endDate: string,
    outletId?: string
  ): Promise<TaxSummary> {
    const orders = await this.getOrders(startDate, endDate, outletId);
    
    // Calculate tax on paid revenue only
    const revenue = orders.reduce((sum, order) => sum + (order.paidAmount || 0), 0);
    const taxRate = 0.05;
    
    return {
      totalTaxableAmount: revenue,
      totalTaxCollected: revenue * taxRate,
      taxRate,
      period: { startDate, endDate },
      breakdown: {}
    };
  }
}