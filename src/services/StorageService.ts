import AsyncStorage from '@react-native-async-storage/async-storage';
import LoggingService from './LoggingService';

export class StorageService {
  // Cache to improve performance
  private static cache = new Map<string, any>();
  private static cacheExpiry = new Map<string, number>();
  private static defaultTTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Custom error class for storage operations
   */
  private static StorageError = class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'StorageError';
    }
  };

  /**
   * Get data from storage with caching support
   */
  static async get<T = any>(key: string, useCache: boolean = true, ttl: number = this.defaultTTL): Promise<T | null> {
    try {
      // Check cache first
      if (useCache && this.cache.has(key)) {
        const expiry = this.cacheExpiry.get(key) || 0;
        if (Date.now() < expiry) {
          LoggingService.debug(`Storage cache hit for key: ${key}`, 'STORAGE');
          return this.cache.get(key);
        } else {
          // Remove expired cache
          this.cache.delete(key);
          this.cacheExpiry.delete(key);
        }
      }

      // PRODUCTION FIX: Add timeout for AsyncStorage operations
      const storageTimeout = new Promise<string | null>((_, reject) => {
        setTimeout(() => reject(new Error('Storage operation timeout')), 5000);
      });
      
      const value = await Promise.race([
        AsyncStorage.getItem(key),
        storageTimeout
      ]);
      
      const parsedValue: T | null = value ? JSON.parse(value) : null;

      // Update cache
      if (useCache && parsedValue !== null) {
        this.cache.set(key, parsedValue);
        this.cacheExpiry.set(key, Date.now() + ttl);
      }

      LoggingService.debug(`Storage get for key: ${key}`, 'STORAGE', {
        found: parsedValue !== null,
      });
      return parsedValue;
    } catch (error) {
      LoggingService.error(`Failed to get data for key: ${key}`, 'STORAGE', error as Error);
      
      // PRODUCTION FIX: Return cached value if available, otherwise null
      if (useCache && this.cache.has(key)) {
        LoggingService.info(`Returning cached value for failed storage read: ${key}`, 'STORAGE');
        return this.cache.get(key);
      }
      
      // Don't throw error in production, return null
      return null;
    }
  }

  /**
   * Set data to storage with caching and size validation
   */
  static async set(key: string, value: any, updateCache: boolean = true): Promise<boolean> {
    try {
      const serializedValue = JSON.stringify(value);

      // Check if the serialized value is too large (AsyncStorage has limits)
      const sizeInBytes = new Blob([serializedValue]).size;
      const maxSizeInBytes = 6 * 1024 * 1024; // 6MB limit for safety

      if (sizeInBytes > maxSizeInBytes) {
        LoggingService.warn(
          `Data too large for key ${key}: ${(sizeInBytes / 1024 / 1024).toFixed(2)}MB`,
          'STORAGE'
        );
        // PRODUCTION FIX: Don't throw error, just warn and skip
        return false;
      }

      // PRODUCTION FIX: Add timeout for AsyncStorage operations
      const storageTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Storage set timeout')), 5000);
      });
      
      await Promise.race([
        AsyncStorage.setItem(key, serializedValue),
        storageTimeout
      ]);

      // Update cache
      if (updateCache) {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
      }

      LoggingService.debug(`Storage set for key: ${key}`, 'STORAGE', {
        size: `${(sizeInBytes / 1024).toFixed(2)}KB`,
      });
      return true;
    } catch (error) {
      LoggingService.error(`Failed to set data for key: ${key}`, 'STORAGE', error as Error);
      
      // PRODUCTION FIX: Update cache even if storage fails
      if (updateCache) {
        try {
          this.cache.set(key, value);
          this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
          LoggingService.info(`Data cached despite storage failure for key: ${key}`, 'STORAGE');
        } catch (cacheError) {
          LoggingService.error(`Failed to cache data for key: ${key}`, 'STORAGE', cacheError as Error);
        }
      }
      
      return false;
    }
  }

  /**
   * Remove data from storage and cache
   */
  static async remove(key: string): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(key);
      this.cache.delete(key);
      this.cacheExpiry.delete(key);

      LoggingService.debug(`Storage remove for key: ${key}`, 'STORAGE');
      return true;
    } catch (error) {
      LoggingService.error(`Failed to remove data for key: ${key}`, 'STORAGE', error as Error);
      throw new this.StorageError(`Failed to remove data: ${key}`);
    }
  }

  /**
   * PRODUCTION FIX: Safe storage cleanup when corruption is detected
   */
  static async clearCorruptedData(): Promise<void> {
    try {
      LoggingService.warn('Clearing corrupted storage data', 'STORAGE');
      
      const keys = await AsyncStorage.getAllKeys();
      const appKeys = keys.filter(key => 
        key.startsWith('tailorShop/') || 
        key.startsWith('products') || 
        key.startsWith('orders') || 
        key.startsWith('customers')
      );
      
      for (const key of appKeys) {
        try {
          await AsyncStorage.removeItem(key);
        } catch (error) {
          // Continue removing other keys even if one fails
          LoggingService.error(`Failed to remove corrupted key: ${key}`, 'STORAGE', error as Error);
        }
      }
      
      // Clear cache
      this.cache.clear();
      this.cacheExpiry.clear();
      
      LoggingService.info('Corrupted storage data cleared successfully', 'STORAGE');
    } catch (error) {
      LoggingService.error('Failed to clear corrupted storage data', 'STORAGE', error as Error);
    }
  }
  
  /**
   * Clear all data from storage and cache
   */
  static async clear(): Promise<boolean> {
    try {
      // Clear AsyncStorage
      await AsyncStorage.clear();
      
      // Clear cache
      this.cache.clear();
      this.cacheExpiry.clear();
      
      LoggingService.info('Storage and cache cleared successfully', 'STORAGE');
      return true;
    } catch (error) {
      LoggingService.error('Failed to clear storage and cache', 'STORAGE', error as Error);
      throw new this.StorageError('Failed to clear storage');
    }
  }

  /**
   * Get all keys from storage
   */
  static async getAllKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      LoggingService.debug(`Storage getAllKeys returned ${keys.length} keys`, 'STORAGE');
      return [...keys]; // Convert readonly array to mutable array
    } catch (error) {
      LoggingService.error('Failed to get all keys', 'STORAGE', error as Error);
      throw new this.StorageError('Failed to get all keys');
    }
  }

  /**
   * Get multiple keys at once
   */
  static async getMultiple(keys: string[]): Promise<Record<string, any>> {
    try {
      const results = await AsyncStorage.multiGet(keys);
      const data: Record<string, any> = {};

      results.forEach(([key, value]) => {
        data[key] = value ? JSON.parse(value) : null;
      });

      LoggingService.debug(`Storage getMultiple for keys: ${keys.join(', ')}`, 'STORAGE');
      return data;
    } catch (error) {
      LoggingService.error('Failed to get multiple keys', 'STORAGE', error as Error);
      throw new this.StorageError('Failed to retrieve multiple data');
    }
  }

  /**
   * Set multiple key-value pairs at once
   */
  static async setMultiple(keyValuePairs: Array<[string, any]>): Promise<boolean> {
    try {
      const serializedPairs: Array<[string, string]> = keyValuePairs.map(([key, value]) => [
        key,
        JSON.stringify(value),
      ]);

      await AsyncStorage.multiSet(serializedPairs);

      // Update cache
      keyValuePairs.forEach(([key, value]) => {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
      });

      LoggingService.debug('Storage setMultiple completed', 'STORAGE');
      return true;
    } catch (error) {
      LoggingService.error('Failed to set multiple keys', 'STORAGE', error as Error);
      throw new this.StorageError('Failed to save multiple data');
    }
  }

  /**
   * Check if key exists
   */
  static async exists(key: string): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value !== null;
    } catch (error) {
      // console.error(`Failed to check existence for key: ${key}`, error as any);
      return false;
    }
  }

  /**
   * Get storage size information
   */
  static async getStorageInfo(): Promise<{
    totalKeys: number;
    totalSize: number;
    keyInfo: Array<{ key: string; size: number }>;
    cacheSize: number;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const values = await AsyncStorage.multiGet(keys);

      let totalSize = 0;
      const keyInfo = values.map(([key, value]) => {
        const size = value ? value.length : 0;
        totalSize += size;
        return { key, size };
      });

      return {
        totalKeys: keys.length,
        totalSize,
        keyInfo,
        cacheSize: this.cache.size,
      };
    } catch (error) {
      LoggingService.error('Failed to get storage info', 'STORAGE', error as Error);
      throw new this.StorageError('Failed to get storage information');
    }
  }

  /**
   * Clear expired cache entries
   */
  static clearExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cacheExpiry.forEach((expiry, key) => {
      if (expiry < now) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
    });

    if (expiredKeys.length > 0) {
      LoggingService.debug(`Cleared ${expiredKeys.length} expired cache entries`, 'STORAGE');
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): {
    size: number;
    entries: number;
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      entries: this.cacheExpiry.size,
    };
  }

  /**
   * Force refresh cache for a specific key
   */
  static async refreshCache(key: string): Promise<any> {
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
    return this.get(key, true);
  }

  /**
   * Store large data by splitting into chunks
   */
  static async setLargeData<T>(key: string, data: T[], chunkSize: number = 100): Promise<boolean> {
    try {
      // Create chunks
      const chunks: T[][] = [];
      for (let i = 0; i < data.length; i += chunkSize) {
        chunks.push(data.slice(i, i + chunkSize));
      }

      // Store metadata
      const metadata = {
        totalItems: data.length,
        chunkCount: chunks.length,
        chunkSize,
        createdAt: Date.now(),
      };
      await this.set(`${key}_metadata`, metadata);

      // Store each chunk
      for (let i = 0; i < chunks.length; i++) {
        await this.set(`${key}_chunk_${i}`, chunks[i]);
      }

      LoggingService.info(
        `Large data stored in ${chunks.length} chunks for key: ${key}`,
        'STORAGE'
      );
      return true;
    } catch (error) {
      LoggingService.error(`Failed to set large data for key: ${key}`, 'STORAGE', error as Error);
      throw new this.StorageError(`Failed to save large data: ${key}`);
    }
  }

  /**
   * Get large data by reconstructing from chunks
   */
  static async getLargeData<T = any>(key: string): Promise<T[] | null> {
    try {
      // Get metadata
      const metadata = await this.get<{
        totalItems: number;
        chunkCount: number;
        chunkSize: number;
        createdAt: number;
      }>(`${key}_metadata`);

      if (!metadata) {
        return null;
      }

      // Get all chunks
      const chunks: T[][] = [];
      for (let i = 0; i < metadata.chunkCount; i++) {
        const chunk = await this.get<T[]>(`${key}_chunk_${i}`);
        if (chunk) {
          chunks.push(chunk);
        }
      }

      // Reconstruct the original array
      const result: T[] = [];
      chunks.forEach(chunk => {
        result.push(...chunk);
      });

      LoggingService.info(
        `Large data retrieved: ${result.length}/${metadata.totalItems} items for key: ${key}`,
        'STORAGE'
      );
      return result;
    } catch (error) {
      LoggingService.error(`Failed to get large data for key: ${key}`, 'STORAGE', error as Error);
      throw new this.StorageError(`Failed to retrieve large data: ${key}`);
    }
  }

  /**
   * Clean up chunks for large data
   */
  static async removeLargeData(key: string): Promise<boolean> {
    try {
      // Get metadata to know how many chunks to remove
      const metadata = await this.get<{ chunkCount: number }>(`${key}_metadata`);
      
      if (metadata) {
        // Remove all chunks
        for (let i = 0; i < metadata.chunkCount; i++) {
          await this.remove(`${key}_chunk_${i}`);
        }
      }

      // Remove metadata
      await this.remove(`${key}_metadata`);

      LoggingService.info(`Large data removed for key: ${key}`, 'STORAGE');
      return true;
    } catch (error) {
      LoggingService.error(`Failed to remove large data for key: ${key}`, 'STORAGE', error as Error);
      throw new this.StorageError(`Failed to remove large data: ${key}`);
    }
  }
}