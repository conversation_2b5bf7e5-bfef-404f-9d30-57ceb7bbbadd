import React from 'react';

import LoggingService from './LoggingService';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  bundleSize?: number;
  networkRequests: number;
  errorRate: number;
  crashCount: number;
}

interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  type: 'render' | 'network' | 'storage' | 'compute';
  metadata?: Record<string, any>;
}

class PerformanceMonitoringService {
  private static instance: PerformanceMonitoringService;
  private metrics: PerformanceMetrics;
  private entries: PerformanceEntry[];
  private timers: Map<string, number>;
  private isEnabled: boolean;

  private constructor() {
    this.metrics = {
      renderTime: 0,
      memoryUsage: 0,
      networkRequests: 0,
      errorRate: 0,
      crashCount: 0,
    };
    this.entries = [];
    this.timers = new Map();
    this.isEnabled = __DEV__; // Enable only in development by default
  }

  public static getInstance(): PerformanceMonitoringService {
    if (!PerformanceMonitoringService.instance) {
      PerformanceMonitoringService.instance = new PerformanceMonitoringService();
    }
    return PerformanceMonitoringService.instance;
  }

  public enable(): void {
    this.isEnabled = true;
    LoggingService.info('Performance monitoring enabled', 'PERFORMANCE');
  }

  public disable(): void {
    this.isEnabled = false;
    LoggingService.info('Performance monitoring disabled', 'PERFORMANCE');
  }

  public startTimer(name: string): void {
    if (!this.isEnabled) {return;}
    this.timers.set(name, Date.now());
  }

  public endTimer(name: string, type: PerformanceEntry['type'] = 'compute', metadata?: Record<string, any>): number {
    if (!this.isEnabled) {return 0;}
    
    const startTime = this.timers.get(name);
    if (!startTime) {
      LoggingService.warn(`Timer '${name}' was not started`, 'PERFORMANCE');
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(name);

    const entry: PerformanceEntry = {
      name,
      startTime,
      duration,
      type,
      metadata,
    };

    this.entries.push(entry);

    // Keep only last 100 entries to prevent memory issues
    if (this.entries.length > 100) {
      this.entries = this.entries.slice(-50);
    }

    if (duration > 100) { // Log slow operations
      LoggingService.warn(`Slow ${type} operation: ${name} took ${duration}ms`, 'PERFORMANCE', metadata);
    }

    return duration;
  }

  public recordRenderTime(componentName: string, renderTime: number): void {
    if (!this.isEnabled) {return;}
    
    this.metrics.renderTime = renderTime;
    
    const entry: PerformanceEntry = {
      name: componentName,
      startTime: Date.now() - renderTime,
      duration: renderTime,
      type: 'render',
      metadata: { componentName },
    };

    this.entries.push(entry);
    
    if (renderTime > 16.67) { // 60fps threshold
      LoggingService.warn(`Slow render: ${componentName} took ${renderTime.toFixed(2)}ms`, 'PERFORMANCE');
    }
  }

  public recordNetworkRequest(url: string, duration: number, success: boolean): void {
    if (!this.isEnabled) {return;}
    
    this.metrics.networkRequests++;
    if (!success) {
      this.metrics.errorRate = (this.metrics.errorRate * (this.metrics.networkRequests - 1) + 1) / this.metrics.networkRequests;
    }

    const entry: PerformanceEntry = {
      name: `Network: ${url}`,
      startTime: Date.now() - duration,
      duration,
      type: 'network',
      metadata: { url, success },
    };

    this.entries.push(entry);
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getEntries(): PerformanceEntry[] {
    return [...this.entries];
  }

  public clearEntries(): void {
    this.entries = [];
    LoggingService.info('Performance entries cleared', 'PERFORMANCE');
  }

  public generateReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      slowOperations: this.entries.filter(e => e.duration > 100),
      averageRenderTime: this.getAverageRenderTime(),
      totalEntries: this.entries.length,
    };

    return JSON.stringify(report, null, 2);
  }

  private getAverageRenderTime(): number {
    const renderEntries = this.entries.filter(e => e.type === 'render');
    if (renderEntries.length === 0) {return 0;}
    
    const total = renderEntries.reduce((sum, entry) => sum + entry.duration, 0);
    return total / renderEntries.length;
  }
}

// React HOC for automatic performance tracking
export function withPerformanceTracking<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
): React.ComponentType<P> {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
  
  const PerformanceTrackedComponent = (props: P) => {
    const perfService = PerformanceMonitoringService.getInstance();
    const renderStartTime = React.useRef<number>(0);

    React.useLayoutEffect(() => {
      renderStartTime.current = Date.now();
    });

    React.useLayoutEffect(() => {
      const renderTime = Date.now() - renderStartTime.current;
      perfService.recordRenderTime(displayName, renderTime);
    });

    return React.createElement(WrappedComponent, props);
  };

  PerformanceTrackedComponent.displayName = `withPerformanceTracking(${displayName})`;
  
  return PerformanceTrackedComponent;
}

// React Hook for manual performance tracking
export function usePerformanceTracking(operationName: string) {
  const perfService = React.useMemo(() => PerformanceMonitoringService.getInstance(), []);
  
  const startTracking = React.useCallback((name?: string) => {
    const trackingName = name || operationName;
    perfService.startTimer(trackingName);
    return trackingName;
  }, [perfService, operationName]);
  
  const endTracking = React.useCallback((name?: string, type?: PerformanceEntry['type'], metadata?: Record<string, any>) => {
    const trackingName = name || operationName;
    return perfService.endTimer(trackingName, type, metadata);
  }, [perfService, operationName]);
  
  return { startTracking, endTracking };
}

export default PerformanceMonitoringService.getInstance();