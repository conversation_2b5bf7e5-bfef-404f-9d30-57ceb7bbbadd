import AsyncStorage from '@react-native-async-storage/async-storage';

import { DataState, Settings } from '../types/data';

import LoggingService from './LoggingService';
import masterDb from './MasterDatabaseService';

// Initial state for reference during data loading/clearing
const initialState: Partial<DataState> = {
  products: [],
  orders: [],
  customers: [],
  settings: {
    storeName: 'TailorZap',
    ownerName: 'Business Owner',
    email: '<EMAIL>',
    phone: '***********',
    address: 'Business Address',
    taxRate: 0.08,
    currency: '৳',
    notifications: true,
    darkMode: false,
    autoBackup: true,
    storeHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true },
    },
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      card: { enabled: true, processingFee: 2.9 },
      digitalWallet: { enabled: false, processingFee: 2.5 },
      bankTransfer: { enabled: false, processingFee: 1.0 },
      giftCard: { enabled: true, processingFee: 0 },
    },
  },
  nextProductId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  isDataLoaded: false,
};

interface DataLoadResult {
  data: Partial<DataState>;
  source: 'masterDb' | 'asyncStorage';
}

const loadDataFromAsyncStorage = async (): Promise<Partial<DataState>> => {
  try {
    const consolidatedData = await AsyncStorage.getItem('tailorShop/data');
    if (consolidatedData) {
      try {
        const parsed = JSON.parse(consolidatedData) as Partial<DataState>;
        return {
                    products: parsed.products ?? [],
          orders: parsed.orders ?? [],
          customers: parsed.customers ?? [],
          warehouses: parsed.warehouses ?? [],
          nextProductId: parsed.nextProductId ?? initialState.nextProductId,
          nextOrderId: parsed.nextOrderId ?? initialState.nextOrderId,
          nextCustomerId: parsed.nextCustomerId ?? initialState.nextCustomerId,
        };
      } catch (e) {
        LoggingService.debug(
          'Failed to parse consolidated tailorShopData; falling back to legacy keys',
          'DATA_PERSISTENCE_SERVICE',
          e as Error
        );
      }
    }

    // Backward-compatible legacy loads if consolidated not present
    const [productsData, ordersData, customersData, staffData] = await Promise.all([
      AsyncStorage.getItem('products'),
      AsyncStorage.getItem('orders'),
      AsyncStorage.getItem('customers'),
      AsyncStorage.getItem('staff'),
    ]);

    const loadedData: Partial<DataState> = {};
    if (productsData) {loadedData.products = JSON.parse(productsData);}
    if (ordersData) {loadedData.orders = JSON.parse(ordersData);}
    if (customersData) {loadedData.customers = JSON.parse(customersData);}
    if (staffData) {loadedData.staff = JSON.parse(staffData);}

    const settingsData = await AsyncStorage.getItem('tailorShop/settings');
    if (settingsData) {loadedData.settings = JSON.parse(settingsData);}

    return loadedData;
  } catch (error) {
    LoggingService.error(
      'Error loading data from AsyncStorage fallback',
      'DATA_PERSISTENCE_SERVICE',
      error as Error
    );
    return {};
  }
};

const loadDataFromMasterDatabase = async (): Promise<Partial<DataState>> => {
  try {
    LoggingService.info('Loading data from Master Database Service...', 'DATA_PERSISTENCE_SERVICE');

    const [products, orders, customers, staff, settingsArray] = await Promise.all([
      masterDb.getProducts(),
      masterDb.getOrders(),
      masterDb.getCustomers({ is_active: true }),
      masterDb.getStaff({ is_active: true }),
      masterDb.getSettingsByCategory('app'),
    ]);

    const settings: Partial<Settings> = {};
    if (settingsArray && Array.isArray(settingsArray)) {
      settingsArray.forEach((s: any) => {
        (settings as any)[s.key] = s.value;
      });
    }

    return {
      products: products.length > 0 ? products : undefined,
      orders: orders.length > 0 ? orders : undefined,
      customers: customers.length > 0 ? customers : undefined,
      settings: Object.keys(settings).length > 0 ? settings as any : undefined,
    };
  } catch (error) {
    LoggingService.error(
      'Error loading data from Master Database Service',
      'DATA_PERSISTENCE_SERVICE',
      error as Error
    );
    throw error; // Re-throw to trigger fallback
  }
};

export const loadAllData = async (): Promise<DataLoadResult> => {
  const startTime = Date.now();
  try {
    LoggingService.info('Attempting to load data from Master Database Service...', 'DATA_PERSISTENCE_SERVICE');
    const data = await loadDataFromMasterDatabase();
    const loadTime = Date.now() - startTime;
    LoggingService.info(`Data loaded successfully from Master Database Service in ${loadTime}ms`, 'DATA_PERSISTENCE_SERVICE');
    return { data, source: 'masterDb' };
  } catch (error) {
    LoggingService.debug('Master Database not available, using AsyncStorage', 'DATA_PERSISTENCE_SERVICE');
    const data = await loadDataFromAsyncStorage();
    const loadTime = Date.now() - startTime;
    LoggingService.info(`Data loaded successfully from AsyncStorage in ${loadTime}ms`, 'DATA_PERSISTENCE_SERVICE');
    return { data, source: 'asyncStorage' };
  }
};

const saveDataToMasterDatabase = async (data: Partial<DataState>): Promise<void> => {
  try {
    LoggingService.debug('Saving data to Master Database Service...', 'DATA_PERSISTENCE_SERVICE');

    if (data.settings) {
      const settingsEntries = Object.entries(data.settings);
      for (const [key, value] of settingsEntries) {
        await masterDb.setSetting('app', key, value);
      }
    }

    // Add logic to save products, orders, customers if they are managed by masterDb
    // For now, assuming masterDb handles settings primarily for this context

    LoggingService.info('Data saved successfully to Master Database Service', 'DATA_PERSISTENCE_SERVICE');
  } catch (error) {
    LoggingService.error(
      'Error saving data to Master Database Service',
      'DATA_PERSISTENCE_SERVICE',
      error as Error
    );
    throw error; // Re-throw to trigger fallback
  }
};

const saveDataToAsyncStorage = async (data: Partial<DataState>): Promise<void> => {
  try {
    const snapshot: Partial<DataState> = {
            products: data.products,
      orders: data.orders,
      customers: data.customers,
      warehouses: data.warehouses,
    };
    await AsyncStorage.setItem('tailorShop/data', JSON.stringify(snapshot));

    // Save settings separately as well for backward compatibility
    if (data.settings) {
      await AsyncStorage.setItem('tailorShop/settings', JSON.stringify(data.settings));
    }

    LoggingService.info('Data snapshot saved to AsyncStorage successfully', 'DATA_PERSISTENCE_SERVICE');
  } catch (error) {
    LoggingService.error(
      'Error saving data to AsyncStorage fallback',
      'DATA_PERSISTENCE_SERVICE',
      error as Error
    );
    // Don't crash the app on save errors
  }
};

export const saveAllData = async (data: Partial<DataState>): Promise<void> => {
  try {
    LoggingService.debug('Attempting to save data to Master Database Service...', 'DATA_PERSISTENCE_SERVICE');
    await saveDataToMasterDatabase(data);
    LoggingService.info('Data saved successfully to Master Database Service', 'DATA_PERSISTENCE_SERVICE');
  } catch (error) {
    LoggingService.debug('Master Database not available, using AsyncStorage', 'DATA_PERSISTENCE_SERVICE');
    await saveDataToAsyncStorage(data);
    LoggingService.info('Data saved successfully to AsyncStorage', 'DATA_PERSISTENCE_SERVICE');
  }
};

export const clearAllPersistenceData = async (): Promise<void> => {
  try {
    await masterDb.clearAllData();
    await AsyncStorage.clear();
    LoggingService.info('All persistence data cleared successfully', 'DATA_PERSISTENCE_SERVICE');
  } catch (error) {
    LoggingService.error('Error clearing persistence data', 'DATA_PERSISTENCE_SERVICE', error as Error);
    throw error;
  }
};

export const resetAllPersistenceData = async (): Promise<void> => {
  try {
    LoggingService.info('Resetting all persistence data and database', 'DATA_PERSISTENCE_SERVICE');
    await masterDb.resetDatabase();
    await AsyncStorage.clear();
    LoggingService.info('All persistence data and database reset successfully', 'DATA_PERSISTENCE_SERVICE');
  } catch (error) {
    LoggingService.error('Failed to reset all persistence data', 'DATA_PERSISTENCE_SERVICE', error as Error);
    throw error;
  }
};

export const restorePersistenceData = async (backupData: any): Promise<void> => {
  try {
    await AsyncStorage.setItem('tailorShop/data', JSON.stringify(backupData));
    LoggingService.info('Data restored successfully to AsyncStorage from backup', 'DATA_PERSISTENCE_SERVICE');
  } catch (error) {
    LoggingService.error(
      'Failed to restore data to AsyncStorage from backup',
      'DATA_PERSISTENCE_SERVICE',
      error as Error
    );
    throw error;
  }
};
