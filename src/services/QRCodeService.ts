import * as FileSystem from 'expo-file-system';
import { Share } from 'react-native';

import LoggingService from './LoggingService';
import UnifiedToast from './ToastService'; // Assuming this is your custom toast service

// --- STEP 1: Define Strong Types ---
// Using an enum for entity types prevents typos and magic strings.
export enum EntityType {
  Order = 'order',
  Product = 'product',
  Garment = 'garment',
  Customer = 'customer',
  Appointment = 'appointment',
  Measurement = 'measurement',
  Fabric = 'fabric',
}

// Define basic interfaces for your data models. These should match your app's actual data structure.
interface BaseEntity {
  id: string;
}

interface Order extends BaseEntity {
  name?: string; // Add properties as needed for QR generation
}

interface Product extends BaseEntity {
  name: string;
  price: number;
}

interface Customer extends BaseEntity {
  name: string;
  phone: string;
}

interface Appointment extends BaseEntity {
  customerId: string;
  date: string;
}

interface Measurement extends BaseEntity {
  customerId: string;
}

interface Fabric extends BaseEntity {
  name: string;
  pricePerMeter: number;
}

// A union type for any entity the service can handle.
type Entity = Order | Product | Customer | Appointment | Measurement | Fabric;

// Define a clear structure for the result of a QR scan.
// FIXED: Added export to make this type available for import
export interface ParsedQRData {
  app: 'TailorManagement';
  version: string;
  type: EntityType;
  id: string;
  [key: string]: any; // Allow for other expanded properties
}

// FIXED: Added export to make this type available for import
export interface ParseResult {
  success: boolean;
  data?: ParsedQRData;
  error?: string;
  rawData?: string;
}

// --- STEP 2: Refactor the Service ---

class QRCodeService {

  /**
   * Generates a compact, URL-safe JSON string for a given entity.
   * Uses shortened keys to minimize QR code complexity.
   * @param type - The type of the entity.
   * @param entity - The data object.
   * @returns A JSON string representing the QR code data.
   */
  public generateQRString(type: EntityType, entity: Entity): string {
    const baseData = {
      app: 'TM', // App identifier
      v: '1.0',  // Version
      t: type.charAt(0), // Type (o, c, p, etc.)
      id: entity.id,
    };

    let specificData: object = {};

    // Type guards for safe property access
    const isProduct = (e: any): e is Product => e.name !== undefined && e.price !== undefined;
    const isCustomer = (e: any): e is Customer => e.name !== undefined && e.phone !== undefined;
    const isAppointment = (e: any): e is Appointment => e.customerId !== undefined && e.date !== undefined;
    const isMeasurement = (e: any): e is Measurement => e.customerId !== undefined;
    const isFabric = (e: any): e is Fabric => e.name !== undefined && e.pricePerMeter !== undefined;

    if ((type === EntityType.Product || type === EntityType.Garment) && isProduct(entity)) {
      specificData = { n: entity.name.substring(0, 20), p: entity.price };
    } else if (type === EntityType.Customer && isCustomer(entity)) {
      specificData = { n: entity.name.substring(0, 20), ph: entity.phone };
    } else if (type === EntityType.Appointment && isAppointment(entity)) {
      specificData = { cid: entity.customerId, d: entity.date.split('T')[0] };
    } else if (type === EntityType.Measurement && isMeasurement(entity)) {
      specificData = { cid: entity.customerId };
    } else if (type === EntityType.Fabric && isFabric(entity)) {
      specificData = { n: entity.name.substring(0, 15), p: entity.pricePerMeter };
    }

    return JSON.stringify({ ...baseData, ...specificData });
  }

  /**
   * Parses a QR code string and expands it into a full data object.
   * @param qrString - The raw string data from the scanner.
   * @returns A structured result object.
   */
  public parseQRString(qrString: string): ParseResult {
    try {
      const data = JSON.parse(qrString);

      if (data.app !== 'TailorManagement' && data.app !== 'TM') {
        throw new Error('QR code not recognized.');
      }

      // If data is already in the full format, return it directly.
      if (data.app === 'TailorManagement') {
        return { success: true, data };
      }

      // If data is in the compact format ('TM'), expand it.
      const expandedData = this.expandOptimizedData(data);
      return { success: true, data: expandedData };

    } catch (error: any) {
      return { success: false, error: "Invalid QR code format.", rawData: qrString };
    }
  }

  /**
   * Converts a compact data object into the full, human-readable format.
   * @param data - The compact data object from a QR code.
   * @returns A fully expanded data object.
   */
  private expandOptimizedData(data: any): ParsedQRData {
    const typeMap: { [key: string]: EntityType } = {
      o: EntityType.Order, c: EntityType.Customer, p: EntityType.Product,
      g: EntityType.Garment, a: EntityType.Appointment, m: EntityType.Measurement, f: EntityType.Fabric
    };

    const fullType = typeMap[data.t] || 'unknown' as EntityType;
    
    const expanded: any = {
      app: 'TailorManagement',
      version: data.v || '1.0',
      type: fullType,
      id: data.id,
      name: data.n, // Generic properties
      phone: data.ph,
      price: data.p,
      customerId: data.cid,
      date: data.d,
    };

    return expanded;
  }
  
  /**
   * Saves a QR code image from a Base64 data URL to the device's file system.
   * @param base64DataUrl - The "data:image/png;base64,..." string from a QR code component.
   * @param filename - The desired filename.
   * @returns The URI of the saved file.
   */
  public async saveQRCodeAsImage(base64DataUrl: string, filename: string): Promise<{ success: boolean; uri?: string; error?: string }> {
    try {
      // Ensure filename is valid
      const safeFilename = filename.replace(/[^a-zA-Z0-9_.-]/g, '_');
      const fileUri = FileSystem.documentDirectory + safeFilename;

      // Extract the raw base64 data
      const base64Code = base64DataUrl.split("base64,")[1];

      await FileSystem.writeAsStringAsync(fileUri, base64Code, {
        encoding: FileSystem.EncodingType.Base64,
      });

      return { success: true, uri: fileUri };
    } catch (error: any) {
      LoggingService.error('Error saving QR code image', 'QR_CODE', error);
      UnifiedToast.error('Failed to save QR code');
      return { success: false, error: error.message };
    }
  }

  /**
   * Shares a QR code's raw data string using the device's native share UI.
   * @param type - The type of the entity.
   * @param entity - The data object.
   */
  public async shareQRCode(type: EntityType, entity: Entity): Promise<{ success: boolean; error?: string }> {
    try {
      const qrString = this.generateQRString(type, entity);
      const entityName = type.charAt(0).toUpperCase() + type.slice(1);

      await Share.share({
        title: `Share ${entityName} QR Code`,
        message: `${entityName} QR Code\n\n${qrString}`,
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error sharing QR code:', error);
      UnifiedToast.error('Failed to share QR code');
      return { success: false, error: error.message };
    }
  }
}

// Export a singleton instance of the service
export default new QRCodeService();