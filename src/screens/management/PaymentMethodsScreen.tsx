import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState, useCallback } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Surface, Switch, Text } from 'react-native-paper';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { useNotifications } from '../../services/notificationService';
import { ActionSheetOption } from '../../types';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface PaymentMethod {
  enabled: boolean;
  processingFee: number;
}

interface PaymentMethods {
  cash: PaymentMethod;
  card: PaymentMethod;
  digitalWallet: PaymentMethod;
  bankTransfer: PaymentMethod;
  giftCard: PaymentMethod;
  [key: string]: PaymentMethod; // Index signature for dynamic access
}

const PaymentMethodsScreen: React.FC = () => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { unreadCount } = useNotifications();
  const navigation = useNavigation();

  const [methods, setMethods] = useState<PaymentMethods>({
    cash: { enabled: true, processingFee: 0 },
    card: { enabled: true, processingFee: 2.9 },
    digitalWallet: { enabled: false, processingFee: 2.5 },
    bankTransfer: { enabled: false, processingFee: 1.0 },
    giftCard: { enabled: true, processingFee: 0 },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // ActionSheet state
  const [showErrorActionSheet, setShowErrorActionSheet] = useState<boolean>(false);
  const [showSuccessActionSheet, setShowSuccessActionSheet] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const paymentOptions = [
    {
      key: 'cash',
      label: 'Cash',
      icon: 'cash',
      description: 'Physical cash payments',
    },
    {
      key: 'card',
      label: 'Credit/Debit Card',
      icon: 'credit-card',
      description: 'Visa, Mastercard, American Express',
    },
    {
      key: 'digitalWallet',
      label: 'Digital Wallet',
      icon: 'wallet',
      description: 'Apple Pay, Google Pay, Samsung Pay',
    },
    {
      key: 'bankTransfer',
      label: 'Bank Transfer',
      icon: 'bank-transfer',
      description: 'Direct bank transfers',
    },
    {
      key: 'giftCard',
      label: 'Gift Cards',
      icon: 'gift',
      description: 'Store gift cards and vouchers',
    },
  ];

  useEffect(() => {
    if (state.settings.paymentMethods) {
      setMethods(state.settings.paymentMethods);
    }
    setErrors({});
  }, [state.settings.paymentMethods]);

  const toggleMethod = (key: keyof PaymentMethods): void => {
    setMethods(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        enabled: !prev[key].enabled,
      },
    }));
  };

  const updateProcessingFee = (key: keyof PaymentMethods, fee: string): void => {
    const numericFee = parseFloat(fee) || 0;
    if (numericFee < 0 || numericFee > 10) {
      setErrors(prev => ({ ...prev, [key]: 'Fee must be between 0% and 10%' }));
      return;
    }

    setErrors(prev => ({ ...prev, [key]: '' }));
    setMethods(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        processingFee: numericFee,
      },
    }));
  };

  const handleSave = (): void => {
    const enabledMethods = Object.keys(methods).filter(key => (methods as any)[key].enabled);

    if (enabledMethods.length === 0) {
      setErrorMessage('Please enable at least one payment method.');
      setShowErrorActionSheet(true);
      return;
    }

    actions.updateSettings({ paymentMethods: methods });
    setSuccessMessage('Payment methods updated successfully!');
    setShowSuccessActionSheet(true);
  };

  const enabledMethods = Object.keys(methods).filter(key => (methods as any)[key].enabled);

  const PaymentMethodCard: React.FC<{ option: any }> = ({ option }) => (
    <Surface style={[styles.methodCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <View style={styles.methodHeader}>
        <View style={styles.methodInfo}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
            <PhosphorIcon name={option.icon} size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.methodText}>
            <Text variant='titleMedium' style={{ color: theme.colors.on }}>
              {option.label}
            </Text>
            <Text variant='bodySmall' style={{ color: theme.colors.onVariant }}>
              {option.description}
            </Text>
          </View>
        </View>
        <Switch
          value={(methods as any)[option.key].enabled}
          onValueChange={() => toggleMethod(option.key)}
          color={theme.colors.primary}
        />
      </View>

      {(methods as any)[option.key].enabled && (
        <View style={styles.feeSection}>
          <TextInput
            label='Processing Fee (%)'
            value={(methods as any)[option.key].processingFee.toString()}
            onChangeText={value => updateProcessingFee(option.key, value)}
            type='number'
            as
            required={true}
            error={errors[option.key] || ''}
            rightAffix='%'
          />
          {errors[option.key] && (
            <Text variant='bodySmall' style={[styles.errorText, { color: theme.colors.error }]}>
              {errors[option.key]}
            </Text>
          )}
        </View>
      )}
    </Surface>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Payment Methods'
        showBack={true}
        onBackPress={() => navigation.goBack()}
        showNotifications={false}
        notificationCount={unreadCount}
        onNotificationPress={() => {
          try {
            (navigation as any).navigate('Notifications');
          } catch (error) {
            LoggingService.error(
              'Failed to navigate to Notifications',
              'PAYMENT_METHODS_SCREEN',
              error as Error
            );
          }
        }}
        
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Summary Card */}
        <Surface
          style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}
          elevation={1}
        >
          <View style={styles.summaryContent}>
            <PhosphorIcon name='info' size={20} color={theme.colors.onPrimaryContainer} />
            <View style={styles.summaryText}>
              <Text variant='titleMedium' style={{ color: theme.colors.onPrimaryContainer }}>
                {enabledMethods.length} payment method{enabledMethods.length !== 1 ? 's' : ''}{' '}
                enabled
              </Text>
              {enabledMethods.length > 0 && (
                <Text
                  variant='bodySmall'
                  style={{ color: theme.colors.onPrimaryContainer, marginTop: 4 }}
                >
                  {paymentOptions
                    .filter(option => methods[option.key].enabled)
                    .map(option => option.label)
                    .join(', ')}
                </Text>
              )}
            </View>
          </View>
        </Surface>

        {/* Payment Methods */}
        {paymentOptions.map(option => (
          <PaymentMethodCard key={option.key} option={option} />
        ))}

        {/* Info Card */}
        <Surface
          style={[styles.infoCard, { backgroundColor: theme.colors.surfaceVariant }]}
          elevation={1}
        >
          <View style={styles.infoHeader}>
            <PhosphorIcon name='lightbulb' size={20} color={theme.colors.primary} />
            <Text variant='titleMedium' style={{ marginLeft: 8, color: theme.colors.on }}>
              Processing Fees
            </Text>
          </View>
          <Text variant='bodySmall' style={{ color: theme.colors.onVariant, marginTop: 8 }}>
            Processing fees are automatically calculated and added to transactions. These fees help
            cover payment processing costs and can be adjusted based on your payment
            processor&apos;s rates.
          </Text>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            variant='outline'
            onPress={() => navigation.goBack()}
            style={{ ...styles.button, ...styles.cancelButton }}
          >
            Cancel
          </Button>
          <Button
            variant='primary'
            onPress={handleSave}
            style={{ ...styles.button, ...styles.saveButton }}
            icon='check'
          >
            Save Changes
          </Button>
        </View>

        {/* ActionSheet for error messages */}
        <ActionSheet
          visible={showErrorActionSheet}
          onDismiss={() => setShowErrorActionSheet(false)}
          title='Error'
          description={errorMessage}
          options={[{ text: 'OK', onPress: () => setShowErrorActionSheet(false), isAction: true }]}
        />

        {/* ActionSheet for success messages */}
        <ActionSheet
          visible={showSuccessActionSheet}
          onDismiss={() => setShowSuccessActionSheet(false)}
          title='Success'
          description={successMessage}
          options={[
            {
              text: 'OK',
              onPress: () => {
                setShowSuccessActionSheet(false);
                navigation.goBack();
              },
              isAction: true,
            },
          ]}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  summaryCard: {
    marginVertical: 16,
    borderRadius: 12,
    padding: 16,
  },
  summaryContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  summaryText: {
    flex: 1,
    marginLeft: 12,
  },
  methodCard: {
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
  },
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  methodText: {
    flex: 1,
  },
  feeSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  errorText: {
    marginLeft: 4,
  },
  infoCard: {
    marginVertical: 16,
    borderRadius: 12,
    padding: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 24,
    paddingBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    marginRight: 6,
  },
  saveButton: {
    marginLeft: 6,
  },
});

export default PaymentMethodsScreen;
