import { useNavigation } from '@react-navigation/native';
import React, { useState, useCallback, useMemo, useRef } from 'react';
import { ScrollView, StyleSheet, View, TextInput as RNTextInput } from 'react-native';
import { Text } from 'react-native-paper';

import ImagePicker from '../../components/forms/ImagePicker';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Dropdown from '../../components/ui/Dropdown';
import Switch from '../../components/ui/Switch';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { ImageProcessingService } from '../../services/ImageProcessingService';
import LoggingService from '../../services/LoggingService';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { ActionSheetOption, Staff } from '../../types';

// --- TYPE DEFINITIONS ---
type StaffRole = 'manager' | 'tailor' | 'assistant';

interface StaffData {
  employeeId: string;
  name: string;
  email: string;
  phone: string;
  role: StaffRole;
  outletId: string;
  profileImage: string;
  isActive: boolean;
}

interface AddStaffScreenProps {
  route?: {
    params?: {
      staff?: Staff;
    };
  };
}

// --- CONSTANTS ---
const roleOptions: { label: string; value: StaffRole }[] = [
  { label: 'Manager', value: 'manager' },
  { label: 'Tailor', value: 'tailor' },
  { label: 'Assistant', value: 'assistant' },
];

const generateEmployeeId = () => `EMP${Date.now().toString().slice(-6)}`;

// --- MAIN COMPONENT ---
const AddStaffScreen: React.FC<AddStaffScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { state: dataState, actions: dataActions } = useData();
  const navigation = useNavigation();

  const editingStaff = route?.params?.staff;
  const isEditing = !!editingStaff;

  // --- REFS FOR KEYBOARD NAVIGATION ---
  const phoneRef = useRef<RNTextInput>(null);
  const emailRef = useRef<RNTextInput>(null);

  // --- STATE MANAGEMENT ---
  const [staffData, setStaffData] = useState<StaffData>({
    employeeId: editingStaff?.employeeId || generateEmployeeId(),
    name: editingStaff?.name || '',
    email: editingStaff?.email || '',
    phone: editingStaff?.phone || '',
    role: (editingStaff?.role as StaffRole) || 'tailor',
    outletId: (editingStaff as any)?.outletId || '', // Handle legacy outletId
    profileImage: editingStaff?.profileImage || '',
    isActive: editingStaff?.isActive ?? true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [actionSheet, setActionSheet] = useState({
    visible: false,
    title: '',
    description: '',
    options: [] as ActionSheetOption[],
  });

  // --- MEMOIZED DERIVED DATA ---
  const outletOptions = useMemo(() =>
    (dataState.warehouses || []).map(warehouse => ({
      label: warehouse.name,
      value: warehouse.id,
    })), [dataState.warehouses]);

  // --- CALLBACKS & HANDLERS ---
  const handleInputChange = useCallback((field: keyof StaffData, value: any) => {
    setStaffData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};
    if (!staffData.name.trim()) {newErrors.name = 'Name is required';}
    if (staffData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(staffData.email)) {
      newErrors.email = 'Invalid email format';
    }
    if (staffData.phone && !/^(\+?880|0?1)[3-9]\d{8}$/.test(staffData.phone)) {
      newErrors.phone = 'Please enter a valid Bangladeshi phone number';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [staffData]);

  const handleSave = useCallback(async () => {
    emailRef.current?.blur();
    
    if (!validateForm()) {return;}
    setIsSaving(true);

    try {
      const finalPayload: any = { ...staffData };

      const isNewImage = finalPayload.profileImage && finalPayload.profileImage.startsWith('file://');
      if (isNewImage) {
        const remoteUrl = await ImageProcessingService.optimizeAndUpload(finalPayload.profileImage);
        finalPayload.profileImage = remoteUrl;
      }
      
      if (isEditing) {
        await dataActions.updateStaff(editingStaff.id, finalPayload);
      } else {
        await dataActions.addStaff(finalPayload);
      }
      navigation.goBack();

    } catch (error) {
      LoggingService.error('Failed to save staff', 'STAFF', error as Error);
      setActionSheet({
        visible: true,
        title: 'Save Failed',
        description: 'An error occurred while saving the staff member. Please try again.',
        options: [{ text: 'OK', onPress: () => setActionSheet(prev => ({ ...prev, visible: false })) }],
      });
    } finally {
      setIsSaving(false);
    }
  }, [validateForm, staffData, isEditing, dataActions, editingStaff, navigation]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={isEditing ? 'Edit Staff Member' : 'Add Staff Member'}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ text: 'Save', onPress: handleSave, color: theme.colors.primary, disabled: isSaving }]}
      />
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
        <View style={styles.profileSection}>
          <ImagePicker
            onImageSelected={uri => handleInputChange('profileImage', uri)}
            currentImage={staffData.profileImage || null}
            placeholder="Add Photo"
            size="large"
            rounded
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Staff Details</Text>
          <TextInput
            label="Full Name"
            value={staffData.name}
            onChangeText={value => handleInputChange('name', value)}
            error={errors.name}
            required
            returnKeyType="next"
            onSubmitEditing={() => phoneRef.current?.focus()}
            blurOnSubmit={false}
          />
          <TextInput
            ref={phoneRef}
            label="Phone Number"
            value={staffData.phone || ''}
            onChangeText={value => handleInputChange('phone', value)}
            error={errors.phone}
            keyboardType="phone-pad"
            returnKeyType="next"
            onSubmitEditing={() => emailRef.current?.focus()}
            blurOnSubmit={false}
          />
          <TextInput
            ref={emailRef}
            label="Email Address"
            value={staffData.email || ''}
            onChangeText={value => handleInputChange('email', value)}
            error={errors.email}
            keyboardType="email-address"
            returnKeyType="done"
            onSubmitEditing={handleSave}
          />
          <TextInput
            label="Employee ID"
            value={staffData.employeeId}
            editable={false}
            onChangeText={() => {}}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Role & Assignment</Text>
          <Dropdown
            label="Role"
            value={staffData.role}
            options={roleOptions}
            onValueChange={value => handleInputChange('role', value as StaffRole)}
          />
          <Dropdown
            label="Outlet / Branch"
            value={staffData.outletId}
            options={outletOptions}
            onValueChange={value => handleInputChange('outletId', value)}
            placeholder="Assign to an outlet"
          />
        </View>

        <View style={styles.section}>
          <View style={styles.switchRow}>
            <Text style={styles.switchTitle}>Active Staff Member</Text>
            <Switch
              value={staffData.isActive}
              onValueChange={value => handleInputChange('isActive', value)}
            />
          </View>
        </View>
      </ScrollView>

      <ActionSheet
        visible={actionSheet.visible}
        onDismiss={() => setActionSheet(prev => ({ ...prev, visible: false }))}
        title={actionSheet.title}
        description={actionSheet.description}
        options={actionSheet.options}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: SPACING.lg, paddingBottom: SPACING.xl * 2 },
  section: { marginBottom: SPACING.xl },
  profileSection: { alignItems: 'center', marginVertical: SPACING.md },
  sectionTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.semibold, marginBottom: SPACING.md },
  switchRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: SPACING.md },
  switchTitle: { fontSize: TYPOGRAPHY.fontSize.md, fontWeight: TYPOGRAPHY.fontWeight.medium },
});

export default AddStaffScreen;