
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, ActivityIndicator } from 'react-native';
import { Text, Surface, Divider, ProgressBar, Title, Paragraph, Portal } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { useNotifications } from '../../services/notificationService';
import { StorageService } from '../../services/StorageService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { ActionSheetOption } from '../../types';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// Constants moved outside the component
const importFormats = [
  { title: 'JSON Format', description: 'Standard JSON file with app data', icon: 'code-json', supported: true, },
  { title: 'CSV Format', description: 'Coming soon', icon: 'file-delimited', supported: false, },
  { title: 'Excel Format', description: 'Coming soon', icon: 'file-excel', supported: false, },
];
const formatBytes = (bytes: number) => {
  if (bytes === 0) {return '0 Bytes';}
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${['Bytes', 'KB', 'MB', 'GB'][i]}`;
};
const formatDate = (date: Date | null) => {
  if (!date) {return 'Never';}
  return new Intl.DateTimeFormat('en-US', { dateStyle: 'medium', timeStyle: 'short' } as any).format(date);
};

type ModalConfig = {
  title: string;
  description: string;
  options: ActionSheetOption[];
};

// Main Component
const DataScreen = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state, actions } = useData();
  const { generateDummyNotifications } = useNotifications();
  const navigation = useNavigation();

  // Component State
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [backupInfo, setBackupInfo] = useState<{ lastBackup: Date | null; backupSize: number; backupCount: number }>({ lastBackup: null, backupSize: 0, backupCount: 0 });
  const [autoBackup, setAutoBackup] = useState(true);

  // --- FIX: Consolidated all modal/actionsheet states into one ---
  const [modal, setModal] = useState({
    visible: false,
    title: '',
    description: '',
    options: [] as ActionSheetOption[],
  });

  const openModal = (config: Partial<ModalConfig>) => setModal((prev) => ({ ...prev, ...config, visible: true }));
  const closeModal = () => setModal((prev) => ({ ...prev, visible: false }));

  // --- Data Loading ---
  const loadScreenData = useCallback(async () => {
    try {
      const backupKeys = (await StorageService.getAllKeys()).filter(key => key.startsWith('backup_'));
      const totalSize = 0;
      let lastBackupDate: Date | null = null;
      // In a real app, you'd calculate size more efficiently
      if (backupKeys.length > 0) {
        const timestampString = backupKeys[0].split('_')[1];
        const timestamp = parseInt(timestampString);
        if (!isNaN(timestamp)) {
          lastBackupDate = new Date(timestamp);
        } else {
          LoggingService.warn(`Invalid timestamp found in backup key: ${backupKeys[0]}`, 'DataScreen');
          lastBackupDate = null; // Or handle as appropriate
        }
      }
      setBackupInfo({ lastBackup: lastBackupDate, backupSize: totalSize, backupCount: backupKeys.length });
    } catch (error) {
      LoggingService.error('Failed to load backup info', 'DataScreen', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadScreenData();
  }, [loadScreenData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadScreenData();
  }, [loadScreenData]);

  // --- Handlers ---
  const handleGenerateSampleData = useCallback(() => {
    openModal({
      title: 'Generate Sample Data?',
      description: 'This will clear all existing data and create new sample content. This action cannot be undone.',
      options: [
        { text: 'Cancel', style: 'cancel', onPress: closeModal },
        { text: 'Generate', style: 'destructive', onPress: async () => {
          closeModal();
          setLoading(true);
          try {
            LoggingService.info('Starting sample data generation', 'DATA_SCREEN');
            LoggingService.info('Starting sample data generation', 'DATA_SCREEN');

            // Generate comprehensive sample data
            await actions.generateSampleData();
            LoggingService.info('Sample data generated successfully', 'DATA_SCREEN');

            await generateDummyNotifications();
            LoggingService.info('Dummy notifications generated successfully', 'DATA_SCREEN');

            openModal({
              title: 'Success!',
              description: 'Sample data and notifications have been generated.',
              options: [{ text: 'OK', onPress: closeModal }],
            });
          } catch (error) {
            LoggingService.error('Sample data generation failed', 'DATA_SCREEN', error as Error);
            LoggingService.error('Sample data generation failed', 'DATA_SCREEN', error as Error);
            openModal({
              title: 'Error',
              description: `Failed to generate sample data: ${(error as Error).message}`,
              options: [{ text: 'OK', onPress: closeModal }]
            });
          } finally {
            setLoading(false);
            loadScreenData(); // Refresh info
          }
        }},
      ],
    });
  }, [actions, generateDummyNotifications, openModal, closeModal, loadScreenData]);
  
  const handleCreateBackup = useCallback(() => {
    openModal({
      title: 'Create Backup?',
      description: 'A new backup of your current data will be created.',
      options: [
        { text: 'Cancel', style: 'cancel', onPress: closeModal },
        { text: 'Create', style: 'primary', onPress: async () => {
          closeModal();
          setIsBackingUp(true);
          try {
            const backupId = `backup_${Date.now()}`;
            await StorageService.set(backupId, { timestamp: Date.now(), data: state });
            await loadScreenData();
            openModal({ title: 'Success!', description: 'Backup created successfully.', options: [{ text: 'OK', onPress: closeModal }] });
          } catch (error) {
            openModal({ title: 'Error', description: 'Failed to create backup.', options: [{ text: 'OK', onPress: closeModal }] });
          } finally {
            setIsBackingUp(false);
          }
        }},
      ],
    });
  }, [state, openModal, closeModal, loadScreenData]);

  

  const handleClearData = useCallback(() => {
    openModal({
      title: 'Clear All Data?',
      description: 'This will permanently delete all data from the application. This action cannot be undone.',
      options: [
        { text: 'Cancel', style: 'cancel', onPress: closeModal },
        { text: 'Clear Data', style: 'destructive', onPress: async () => {
          closeModal();
          setLoading(true);
          try {
            await actions.resetAllData();
            openModal({
              title: 'Success!',
              description: 'All application data has been cleared.',
              options: [{ text: 'OK', onPress: closeModal }],
            });
          } catch (error) {
            LoggingService.error('Failed to clear data', 'DataScreen', error);
            openModal({
              title: 'Error',
              description: 'Failed to clear data.',
              options: [{ text: 'OK', onPress: closeModal }],
            });
          } finally {
            setLoading(false);
            loadScreenData();
          }
        }},
      ],
    });
  }, [actions, openModal, closeModal, loadScreenData]);
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>Loading Data...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Data Management" showBack onBackPress={() => navigation.goBack()} />
      <ScrollView
        contentContainerStyle={{ padding: SPACING.lg, paddingBottom: insets.bottom + 20 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={styles.sectionTitle}>Backup & Restore</Text>
          <View style={styles.infoRow}>
            <Text>Last Backup:</Text>
            <Text>{formatDate(backupInfo.lastBackup)}</Text>
          </View>
          <View style={styles.buttonContainer}>
            <Button variant="primary" onPress={handleCreateBackup} loading={isBackingUp} disabled={isBackingUp}>Create Backup</Button>
            <Button variant="outline" disabled={backupInfo.backupCount === 0}>Restore Backup</Button>
          </View>
        </Surface>

        

        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={styles.sectionTitle}>Sample Data</Text>
          <Paragraph style={styles.description}>Generate sample data for testing. This will clear existing data.</Paragraph>
          <Button variant="danger" onPress={handleGenerateSampleData} loading={loading}>Generate Sample Data</Button>
        </Surface>

        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={styles.sectionTitle}>Clear Data</Text>
          <Paragraph style={styles.description}>Permanently delete all data from the application.</Paragraph>
          <Button variant="danger" onPress={handleClearData} loading={loading}>Clear All Data</Button>
        </Surface>
        
        <Surface style={[styles.warningSection, { backgroundColor: theme.colors.errorContainer }]}>
          <PhosphorIcon name="warning" size={24} color={theme.colors.onErrorContainer} />
          <Text style={styles.warningContent}>Important: Always backup data before generating sample data or restoring.</Text>
        </Surface>

      </ScrollView>

      <Portal>
        <ActionSheet
          visible={modal.visible}
          onDismiss={closeModal}
          title={modal.title}
          description={modal.description}
          options={modal.options}
          showCancel={!modal.options.some(opt => opt.style === 'cancel')}
        />
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  loadingText: { marginTop: SPACING.md },
  section: { padding: SPACING.lg, marginBottom: SPACING.lg, borderRadius: BORDER_RADIUS.lg },
  sectionTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: '600', marginBottom: SPACING.md },
  description: { marginBottom: SPACING.md, lineHeight: 20 },
  buttonContainer: { flexDirection: 'row', gap: SPACING.md, marginTop: SPACING.sm },
  infoRow: { flexDirection: 'row', justifyContent: 'space-between', paddingVertical: SPACING.sm },
  warningSection: { flexDirection: 'row', padding: SPACING.lg, borderRadius: BORDER_RADIUS.lg, alignItems: 'center' },
  warningContent: { flex: 1, marginLeft: SPACING.md, color: 'white' }, // Assuming onErrorContainer is a light color
});

export default DataScreen;