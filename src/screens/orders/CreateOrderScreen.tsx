import dayjs from 'dayjs';
import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Text, KeyboardAvoidingView, Platform } from 'react-native';
import { Checkbox, Text as PaperText } from 'react-native-paper';

import CustomerSelectionBottomSheet, {
  CustomerSelectionBottomSheetRef,
} from '@/components/bottomsheets/CustomerSelectionBottomSheet';
import ServiceTypeSelectionBottomSheet, {
  ServiceTypeSelectionBottomSheetRef,
} from '@/components/bottomsheets/ServiceSelectionBottomSheet';
import TimePeriodBottomSheet, { TimePeriodBottomSheetRef } from '@/components/bottomsheets/TimePeriodBottomSheet';
import { DataCard } from '@/components/cards';
import ImagePicker from '@/components/forms/ImagePicker';
import Header from '@/components/navigation/Header';
import Button from '@/components/ui/Button';
import TextInput from '@/components/ui/TextInput';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { withPerformanceTracking, usePerformanceTracking } from '@/services/PerformanceMonitoringService';
import { ServiceDetail, Customer } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

// --- Type Definitions ---
interface CreateOrderScreenProps {
  navigation: any;
  route: any;
}

type SelectedTemplate = {
  id: string;
  name: string;
  price: number;
  measurementFields: string[];
};

interface OrderFormErrors {
  customer?: string;
  serviceType?: string;
  dueDate?: string;
  price?: string;
  discount?: string;
}

// --- Component ---
const CreateOrderScreen = ({ navigation, route }: CreateOrderScreenProps) => {
  const theme = useTheme();
  const { actions } = useData();
  const { showSuccess, showError } = useToast();
  const { startTracking, endTracking } = usePerformanceTracking('create_order_operations');
  
  const isEditing = route.params?.isEditing || false;
  const editingOrder = route.params?.order || null;

  // --- State Management ---
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(editingOrder?.customer || null);
  // highlight-start
  // FIX 1: Store dueDate as a dayjs object or null, not a string.
  const [dueDate, setDueDate] = useState<dayjs.Dayjs | null>(
    editingOrder?.dueDate ? dayjs(editingOrder.dueDate) : null
  );
  // highlight-end
  const [selectedTemplates, setSelectedTemplates] = useState<SelectedTemplate[]>([]);
  const [serviceDetails, setserviceDetails] = useState<ServiceDetail[]>(editingOrder?.serviceDetails || []);
  const [discount, setDiscount] = useState({ amount: editingOrder?.discountAmount || 0, type: 'amount' as const });
  const [advancePayment, setAdvancePayment] = useState(editingOrder?.paidAmount?.toString() || '');
  
  const [errors, setErrors] = useState<OrderFormErrors>({});
  const [serviceValidationErrors, setserviceValidationErrors] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  
  // --- Refs and Bottom Sheet Management ---
  const serviceTypeBottomSheetRef = useRef<ServiceTypeSelectionBottomSheetRef>(null);
  const customerSelectionBottomSheetRef = useRef<CustomerSelectionBottomSheetRef>(null);
  const timePeriodBottomSheetRef = useRef<TimePeriodBottomSheetRef>(null);

  const [showserviceSheet, setShowserviceSheet] = useState(false);
  const [showCustomerSheet, setShowCustomerSheet] = useState(false);

  useEffect(() => { if (showserviceSheet) {setTimeout(() => serviceTypeBottomSheetRef.current?.open(), 50)} }, [showserviceSheet]);
  useEffect(() => { if (showCustomerSheet) {setTimeout(() => customerSelectionBottomSheetRef.current?.open(), 50)} }, [showCustomerSheet]);

  // PRODUCTION FIX: Debounced and optimized calculations to prevent UI blocking
  const { subtotal, totalPrice, remainingAmount, serviceTotal, fabricCosts, extraCharges } = useMemo(() => {
    // PRODUCTION FIX: Skip calculations if data is empty to prevent unnecessary work
    if (serviceDetails.length === 0) {
      return { 
        serviceTotal: 0, 
        fabricCosts: 0, 
        extraCharges: 0, 
        subtotal: 0, 
        totalPrice: 0, 
        remainingAmount: 0 
      };
    }
    
    const trackingId = startTracking('price_calculation');
    
    try {
      // PRODUCTION FIX: Use more efficient calculation methods
      let calculatedserviceTotal = 0;
      let calculatedFabricCosts = 0;
      let calculatedExtraCharges = 0;
      
      // Single loop instead of multiple reduce calls for better performance
      for (const detail of serviceDetails) {
        calculatedserviceTotal += detail.totalPrice || 0;
        calculatedExtraCharges += detail.extraCharge || 0;
        
        if (detail.fabricSelection === 'inhouse' && detail.fabricAmount && detail.fabricPrice) {
          calculatedFabricCosts += detail.fabricAmount * detail.fabricPrice;
        }
      }
      
      const calculatedSubtotal = calculatedserviceTotal + calculatedFabricCosts + calculatedExtraCharges;
      const calculatedTotal = Math.max(0, calculatedSubtotal - (discount.amount || 0));
      const advance = parseFloat(advancePayment) || 0;
      const calculatedRemaining = Math.max(0, calculatedTotal - advance);
      
      endTracking(trackingId, 'compute', { 
        serviceCount: serviceDetails.length,
        totalAmount: calculatedTotal 
      });
      
      return { 
        serviceTotal: calculatedserviceTotal, 
        fabricCosts: calculatedFabricCosts, 
        extraCharges: calculatedExtraCharges, 
        subtotal: calculatedSubtotal, 
        totalPrice: calculatedTotal, 
        remainingAmount: calculatedRemaining 
      };
    } catch (error) {
      console.error('Price calculation error:', error);
      endTracking(trackingId, 'compute', { error: true });
      return { 
        serviceTotal: 0, 
        fabricCosts: 0, 
        extraCharges: 0, 
        subtotal: 0, 
        totalPrice: 0, 
        remainingAmount: 0 
      };
    }
  }, [serviceDetails, discount.amount, advancePayment, startTracking, endTracking]);
  
  const handleCustomerSelect = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setErrors(prev => ({...prev, customer: undefined}));
  }, []);

  const handleClearCustomer = useCallback(() => setSelectedCustomer(null), []);

  const handleServiceTypeSelect = useCallback((templates: SelectedTemplate[]) => {
    const newServiceDetails: ServiceDetail[] = templates.map(template => ({
      id: `${Date.now()}-${template.id}`,
      serviceType: template.name,
      templateId: template.id,
      measurements: {},
      fabricSelection: 'customer',
      quantity: 1,
      unitPrice: template.price,
      totalPrice: template.price,
      fabricAmount: 0,
      fabricPrice: 0,
      notes: '',
      images: [],
      isUrgent: false,
      sampleGiven: false,
      extraCharge: 0,
    }));
    
    setserviceDetails(prev => [...prev, ...newServiceDetails]);
    setSelectedTemplates(prev => [...prev, ...templates]);
    setErrors(prev => ({...prev, serviceType: undefined}));
  }, []);
  
  const handleServiceDetailUpdate = useCallback((detailId: string, updates: Partial<ServiceDetail>) => {
    setserviceDetails(prevDetails =>
      prevDetails.map(detail => {
        if (detail.id === detailId) {
          const newDetail = { ...detail, ...updates };
          newDetail.totalPrice = (newDetail.quantity || 1) * (newDetail.unitPrice || 0);
          return newDetail;
        }
        return detail;
      })
    );
  }, []);
  
  const handleMeasurementChange = useCallback((detailId: string, field: string, value: number) => {
    setserviceDetails(prev =>
      prev.map(detail =>
        detail.id === detailId ? { ...detail, measurements: { ...detail.measurements, [field]: value } } : detail
      )
    );
  }, []);
  
  const handleRemoveserviceDetail = useCallback((detailId: string) => {
      const detailToRemove = serviceDetails.find(d => d.id === detailId);
      setserviceDetails(prev => prev.filter(detail => detail.id !== detailId));
      setSelectedTemplates(prev => prev.filter(template => template.id !== detailToRemove?.templateId));
  }, [serviceDetails]);
  
  const validateForm = useCallback(() => {
    const newErrors: OrderFormErrors = {};
    const newserviceErrors: Record<string, any> = {};

    if (!selectedCustomer) {newErrors.customer = 'A customer must be selected.';}
    if (serviceDetails.length === 0) {newErrors.serviceType = 'At least one service type must be added.';}
    if (!dueDate) {newErrors.dueDate = 'A due date is required.';}

    const advance = parseFloat(advancePayment) || 0;
    if (advance > totalPrice) {newErrors.price = 'Advance payment cannot exceed the total price.';}
    if (discount.amount > subtotal) {newErrors.discount = 'Discount cannot exceed the subtotal.';}

    serviceDetails.forEach(detail => {
      const template = selectedTemplates.find(t => t.id === detail.templateId);
      if (template) {
        const detailErrors: any = { measurements: {} };
        template.measurementFields.forEach(field => {
          if (!detail.measurements[field] || detail.measurements[field] <= 0) {
            if(!detailErrors.measurements) {detailErrors.measurements = {};}
            detailErrors.measurements[field] = 'Required';
          }
        });
        if (Object.keys(detailErrors.measurements).length > 0) {
           newserviceErrors[detail.id] = detailErrors;
        }
      }
    });

    setErrors(newErrors);
    setserviceValidationErrors(newserviceErrors);
    return Object.keys(newErrors).length === 0 && Object.keys(newserviceErrors).length === 0;
  }, [selectedCustomer, serviceDetails, dueDate, advancePayment, totalPrice, subtotal, discount.amount, selectedTemplates]);
  
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      showError("Please fix the errors before saving.");
      return;
    }
    setIsSubmitting(true);

    try {
      const orderData = {
        customerName: selectedCustomer!.name,
        customer: selectedCustomer,
        email: selectedCustomer!.email,
        phone: selectedCustomer!.phone,
        date: new Date().toISOString(),
        // highlight-start
        // FIX 2: Format the dueDate object to a string for submission
        dueDate: dueDate ? dueDate.format('YYYY-MM-DD') : new Date().toISOString(),
        // highlight-end
        status: 'pending' as const,
        orderType: 'custom' as const,
        total: totalPrice,
        paidAmount: parseFloat(advancePayment) || 0,
        subtotal,
        discountAmount: discount.amount,
        items: serviceDetails.map(d => ({ 
            productId: d.templateId, 
            productName: d.serviceType, 
            quantity: d.quantity, 
            unitPrice: d.unitPrice 
        })),
        serviceDetails,
        notes: serviceDetails.map(d => d.notes).filter(Boolean).join('; '),
      };

      if (isEditing) {
        await actions.updateOrder({ ...editingOrder, ...orderData });
        showSuccess('Order updated successfully!');
        navigation.navigate('OrderDetails', { order: { ...editingOrder, ...orderData } });
      } else {
        const created = await actions.addOrder(orderData as any);
        showSuccess('Order created successfully!');
        navigation.navigate('OrderSuccess', { order: created, customer: selectedCustomer });
      }

    } catch (error) {
      LoggingService.error('Error saving order', 'CREATE_ORDER_SCREEN', error as Error);
      showError('Failed to save order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, selectedCustomer, dueDate, totalPrice, advancePayment, subtotal, discount.amount, serviceDetails, isEditing, editingOrder, actions, showError, showSuccess, navigation]);
  
  const styles = useMemo(() => createStyles(theme), [theme]);

  // --- Render Method ---
  return (
    <View style={styles.container}>
      <Header
        title={isEditing ? 'Edit Order' : 'Create Order'}
        onBackPress={() => navigation.goBack()}
        showBack={true}
      />
      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">
          
          {/* --- Customer and service Sections (Unchanged) --- */}
          {!selectedCustomer ? (
            <TouchableOpacity onPress={() => setShowCustomerSheet(true)}>
              <TextInput label="Select Customer *" value="" editable={false} rightIcon="chevron-down" onChangeText={()=>{}} error={!!errors.customer} />
            </TouchableOpacity>
          ) : (
             <View style={styles.customerContainer}>
                <DataCard id={selectedCustomer.id} name={selectedCustomer.name} phone={selectedCustomer.phone} cardType='customer' onPress={() => {}} onCheckboxPress={() => {}} />
                <TouchableOpacity onPress={handleClearCustomer} style={styles.clearCustomerButton}>
                  <PhosphorIcon name='x' size={20} color={theme.colors.onSurfaceVariant} />
                </TouchableOpacity>
            </View>
          )}
          {errors.customer && <Text style={styles.errorText}>{errors.customer}</Text>}

          <TouchableOpacity onPress={() => setShowserviceSheet(true)}>
            <TextInput label="Select service Type *" value={serviceDetails.map(g => g.serviceType).join(', ')} editable={false} rightIcon="plus" onChangeText={()=>{}} error={!!errors.serviceType} />
          </TouchableOpacity>
          {errors.serviceType && <Text style={styles.errorText}>{errors.serviceType}</Text>}

          {serviceDetails.map(detail => {
            const template = selectedTemplates.find(t => t.id === detail.templateId);
            const measurementFields = template?.measurementFields || [];
            const isExpanded = expandedSections[detail.id] !== false;
            const validationErrors = serviceValidationErrors[detail.id] || {};
            const hasImage = detail.images && detail.images.length > 0;

            return (
              <View key={detail.id} style={[styles.serviceSection, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.serviceHeader}>
                  <TouchableOpacity style={styles.serviceHeaderLeft} onPress={() => setExpandedSections(prev => ({ ...prev, [detail.id]: !isExpanded }))}>
                    <PhosphorIcon name={isExpanded ? 'caret-down' : 'caret-right'} size={20} color={theme.colors.onSurface}/>
                    <Text style={styles.serviceTitle}>{detail.serviceType}</Text>
                  </TouchableOpacity>
                  <View style={styles.serviceHeaderRight}>
                    <View style={styles.quantityControl}>
                      <TouchableOpacity style={styles.quantityButton} onPress={() => handleServiceDetailUpdate(detail.id, { quantity: Math.max(1, detail.quantity - 1) })}>
                        <PhosphorIcon name='minus' size={16} color={theme.colors.onSurface} />
                      </TouchableOpacity>
                      <Text style={styles.quantityText}>{detail.quantity}</Text>
                      <TouchableOpacity style={styles.quantityButton} onPress={() => handleServiceDetailUpdate(detail.id, { quantity: detail.quantity + 1 })}>
                        <PhosphorIcon name='plus' size={16} color={theme.colors.onSurface} />
                      </TouchableOpacity>
                      <TouchableOpacity style={styles.deleteButton} onPress={() => handleRemoveserviceDetail(detail.id)}>
                        <PhosphorIcon name='trash' size={20} color={theme.colors.error} />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>

                {isExpanded && (
                  <View style={styles.serviceContent}>
                    <View style={styles.statusSection}>
                      <View style={styles.checkboxRow}>
                        <Checkbox status={detail.isUrgent ? 'checked' : 'unchecked'} onPress={() => handleServiceDetailUpdate(detail.id, { isUrgent: !detail.isUrgent })} color={theme.colors.primary} />
                        <Text style={styles.checkboxLabel}>Urgent Order</Text>
                      </View>
                      <View style={styles.checkboxRow}>
                        <Checkbox status={detail.sampleGiven ? 'checked' : 'unchecked'} onPress={() => handleServiceDetailUpdate(detail.id, { sampleGiven: !detail.sampleGiven })} color={theme.colors.primary} />
                        <Text style={styles.checkboxLabel}>Sample Given</Text>
                      </View>
                    </View>

                    {detail.isUrgent && ( <TextInput label='Extra Charge' value={detail.extraCharge?.toString() || ''} onChangeText={text => handleServiceDetailUpdate(detail.id, { extraCharge: parseFloat(text) || 0 })} type='number'/> )}
                    
                    <Text style={styles.subSectionTitle}>Measurements</Text>
                    <View style={styles.measurementGrid}>
                      {measurementFields.map(field => (
                        <View key={field} style={styles.measurementField}>
                          <TextInput label={`${field} (cm)`} value={detail.measurements[field]?.toString() || ''}
                            onChangeText={text => handleMeasurementChange(detail.id, field, parseFloat(text) || 0)} 
                            keyboardType="numeric"
                            error={validationErrors.measurements?.[field]}
                          />
                        </View>
                      ))}
                    </View>
                    
                    <Text style={styles.subSectionTitle}>Fabric Source</Text>
                    <View style={styles.radioGroup}>
                        <TouchableOpacity style={styles.radioOption} onPress={() => handleServiceDetailUpdate(detail.id, { fabricSelection: 'inhouse' })}>
                            <View style={[styles.radioButton, {borderColor: theme.colors.outline}]}>{detail.fabricSelection === 'inhouse' && <View style={[styles.radioButtonInner, { backgroundColor: theme.colors.primary }]} />}</View>
                            <Text style={styles.radioLabel}>In-house</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.radioOption} onPress={() => handleServiceDetailUpdate(detail.id, { fabricSelection: 'customer' })}>
                            <View style={[styles.radioButton, {borderColor: theme.colors.outline}]}>{detail.fabricSelection === 'customer' && <View style={[styles.radioButtonInner, { backgroundColor: theme.colors.primary }]} />}</View>
                            <Text style={styles.radioLabel}>Customer</Text>
                        </TouchableOpacity>
                    </View>
                    {detail.fabricSelection === 'inhouse' && (
                        <View style={styles.row}>
                            <TextInput label='Amount (m)' value={detail.fabricAmount?.toString() || ''} onChangeText={text => handleServiceDetailUpdate(detail.id, { fabricAmount: parseFloat(text) || 0})} type='number' style={styles.halfWidth} />
                            <TextInput label='Price/m' value={detail.fabricPrice?.toString() || ''} onChangeText={text => handleServiceDetailUpdate(detail.id, { fabricPrice: parseFloat(text) || 0})} type='number' style={styles.halfWidth} />
                        </View>
                    )}
                    
                    <View style={styles.notesAndImageRow}>
                      <View style={styles.notesContainer}>
                        <TextInput label='Notes & Instructions' value={detail.notes} onChangeText={notes => handleServiceDetailUpdate(detail.id, { notes })} type='textarea' multiline style={styles.notesInput} />
                      </View>
                      <View style={styles.imageContainer}>
                        {!hasImage ? (
                           <ImagePicker currentImage={null} size='small' placeholder='' onImageSelected={(uri) => { if (uri) {handleServiceDetailUpdate(detail.id, { images: [uri] });} }} />
                        ) : (
                          <View>
                            <ImagePicker currentImage={detail.images[0]} onImageSelected={() => {}} size='small' />
                            <TouchableOpacity style={styles.imageRemoveButton} onPress={() => handleServiceDetailUpdate(detail.id, { images: [] })}>
                              <PhosphorIcon name='x' size={16} color={theme.colors.error} />
                            </TouchableOpacity>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                )}
              </View>
            );
          })}
          
          <TouchableOpacity onPress={() => timePeriodBottomSheetRef.current?.open()} activeOpacity={0.7}>
            <View pointerEvents="none">
              <TextInput 
                label='Due Date *' 
                // highlight-start
                // FIX 3: Format the date object for display
                value={dueDate ? dueDate.format('MMMM D, YYYY') : ''} 
                // highlight-end
                editable={false} 
                rightIcon='calendar'
                onChangeText={() => {}} 
                error={!!errors.dueDate} 
              />
            </View>
          </TouchableOpacity>
          {errors.dueDate && <Text style={styles.errorText}>{errors.dueDate}</Text>}
          
          {/* --- Price and Summary Sections (Unchanged) --- */}
          <PaperText variant='titleMedium' style={styles.sectionTitle}>Price & Payment</PaperText>
          <View style={styles.row}>
            <TextInput label='Advance' value={advancePayment} onChangeText={setAdvancePayment} style={styles.halfWidth} type='number' />
            <TextInput label='Discount Amount' value={discount.amount === 0 ? '' : discount.amount.toString()} onChangeText={text => setDiscount(prev => ({ ...prev, amount: parseFloat(text) || 0 }))} style={styles.halfWidth} type='number' error={!!errors.discount} />
          </View>
          {errors.price && <Text style={styles.errorText}>{errors.price}</Text>}
          {errors.discount && <Text style={styles.errorText}>{errors.discount}</Text>}
          
          <View style={[styles.priceSummaryContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={styles.priceSummaryTitle}>Order Summary</Text>
            <View style={styles.summaryRow}><Text style={styles.summaryLabel}>service Total:</Text><Text style={styles.summaryValue}>{formatCurrency(serviceTotal)}</Text></View>
            {fabricCosts > 0 && (
                <View style={styles.summaryRow}><Text style={styles.summaryLabel}>Fabric Costs:</Text><Text style={styles.summaryValue}>{formatCurrency(fabricCosts)}</Text></View>
            )}
            {extraCharges > 0 && <View style={styles.summaryRow}><Text style={styles.summaryLabel}>Extra Charges:</Text><Text style={styles.summaryValue}>{formatCurrency(extraCharges)}</Text></View>}
            <View style={styles.summaryRow}><Text style={styles.summaryLabel}>Subtotal:</Text><Text style={styles.summaryValue}>{formatCurrency(subtotal)}</Text></View>
            {discount.amount > 0 && <View style={styles.summaryRow}><Text style={[styles.summaryLabel, {color: theme.colors.tertiary}]}>Discount:</Text><Text style={[styles.summaryValue, {color: theme.colors.tertiary}]}>-{formatCurrency(discount.amount)}</Text></View>}
            <View style={[styles.summaryDivider, { backgroundColor: theme.colors.outline }]} />
            <View style={styles.summaryRow}><Text style={styles.totalLabel}>Total Amount:</Text><Text style={[styles.totalValue, { color: theme.colors.primary }]}>{formatCurrency(totalPrice)}</Text></View>
            {(parseFloat(advancePayment) || 0) > 0 && (
                <>
                    <View style={styles.summaryRow}><Text style={styles.summaryLabel}>Advance Payment:</Text><Text style={styles.summaryValue}>{formatCurrency(parseFloat(advancePayment) || 0)}</Text></View>
                    <View style={styles.summaryRow}><Text style={styles.remainingLabel}>Remaining Amount:</Text><Text style={[styles.remainingValue, { color: theme.colors.tertiary }]}>{formatCurrency(remainingAmount)}</Text></View>
                </>
            )}
          </View>
          
          <View style={styles.buttonContainer}>
            <Button variant='outline' onPress={() => navigation.goBack()} style={styles.button}>Cancel</Button>
            <Button variant='primary' onPress={handleSubmit} style={styles.button} loading={isSubmitting} disabled={isSubmitting}>
              {isEditing ? 'Update Order' : 'Create Order'}
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      
      {/* --- Bottom Sheets --- */}
      {showCustomerSheet && <CustomerSelectionBottomSheet ref={customerSelectionBottomSheetRef} onSelect={handleCustomerSelect} onClose={() => setShowCustomerSheet(false)} />}
      {showserviceSheet && <ServiceTypeSelectionBottomSheet ref={serviceTypeBottomSheetRef} onSelect={handleServiceTypeSelect} onClose={() => setShowserviceSheet(false)} />}
      <TimePeriodBottomSheet
        ref={timePeriodBottomSheetRef}
        mode="single"
        onDateSelect={(date: Date | null) => {
          if (date) {
            setDueDate(dayjs(date)); // Convert Date to dayjs.Dayjs
            setErrors(prev => ({...prev, dueDate: undefined}));
          } else {
            setDueDate(null);
          }
        }}
        onApply={() => {}} // Dummy onApply to satisfy type requirement
        selectedDate={dueDate ? dueDate.toDate() : undefined} // Convert dayjs.Dayjs to Date
      />
    </View>
  );
};

// --- (Styles are correct and unchanged) ---
const createStyles = (theme: ReturnType<typeof useTheme>) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.colors.background },
  content: { flex: 1, paddingHorizontal: 16 },
  errorText: { color: theme.colors.error, fontSize: 12, marginTop: -8, marginBottom: 8, paddingLeft: 8 },
  row: { flexDirection: 'row', gap: 12 },
  halfWidth: { flex: 1 },
  customerContainer: { position: 'relative', marginVertical: 6 },
  clearCustomerButton: { position: 'absolute', right: 8, top: 8, zIndex: 1, padding: 8, backgroundColor: theme.colors.surface, borderRadius: 16 },
  serviceSection: { marginVertical: 8, borderRadius: 12, borderWidth: 1, borderColor: theme.colors.outlineVariant, overflow: 'hidden' },
  serviceHeader: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16, paddingVertical: 12, backgroundColor: theme.colors.surfaceVariant },
  serviceHeaderLeft: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  serviceHeaderRight: { flexDirection: 'row', alignItems: 'center' },
  serviceTitle: { fontSize: 16, fontWeight: '600', marginLeft: 8, color: theme.colors.onSurface, flex: 1 },
  deleteButton: { padding: 4, marginLeft: 12 },
  serviceContent: { padding: 16, borderTopWidth: 1, borderTopColor: theme.colors.outlineVariant, gap: 16 },
  quantityControl: { flexDirection: 'row', alignItems: 'center', gap: 8 },
  quantityButton: { width: 28, height: 28, borderRadius: 14, justifyContent: 'center', alignItems: 'center', borderWidth: 1, borderColor: theme.colors.outline },
  quantityText: { fontSize: 16, fontWeight: '600', minWidth: 24, textAlign: 'center' },
  statusSection: { flexDirection: 'row', justifyContent: 'space-around' },
  checkboxRow: { flexDirection: 'row', alignItems: 'center' },
  checkboxLabel: { fontSize: 14, marginLeft: 4, color: theme.colors.onSurface },
  subSectionTitle: { fontWeight: '600', color: theme.colors.onSurfaceVariant, fontSize: 14, marginBottom: 8 },
  measurementGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', rowGap: 12 },
  measurementField: { width: '48%' },
  radioGroup: { flexDirection: 'row', gap: 24 },
  radioOption: { flexDirection: 'row', alignItems: 'center', paddingVertical: 4 },
  radioButton: { width: 20, height: 20, borderRadius: 10, borderWidth: 2, marginRight: 8, justifyContent: 'center', alignItems: 'center' },
  radioButtonInner: { width: 10, height: 10, borderRadius: 5 },
  radioLabel: { fontSize: 14, fontWeight: '500', color: theme.colors.onSurface },
  
  notesAndImageRow: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'flex-start',
  },
  notesContainer: {
    flex: 2,
  },
  notesInput: {
    height: 110, 
    textAlignVertical: 'top',
  },
  imageContainer: {
    flex: 1,
  },
  imageRemoveButton: { 
    position: 'absolute', 
    top: -8, 
    right: -8, 
    backgroundColor: theme.colors.surface, 
    borderRadius: 12, 
    width: 24, 
    height: 24, 
    justifyContent: 'center', 
    alignItems: 'center', 
    borderWidth: 1, 
    borderColor: theme.colors.outlineVariant 
  },

  sectionTitle: { fontWeight: '600', marginBottom: 12, marginTop: 16, color: theme.colors.onSurface, fontSize: 18 },
  priceSummaryContainer: { borderRadius: 12, padding: 16, marginVertical: 16, borderWidth: 1, borderColor: theme.colors.outlineVariant },
  priceSummaryTitle: { fontSize: 16, fontWeight: '600', marginBottom: 12, textAlign: 'center', color: theme.colors.onSurface },
  summaryRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 },
  summaryLabel: { fontSize: 14, color: theme.colors.onSurfaceVariant, fontWeight: '500' },
  summaryValue: { fontSize: 16, fontWeight: '600', color: theme.colors.onSurface },
  totalLabel: { fontSize: 16, color: theme.colors.onSurface, fontWeight: '700' },
  totalValue: { fontSize: 18, fontWeight: '700' },
  remainingLabel: { fontSize: 14, color: theme.colors.onSurfaceVariant, fontWeight: '500' },
  remainingValue: { fontSize: 16, fontWeight: '700' },
  summaryDivider: { height: 1, marginVertical: 12 },
  buttonContainer: { flexDirection: 'row', gap: 12, marginTop: 16, marginBottom: 32, paddingHorizontal: 4 },
  button: { flex: 1 },
});

// PRODUCTION FIX: Add component cleanup
const WrappedCreateOrderScreen = withPerformanceTracking(CreateOrderScreen, 'CreateOrderScreen');

const CreateOrderScreenWithCleanup: React.FC<CreateOrderScreenProps> = (props) => {
  useEffect(() => {
    return () => {
      // Clean up any remaining timeouts or intervals on unmount
      console.log('CreateOrderScreen: Component unmounting, cleaning up resources');
    };
  }, []);
  
  return <WrappedCreateOrderScreen {...props} />;
};

export default CreateOrderScreenWithCleanup;