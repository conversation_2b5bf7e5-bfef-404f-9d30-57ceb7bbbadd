// This file has been refactored to align with the project's structure and conventions.
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Linking,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Surface,
  Divider,
  Menu,
  ActivityIndicator,
  Chip,
  IconButton,
  TextInput,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useAuth } from '../../context/AuthContext'; // Import useAuth
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import LoggingService from '../../services/LoggingService';
import QRCodeService, { EntityType } from '../../services/QRCodeService'; // Import EntityType
import { RootStackParamList } from '../../types/navigation';
import { Order, OrderStatus, TimelineEvent } from '../../types/order';
import { formatCurrency } from '../../utils/currency';
import { PDFInvoiceGenerator } from '../../utils/pdfInvoiceGenerator';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';



type OrderDetailsScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'OrderDetails'
>;

type OrderDetailsScreenRouteProp = RouteProp<
  RootStackParamList,
  'OrderDetails'
>;

interface Props {
  navigation: OrderDetailsScreenNavigationProp;
  route: OrderDetailsScreenRouteProp;
}


const OrderDetailsScreen = ({ navigation, route }: Props) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.lg,
    },
    scrollView: {
      flex: 1,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        fontSize: 18,
    },
    topCard: {
        margin: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
    },
    topCardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: theme.spacing.lg,
    },
    orderInfoSection: {
        flex: 1,
    },
    orderTitleRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    orderNumber: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.semibold,
    },
    statusDropdown: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: theme.spacing.xxs,
        paddingHorizontal: theme.spacing.xs,
        borderRadius: theme.borderRadius.sm,
        borderWidth: 1,
    },
    statusText: {
        marginRight: theme.spacing.xxs,
    },
    itemCount: {
        fontSize: theme.typography.fontSize.md,
    },
    qrCodeContainer: {
        marginLeft: theme.spacing.lg,
    },
    topCardBottom: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        padding: theme.spacing.lg,
        borderTopWidth: 1,
        borderColor: theme.colors.outlineVariant,
    },
    dateTextSmall: {
        fontSize: theme.typography.fontSize.sm,
    },
    totalAmount: {
        fontSize: theme.typography.fontSize.sm,
        fontWeight: theme.typography.fontWeight.semibold,
    },
    dueAmount: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.normal,
        textAlign: 'right',
    },
    orderItemsSection: {
        marginHorizontal: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
        padding: theme.spacing.lg,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.md,
    },
    sectionTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.semibold,
    },
    totalItemsText: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
    },
    itemsList: {},
    orderItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: theme.spacing.xs,
    },
    itemName: {
        fontSize: theme.typography.fontSize.md,
    },
    assignedText: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.onSurfaceVariant,
    },
    assignButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    assignButtonText: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.onSurfaceVariant,
        marginRight: theme.spacing.xxs,
    },
    progressSection: {
        marginHorizontal: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
        padding: theme.spacing.lg,
    },
    progressHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.md,
    },
    progressCompletedText: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
    },
    progressItems: {},
    progressItem: {
        marginBottom: theme.spacing.xs,
    },
    progressItemInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    progressItemName: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.normal,
    },
    progressItemStatus: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
    },
    progressBarContainer: {
        marginTop: theme.spacing.xs,
    },
    progressBar: {
        height: theme.spacing.xs,
        borderRadius: theme.borderRadius.sm,
    },
    progressBarFill: {
        height: '100%',
        borderRadius: theme.borderRadius.sm,
    },
    customerSection: {
        marginHorizontal: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
    },
    customerContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: theme.spacing.lg,
    },
    customerMainInfo: {},
    customerNameRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    customerName: {
        fontSize: theme.typography.fontSize.xs,
        fontWeight: theme.typography.fontWeight.semibold,
    },
    vipBadge: {
        marginLeft: theme.spacing.xs,
        backgroundColor: theme.colors.primary,
        borderRadius: theme.borderRadius.xs,
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xxs,
    },
    vipBadgeText: {
        color: theme.colors.onPrimary,
        fontSize: theme.typography.fontSize.sm,
        fontWeight: theme.typography.fontWeight.bold,
    },
    customerPhone: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.normal,
        marginTop: theme.spacing.xxs,
    },
    activeOrdersText: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
        marginTop: theme.spacing.xxs,
    },
    customerActions: {},
    callIcon: {
        borderRadius: theme.borderRadius.xl,
    },
    paymentSection: {
        marginHorizontal: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
        padding: theme.spacing.lg,
    },
    paymentSectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.md,
    },
    paymentDetails: {
        marginBottom: theme.spacing.lg,
    },
    paymentRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: theme.spacing.xs,
    },
    paymentLabel: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.normal,
    },
    paymentValue: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.normal,
    },
    paymentActions: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    paymentInputContainer: {
        flex: 1,
        marginRight: theme.spacing.xs,
    },
    paymentInput: {},
    takePaymentButton: {
        backgroundColor: theme.colors.primary,
        paddingVertical: theme.spacing.md,
        paddingHorizontal: theme.spacing.lg,
        borderRadius: theme.borderRadius.sm,
    },
    takePaymentButtonText: {
        color: theme.colors.onPrimary,
        fontWeight: theme.typography.fontWeight.bold,
    },
    orderSummarySection: {
        marginHorizontal: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
        padding: theme.spacing.lg,
    },
    summaryDetails: {},
    summaryCategory: {
        marginBottom: theme.spacing.md,
    },
    categoryTitle: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.semibold,
        marginBottom: theme.spacing.xs,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: theme.spacing.xxs,
    },
    summaryLabel: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
    },
    summaryValue: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
    },
    totalRow: {
        marginTop: theme.spacing.xs,
        paddingTop: theme.spacing.xs,
        borderTopWidth: 1,
        borderColor: theme.colors.outlineVariant,
    },
    timelineSection: {
        marginHorizontal: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        borderRadius: theme.borderRadius.md,
        padding: theme.spacing.lg,
    },
    timelineHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing.md,
    },
    timelineContent: {},
    timelineItem: {
        flexDirection: 'row',
        marginBottom: theme.spacing.lg,
    },
    timelineDot: {
        width: theme.spacing.md,
        height: theme.spacing.md,
        borderRadius: theme.borderRadius.md,
        marginRight: theme.spacing.md,
        marginTop: theme.spacing.xxs,
    },
    timelineItemContent: {},
    timelineTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.normal,
    },
    timelineDescription: {
        fontSize: theme.typography.fontSize.md,
        fontWeight: theme.typography.fontWeight.normal,
    },
    timelineMetadata: {
        flexDirection: 'row',
        marginTop: theme.spacing.xxs,
    },
    timelineDate: {
        fontSize: theme.typography.fontSize.sm,
        fontWeight: theme.typography.fontWeight.normal,
    },
    timelineUser: {
        fontSize: theme.typography.fontSize.sm,
        fontWeight: theme.typography.fontWeight.normal,
        marginLeft: theme.spacing.xs,
    },
    bottomActions: {
        flexDirection: 'row',
        padding: theme.spacing.lg,
    },
    shareButton: {
        flex: 1,
        backgroundColor: theme.colors.secondary,
        padding: theme.spacing.lg,
        borderRadius: theme.borderRadius.sm,
        alignItems: 'center',
        marginRight: theme.spacing.xs,
    },
    shareButtonText: {
        color: theme.colors.onSecondary,
        fontWeight: theme.typography.fontWeight.bold,
    },
    printButton: {
        flex: 1,
        backgroundColor: theme.colors.tertiary,
        padding: theme.spacing.lg,
        borderRadius: theme.borderRadius.sm,
        alignItems: 'center',
        marginLeft: theme.spacing.xs,
    },
    printButtonText: {
        color: theme.colors.onTertiary,
        fontWeight: theme.typography.fontWeight.bold,
    },
    fab: {
        position: 'absolute',
        margin: theme.spacing.lg,
        right: 0,
        bottom: 0,
    },
    fullBottomSheetContainer: {
        flex: 1,
        marginTop: insets.top + theme.spacing.xxl,
        borderTopLeftRadius: theme.borderRadius.lg,
        borderTopRightRadius: theme.borderRadius.lg,
    },
    bottomSheetHeader: {
        padding: theme.spacing.lg,
        borderBottomWidth: 1,
        borderColor: theme.colors.outlineVariant,
    },
    bottomSheetTitle: {
        fontSize: theme.typography.fontSize.xs,
        fontWeight: theme.typography.fontWeight.semibold,
    },
    bottomSheetContent: {
        padding: theme.spacing.lg,
    },
    bottomSheetFooter: {
        padding: theme.spacing.lg,
    },
    closeButton: {},
    closeButtonContent: {},
  }), [theme, insets]);

  const QRCodeComponent = ({ value, size = 120 }: { value: string; size?: number }) => {
    try {
      const QRCode = require('react-native-qrcode-svg').default;
      return (
        <QRCode value={value} size={size} color={theme.colors.onSurface} backgroundColor={theme.colors.surface} />
      );
    } catch (error) {
      return (
        <View style={{
          width: size,
          height: size,
          backgroundColor: theme.colors.surfaceVariant, // Using theme color
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: theme.borderRadius.md,
          borderWidth: 2,
          borderColor: theme.colors.outlineVariant,
          borderStyle: 'dashed'
        }}>
          <PhosphorIcon name="grid-four" size={size * 0.4} color={theme.colors.onSurfaceVariant} />
          <Text style={{ fontSize: theme.typography.fontSize.sm, fontWeight: theme.typography.fontWeight.normal, color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: theme.spacing.xxs }}>{"QR Code\n(Build required)"}</Text>
        </View>
      );
    }
  };

  const { showToast, showError, showSuccess } = useToast();
  const { state, actions } = useData();
  const { isAdmin } = useAuth(); // Use the useAuth hook
  const { updateOrderStatus, deleteOrder, updateOrder } = actions || ({} as any);
  const [loading, setLoading] = useState(false);
  const [order, setOrder] = useState<Order | undefined>(route?.params?.order);
  const [statusMenuVisible, setStatusMenuVisible] = useState<string | null>(null);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [actionSheetType, setActionSheetType] = useState<'delete' | 'duplicate' | null>(null);

  useEffect(() => {
    if (route?.params?.order) {
      setOrder(route.params.order);
    }
  }, [route?.params?.order]);

  // Keep local order in sync with global state updates
  useEffect(() => {
    if (!order?.id) {return;}
    const latest = (state.orders || []).find((o: any) => o.id === order.id);
    if (latest) {setOrder(prev => ({ ...(prev || {}), ...latest } as any));}
  }, [state.orders, order?.id]);

  const getStatusColor = (status?: OrderStatus): string => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'delivered':
        return theme?.colors?.success;
      case 'in progress':
      case 'in-progress':
      case 'cutting':
      case 'stitching':
      case 'fittings':
        return theme?.colors?.info;
      case 'ready':
        return theme?.colors?.info;
      case 'pending':
        return theme?.colors?.warning;
      case 'cancelled':
        return theme?.colors?.error;
      case 'on_hold':
        return theme.colors.onSurfaceVariant;
      default:
        return theme?.colors?.outline;
    }
  };

  const handleStatusUpdate = async (newStatus: OrderStatus) => {
    if (!order) {return;}
    try {
      setLoading(true);
      if (!updateOrderStatus) {throw new Error('updateOrderStatus function is not available');}
      await updateOrderStatus(order.id, newStatus);
      // Update all items to the same status for consistency if needed
      const updatedItems = (order.items || []).map(i => ({ ...i, status: newStatus }));
      const updated = { ...order, status: newStatus, items: updatedItems } as Order;
      setOrder(updated);
      if (updateOrder) {await updateOrder(updated as any);}
      showToast(`Order status updated to ${newStatus}`);
    } catch (error: any) {
      LoggingService.error('Failed to update order status', 'ORDER_DETAILS', error);
      showError(`Error: Failed to update order status: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    if (!order) {return;}
    navigation.navigate('CreateOrder', {
      mode: 'edit',
      order,
      orderId: order.id,
    });
  };

  const handleCallCustomer = () => {
    const phone = order?.phone || order?.customer_phone || (typeof order?.customer === 'object' ? order?.customer?.phone : undefined);
    if (phone) {Linking.openURL(`tel:${phone}`);}
  };



  const formatDate = (dateString?: string) => {
    if (!dateString) {return 'Not set';}
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTimelineDateTime = (dateString?: string) => {
    if (!dateString) {return 'Not set';}
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {return 'Invalid date';}
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear().toString().slice(-2);
    const time = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).toLowerCase();
    return `${day} ${month} ${year} at ${time}`;
  };

  const calculateBalance = (): number => {
    if (!order) {return 0;}
    const total = order.total ?? order.amount ?? 0;
    const paid = order.paidAmount ?? 0;
    return Math.max(0, total - paid);
  };

  const getCustomerActiveOrders = (): number => {
    if (!state.orders || !order?.customerId) {return 0;}
    return state.orders.filter(o =>
      (o.customerId === order.customerId || o.customerName === order.customerName) &&
      o.status !== 'Completed' &&
      o.status !== 'Delivered' &&
      o.status !== 'Cancelled'
    ).length;
  };

  const getPaymentStatusColor = (order: Order): string => {
    const balance = calculateBalance();
    if (balance <= 0) {return '#10b981';} // Paid
    if (balance >= (order.total || order.amount || 0)) {return theme?.colors?.error;} // Unpaid
    return theme?.colors?.warning; // Partial - Using theme color
  };

  const getPaymentStatusText = (order: Order): string => {
    const balance = calculateBalance();
    if (balance <= 0) {return 'Paid';}
    if (balance >= (order.total || order.amount || 0)) {return 'Unpaid';}
    return 'Due';
  };

  const getCompletedItemsCount = (): number => {
    if (!order || !order.items || order.items.length === 0) {return 0;}
    return order.items.filter(item =>
      item.status?.toLowerCase() === 'completed' ||
      item.status?.toLowerCase() === 'delivered'
    ).length;
  };

  const generateTimelineEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [];
    if (!order) {return events;}

    events.push({
      id: 'order_created',
      type: 'order_created',
      title: 'Order created',
      description: `Order #${order.id?.slice(-3) || '002'} created with ${order.items?.length || 0} items`,
      timestamp: order.createdAt || order.created_at || order.date,
      user: 'Admin',
      color: theme?.colors?.primary
    });

    if (order.payments && Array.isArray(order.payments)) {
      order.payments.forEach((payment, index) => {
        events.push({
          id: `payment_${payment.id || index}`,
          type: 'payment',
          title: `Payment received`,
          description: `${formatCurrency(payment.amount)} received via ${payment.method || 'Cash'}${payment.reference ? ` (Ref: ${payment.reference})` : ''}`,
          timestamp: payment.date || payment.timestamp,
          user: payment.user || 'Admin',
          color: '#10b981'
        });
      });
    } else if (order.paidAmount && order.paidAmount > 0) {
      events.push({
        id: 'payment_fallback',
        type: 'payment',
        title: 'Payment received',
        description: `${formatCurrency(order.paidAmount)} received`,
        timestamp: order.paymentDate || order.updatedAt || order.date,
        user: 'Admin',
        color: '#10b981'
      });
    }

    return events.sort((a, b) => new Date(b.timestamp!).getTime() - new Date(a.timestamp!).getTime());
  };

  const renderTimelineEvents = () => {
    const events = generateTimelineEvents();
    const recentEvents = events.slice(0, 3);

    return recentEvents.map((event) => (
      <View key={event.id} style={styles.timelineItem}>
        <View style={[styles.timelineDot, { backgroundColor: event.color }]} />
        <View style={styles.timelineItemContent}>
          <Text style={[styles.timelineTitle, { color: theme?.colors?.onSurface }]}>{event.title}</Text>
          <Text style={[styles.timelineDescription, { color: theme?.colors?.onSurfaceVariant }]}>{event.description}</Text>
          <View style={styles.timelineMetadata}>
            <Text style={[styles.timelineDate, { color: theme?.colors?.onSurfaceVariant }]}>{formatTimelineDateTime(event.timestamp)}</Text>
            <Text style={[styles.timelineUser, { color: theme?.colors?.onSurfaceVariant }]}>by {event.user}</Text>
          </View>
        </View>
      </View>
    ));
  };

  const handleViewFullTimeline = () => {
    showToast('Full timeline view is not implemented yet.');
  };

  const handleTakePayment = async () => {
    if (!order) {return;}
    if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
      showError('Please enter a valid payment amount');
      return;
    }
    const amount = parseFloat(paymentAmount);
    const currentPaid = order.paidAmount || 0;
    const total = order.total || order.amount || 0;
    if (currentPaid + amount > total) {
      showError('Payment amount exceeds the remaining amount');
      return;
    }
    const updatedOrder: Order = {
      ...order,
      paidAmount: currentPaid + amount,
      balanceAmount: total - (currentPaid + amount),
      updatedAt: new Date().toISOString(),
    };
    try {
      if (!updateOrder) {throw new Error('updateOrder action not available');}
      await updateOrder(updatedOrder as any);
      setOrder(updatedOrder);
      setPaymentAmount('');
      showSuccess(`Payment of ${formatCurrency(amount)} received`);
    } catch (error: any) {
      LoggingService.error('Failed to save payment update', 'ORDER_DETAILS', error);
      showError('Failed to record payment');
    }
  };

  const handleCustomerPress = () => {
    if (!order || !order.customerId) {return;}
    navigation.navigate('CustomerDetails', { customerId: order.customerId });
  };

  const getOrderProgress = (): number => {
    if (!order) {return 0;}
    if (order.status?.toLowerCase() === 'delivered') {return 100;}
    if (!order.items || order.items.length === 0) {return 0;}
    const statusWeights: { [key: string]: number } = { 'pending': 0, 'cutting': 20, 'stitching': 40, 'in-progress': 50, 'fitting': 70, 'finishing': 85, 'ready': 95, 'completed': 100, 'delivered': 100 };
    const totalProgress = order.items.reduce((sum, item) => {
      const status = item.status?.toLowerCase() || 'pending';
      return sum + (statusWeights[status] || 0);
    }, 0);
    return totalProgress / order.items.length;
  };

  const handlePrintInvoice = async () => {
    if (!order) {return;}
    try {
      const printableOrder = { ...order, createdAt: order.createdAt || new Date().toISOString() };
      await PDFInvoiceGenerator.printInvoice(printableOrder as any);
    } catch (error: any) {
      showError(`Failed to print invoice: ${error.message}`);
    }
  };

  const handlePrintReceipt = async () => {
    if (!order) {return;}
    try {
      await PDFInvoiceGenerator.printInvoice({ ...order, createdAt: order.createdAt || new Date().toISOString(), total: order.total ?? order.amount ?? 0 } as any);
    } catch (error: any) {
      showError(`Failed to print invoice: ${error.message}`);
    }
  };

  const handleShareInvoice = async () => {
    if (!order) {return;}
    try {
      const printableOrder = { ...order, createdAt: order.createdAt || new Date().toISOString() };
      await PDFInvoiceGenerator.generateAndSharePDF(printableOrder as any);
    } catch (error: any) {
      showError(`Failed to share invoice: ${error.message}`);
    }
  };

  const orderStatuses: OrderStatus[] = ['Pending', 'In Progress', 'Ready', 'Completed', 'Delivered'];

  if (loading) {
    return <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}><ActivityIndicator animating={true} size="large" /></View>;
  }

  if (!order) {
    return (
      <View style={[styles.container, { backgroundColor: theme?.colors?.background }]}>
        <Header title="Order Details" showBack onBackPress={() => navigation.goBack()} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme?.colors?.error }]}>Order not found</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surfaceVariant }]}>
      <Header
        title="Order Details"
        showBack
        onBackPress={() => navigation.goBack()}
        actions={[
          { icon: 'share', onPress: handleShareInvoice },
          { icon: 'print', onPress: handlePrintInvoice },
          { icon: 'dots-three-vertical', onPress: () => setStatusMenuVisible('header_menu') },
        ]}
      />
      <Menu
          visible={statusMenuVisible === 'header_menu'}
          onDismiss={() => setStatusMenuVisible(null)}
          anchor={{x: 200, y: 50}} // Note: Anchor positioning might need adjustment
        >
          <Menu.Item onPress={handlePrintReceipt} title="Print Receipt" leadingIcon={() => <PhosphorIcon name="receipt" size={20} color={theme?.colors?.onSurface} />} />
          <Menu.Item onPress={handleEdit} title="Edit Order" leadingIcon={() => <PhosphorIcon name="edit" size={20} color={theme?.colors?.onSurface} />} />
          <Menu.Item onPress={() => { setActionSheetType('duplicate'); setStatusMenuVisible(null); }} title="Duplicate Order" leadingIcon={() => <PhosphorIcon name="files" size={20} color={theme?.colors?.onSurface} />} />
          <Divider />
          <Menu.Item onPress={() => { setActionSheetType('delete'); setStatusMenuVisible(null); }} title="Delete Order" leadingIcon={() => <PhosphorIcon name="trash" size={20} color={theme?.colors?.onSurface} />} />
        </Menu>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Surface style={[styles.topCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <View style={styles.topCardHeader}>
            <View style={styles.orderInfoSection}>
              <View style={styles.orderTitleRow}>
                <Text style={[styles.orderNumber, { color: theme.colors.onSurface }]}>ORD-{order.id}</Text>
                <Menu
                  visible={statusMenuVisible === 'status_dropdown'}
                  onDismiss={() => setStatusMenuVisible(null)}
                  anchor={
                    <TouchableOpacity
                      style={[styles.statusDropdown, { backgroundColor: theme?.colors?.primary, borderColor: theme?.colors?.primary }]} 
                      onPress={() => setStatusMenuVisible('status_dropdown')}
                    >
                      <Text style={[styles.statusText, { color: theme?.colors?.onPrimary }]}>{order.status || 'Pending'}</Text>
                      <PhosphorIcon name="chevron-down" size={16} color={theme?.colors?.onPrimary} />
                    </TouchableOpacity>
                  }
                >
                  {orderStatuses.map((status) => (
                    <Menu.Item key={status} onPress={() => { handleStatusUpdate(status); setStatusMenuVisible(null); }} title={status} />
                  ))}
                </Menu>
              </View>
              <Text style={[styles.itemCount, { color: theme?.colors?.primary }]}>{order.items?.length || 3} Items</Text>
            </View>
            <View style={styles.qrCodeContainer}>
              <QRCodeComponent value={QRCodeService.generateQRString(EntityType.Order, order)} size={96} />
            </View>
          </View>
          <View style={styles.topCardBottom}>
            <View>
              <Text style={[styles.dateTextSmall, { color: theme.colors.onSurfaceVariant }]}>Created: {formatDate(order.createdAt || order.created_at || order.date)}</Text>
              <Text style={[styles.dateTextSmall, { color: theme.colors.onSurfaceVariant }]}>Due: {formatDate(order.dueDate || order.due_date)}</Text>
            </View>
            <View>
              <Text style={[styles.totalAmount, { color: theme.colors.onSurface }]}>Total: {formatCurrency(order.total || order.amount || 0)}</Text>
              {calculateBalance() <= 0 ? (
                <Text style={[styles.dueAmount, { color: '#10b981' }]}>Paid</Text>
              ) : (
                <Text style={[styles.dueAmount, { color: theme.colors.error }]}>Due: {formatCurrency(Math.max(0, calculateBalance()))}</Text>
              )}
            </View>
          </View>
        </Surface>

        <Surface style={[styles.orderItemsSection, { backgroundColor: theme?.colors?.surface }]} elevation={2}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme?.colors?.onSurface }]}>Order Items</Text>
            <Text style={[styles.totalItemsText, { color: theme?.colors?.onSurfaceVariant }]}>Total {order.items?.length || 0}</Text>
          </View>
          <View style={styles.itemsList}>
            {order.items && order.items.length > 0 ? (
              order.items.map((item, index) => (
                <View key={index} style={styles.orderItem}>
                  <Text style={styles.itemName}>{(item.serviceType || (item as any).productName || `Item ${index + 1}`)} x {item.quantity || 1}</Text>
                  {item.assignedTo ? (
                    <Text style={styles.assignedText}>Assigned to: {item.assignedTo}</Text>
                  ) : (
                    <Text style={styles.assignedText}>Not Assigned</Text>
                  )}
                </View>
              ))
            ) : (
              <Text>No items in this order.</Text>
            )}
          </View>
        </Surface>

        <Surface style={[styles.progressSection, { backgroundColor: theme?.colors?.surface }]} elevation={2}>
            <View style={styles.progressHeader}>
                <Text style={[styles.sectionTitle, { color: theme?.colors?.onSurface }]}>Progress</Text>
                <Text style={[styles.progressCompletedText, { color: theme?.colors?.onSurfaceVariant }]}>{getCompletedItemsCount()}/{order.items?.length || 0} Completed</Text>
            </View>
            <View style={styles.progressItems}>
                {order.items && order.items.map((item, index) => (
                    <View key={index} style={styles.progressItem}>
                        <View style={styles.progressItemInfo}>
                            <Text style={[styles.progressItemName, { color: theme?.colors?.onSurface }]}>{item.serviceType || (item as any).productName || `Item ${index + 1}`} x {item.quantity || 1}</Text>
                            <Text style={[styles.progressItemStatus, { color: getStatusColor(item.status || 'Pending') }]}>{item.status || 'Pending'}</Text>
                        </View>
                    </View>
                ))}
            </View>
            <View style={styles.progressBarContainer}>
                <View style={[styles.progressBar, { backgroundColor: theme?.colors?.outline }]}>
                    <View style={[styles.progressBarFill, { backgroundColor: theme?.colors?.primary, width: `${getOrderProgress()}%` }]} />
                </View>
            </View>
        </Surface>

        <TouchableOpacity onPress={handleCustomerPress} activeOpacity={0.7}>
            <Surface style={[styles.customerSection, { backgroundColor: theme?.colors?.surface }]} elevation={2}>
                <View style={styles.customerContent}>
                    <View style={styles.customerMainInfo}>
                        <View style={styles.customerNameRow}>
                            <Text style={[styles.customerName, { color: theme?.colors?.onSurface }]}>{order.customerName || order.customer || 'Unknown Customer'}</Text>
                            {(order.isVIP || order.customerName === 'Asfak mahmud') && (
                                <View style={styles.vipBadge}><Text style={styles.vipBadgeText}>VIP</Text></View>
                            )}
                        </View>
                        <Text style={[styles.customerPhone, { color: theme?.colors?.onSurfaceVariant }]}>{order.customer_phone || order.phone || order.customer?.phone || 'N/A'}</Text>
                        <Text style={[styles.activeOrdersText, { color: '#10b981' }]}>{getCustomerActiveOrders()} Active orders</Text>
                    </View>
                    <View style={styles.customerActions}>
                        <IconButton icon={() => <PhosphorIcon name="phone" size={20} color={theme?.colors?.primary} />} size={32} onPress={handleCallCustomer} style={[styles.callIcon, { backgroundColor: theme?.colors?.primaryContainer + '40' }]} />
                    </View>
                </View>
            </Surface>
        </TouchableOpacity>

        <Surface style={[styles.paymentSection, { backgroundColor: theme?.colors?.surface }]} elevation={2}>
            <View style={styles.paymentSectionHeader}>
                <Text style={[styles.sectionTitle, { color: theme?.colors?.onSurface }]}>Payment details</Text>
                <Chip
                    style={{ backgroundColor: getPaymentStatusColor(order) }}
                    textStyle={{ color: theme.colors.onPrimary }}
                >
                    {getPaymentStatusText(order)}
                </Chip>
            </View>
            <View style={styles.paymentDetails}>
                <View style={styles.paymentRow}><Text style={[styles.paymentLabel, { color: theme?.colors?.onSurfaceVariant }]}>Total Amount:</Text><Text style={[styles.paymentValue, { color: theme?.colors?.onSurface }]}>{formatCurrency(order.total || order.amount || 0)}</Text></View>
                <View style={styles.paymentRow}><Text style={[styles.paymentLabel, { color: theme?.colors?.onSurfaceVariant }]}>Advance Paid:</Text><Text style={[styles.paymentValue, { color: theme?.colors?.primary }]}>{formatCurrency(order.paidAmount || order.advance_paid || 0)}</Text></View>
                <View style={styles.paymentRow}><Text style={[styles.paymentLabel, { color: theme?.colors?.onSurfaceVariant }]}>Due:</Text><Text style={[styles.paymentValue, { color: getPaymentStatusColor(order) }]}>{formatCurrency(calculateBalance())}</Text></View>
            </View>
            {calculateBalance() > 0 && (
              <View style={styles.paymentActions}>
                <View style={styles.paymentInputContainer}>
                  <TextInput
                    style={styles.paymentInput}
                    label="Payment Amount"
                    placeholder="Enter amount"
                    value={paymentAmount}
                    onChangeText={setPaymentAmount}
                    keyboardType="numeric"
                    mode="outlined"
                    left={<TextInput.Icon icon={() => <PhosphorIcon name="money" size={24} color={theme?.colors?.onSurface} />} />} 
                    dense
                  />
                </View>
                <TouchableOpacity style={styles.takePaymentButton} onPress={handleTakePayment}>
                  <Text style={styles.takePaymentButtonText}>Take Payment</Text>
                </TouchableOpacity>
              </View>
            )}
        </Surface>

        {isAdmin() && (
          <Surface style={[styles.timelineSection, { backgroundColor: theme?.colors?.surface }]} elevation={2}>
              <TouchableOpacity style={styles.timelineHeader} onPress={handleViewFullTimeline}>
                  <Text style={[styles.sectionTitle, { color: theme?.colors?.onSurface }]}>Recent activity</Text>
                  <PhosphorIcon name="chevron-right" size={20} color={theme?.colors?.primary} />
              </TouchableOpacity>
              <View style={styles.timelineContent}>{renderTimelineEvents()}</View>
          </Surface>
        )}

      </ScrollView>
      <ActionSheet
        visible={actionSheetType === 'delete'}
        onDismiss={() => setActionSheetType(null)}
        title="Delete Order"
        description="Are you sure you want to delete this order? This action cannot be undone."
        options={[
          {
            text: 'Delete',
            style: 'destructive',
            icon: 'trash',
            onPress: async () => {
              if (!order) {return;}
              try {
                setLoading(true);
                if (!deleteOrder) {throw new Error('deleteOrder function is not available');}
                await deleteOrder(order.id);
                showToast('Order deleted successfully');
                navigation.goBack();
              } catch (error: any) {
                LoggingService.error('Failed to delete order', 'ORDER_DETAILS', error);
                showError('Failed to delete order');
              } finally {
                setLoading(false);
              }
            },
          },
        ]}
      />

      <ActionSheet
        visible={actionSheetType === 'duplicate'}
        onDismiss={() => setActionSheetType(null)}
        title="Duplicate Order"
        description="Create a new order with the same details?"
        options={[ 
          {
            text: 'Duplicate',
            style: 'primary',
            icon: 'files',
            onPress: () => {
              if (!order) {return;}
              const duplicateOrder: Partial<Order> = { 
                ...order, 
                id: undefined, 
                createdAt: undefined, 
                status: 'Pending', 
                paidAmount: 0, 
                balanceAmount: order.total || order.amount || 0, 
                deliveryStatus: 'pending' 
              };
              delete duplicateOrder.id; // Ensure id is not carried over
              navigation.navigate('CreateOrder', { order: duplicateOrder as Order, isDuplicate: true });
            }
          }
        ]}
      />
    </View>
  );
};

export default OrderDetailsScreen;
