import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useMemo, useState } from 'react';
import { View, ScrollView, StyleSheet, Share } from 'react-native';
import { Text, Button, Surface, Divider } from 'react-native-paper';

import ActionSheet from '../../components/ui/ActionSheet';
import { useToast } from '../../context/ToastContext';
import QRCodeService, { EntityType } from '../../services/QRCodeService';
import { Order, Customer, OrderItem } from '../../types'; 
import { RootStackParamList } from '../../types/navigation';
import { PDFInvoiceGenerator } from '../../utils/pdfInvoiceGenerator';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

import { useTheme } from '@/context/ThemeContext';
// FIX 1: Assuming Order, Customer, and OrderItem types are all in `types/index.ts` or a similar central file.

// FIX 2: Moved styles for this component into a local StyleSheet to prevent scope errors.
const qrCodeComponentStyles = StyleSheet.create({
  qrFallbackContainer: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  qrFallbackText: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
});

const QRCodeComponent = ({ value, size = 120 }: { value: string, size?: number }) => {
  try {
    const QRCode = require('react-native-qrcode-svg').default;
    return <QRCode value={value} size={size} color="black" backgroundColor="white" />;
  } catch (error) {
    return (
      <View style={[qrCodeComponentStyles.qrFallbackContainer, { width: size, height: size }]}>
        <PhosphorIcon name="qr-code" size={size * 0.4} color="#666" />
        <Text style={qrCodeComponentStyles.qrFallbackText}>{'QR Code\n(Build required)'}</Text>
      </View>
    );
  }
};

type OrderSuccessScreenRouteProp = RouteProp<RootStackParamList, 'OrderSuccess'>;
type OrderSuccessScreenNavigationProp = StackNavigationProp<RootStackParamList, 'OrderSuccess'>;

interface Props {
  route: OrderSuccessScreenRouteProp;
  navigation: OrderSuccessScreenNavigationProp;
}

const OrderSuccessScreen: React.FC<Props> = ({ route, navigation }) => {
  const { theme } = useTheme();
  const { order, customer } = route.params;
  const { showToast, showError } = useToast();
  const [printActionSheetVisible, setPrintActionSheetVisible] = useState(false);

  // FIX 3: Rewrote ViewModel to strictly adhere to the properties confirmed by TypeScript errors.
  const orderViewModel = useMemo(() => {
    if (!order || !customer) {return null;}

    const total = order.total ?? 0;
    const paid = order.paidAmount ?? 0;
    const subtotal = order.subtotal ?? total;
    const discountAmount = order.discount ? (subtotal * (order.discount / 100)) : 0; // Assuming discount is always percentage based on error feedback. Adjust if needed.

    return {
      ...order, // Spread original order to include all its valid properties like isUrgent, notes, etc.
      customerName: customer.name || 'N/A',
      customerPhone: customer.phone || 'N/A',
      displayId: order.id,
      dueDate: order.dueDate ? new Date(order.dueDate).toLocaleDateString() : 'N/A',
      total,
      paid,
      subtotal,
      discountAmount,
      balance: total - paid,
      items: order.items?.map(item => ({
        ...item,
        name: item.productName || 'Unknown Item',
        price: item.unitPrice ?? 0, // Ensure price is always a number
      })) || [], // Ensure items is always an array and has a 'name' property
    };
  }, [order, customer]);

  const orderQR = useMemo(() => {
    if (!order || !customer) {return { qrData: '', displayText: '', instructions: '' };}
    return { qrData: QRCodeService.generateQRString(EntityType.Order, order), displayText: `Order #${order.id}`, instructions: 'Scan to view order details' };
  }, [order, customer]);
  
  const handleShareInvoice = async () => {
    if (!orderViewModel) {return;}

    const invoiceText = `
🎉 ORDER CONFIRMATION 🎉

Order ID: ${orderViewModel.displayId}
Customer: ${orderViewModel.customerName}
Phone: ${orderViewModel.customerPhone}

📋 ORDER DETAILS:
• Service: ${orderViewModel.items?.[0]?.productName || 'N/A'}
• Order Type: ${orderViewModel.orderType}
• Due Date: ${orderViewModel.dueDate}

💰 PAYMENT DETAILS:
• Total Amount: ৳${orderViewModel.total.toFixed(2)}
• Amount Paid: ৳${orderViewModel.paid.toFixed(2)}
• Remaining: ৳${orderViewModel.balance.toFixed(2)}

📞 Elite Tailoring Management
Thank you for choosing us!
    `;

    try {
      await Share.share({ message: invoiceText, title: 'Order Invoice' });
    } catch (error) {
      showError('Failed to share invoice');
    }
  };

  const handlePOSPrint = () => showError('POS printing is not implemented yet.');
  
  const handleGeneratePDF = async () => {
    if (!order || !orderViewModel) {
      showError("Order data is missing.");
      return;
    }
    try {
      const result = await PDFInvoiceGenerator.generatePDF(orderViewModel);
      if (result.success) {
        showToast('Invoice PDF generated successfully!');
      }
    } catch (error: any) {
      showError(`Failed to generate PDF: ${error.message}`);
    }
  };

  const handleCreateAnother = () => navigation.navigate('CreateOrder');
  
  const styles = useMemo(() => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background },
    content: { flex: 1, padding: theme.spacing.lg },
    divider: { marginVertical: theme.spacing.md },
    successHeader: { alignItems: 'center', marginBottom: theme.spacing.xxxl },
    successIconWrapper: { width: 120, height: 120, borderRadius: 60, alignItems: 'center', justifyContent: 'center', marginBottom: theme.spacing.xl, backgroundColor: theme.colors.primaryContainer },
    successTitle: { fontSize: 24, fontWeight: '700', textAlign: 'center', marginBottom: theme.spacing.sm, color: theme.colors.onSurface },
    successSubtitle: { fontSize: 16, textAlign: 'center', marginBottom: theme.spacing.lg, color: theme.colors.onSurfaceVariant },
    qrSection: { alignItems: 'center', marginBottom: theme.spacing.md },
    qrContainer: { alignItems: 'center', marginTop: theme.spacing.sm },
    qrCodeWrapper: { padding: theme.spacing.md, borderRadius: theme.borderRadius.md, marginBottom: theme.spacing.sm, elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, backgroundColor: '#FFFFFF' },
    qrInfo: { alignItems: 'center', maxWidth: 200 },
    qrDisplayText: { color: theme.colors.onSurface, fontWeight: '600' },
    qrInstructions: { color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 },
    invoiceCard: { borderRadius: theme.borderRadius.xl, padding: theme.spacing.lg, marginBottom: theme.spacing.lg, backgroundColor: theme.colors.surface },
    invoiceHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
    invoiceTitle: { fontSize: 22, fontWeight: '700', color: theme.colors.onSurface },
    invoiceId: { color: theme.colors.onSurfaceVariant },
    invoiceSection: { marginBottom: theme.spacing.md },
    sectionTitle: { fontSize: 18, fontWeight: '600', marginBottom: theme.spacing.sm, color: theme.colors.primary },
    detailRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: theme.spacing.xxs },
    totalRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: theme.spacing.sm },
    detailLabel: { color: theme.colors.onSurfaceVariant },
    detailValue: { color: theme.colors.onSurface, fontWeight: '600' },
    totalLabel: { color: theme.colors.onSurface, fontWeight: '700', fontSize: 16 },
    totalValue: { color: theme.colors.primary, fontWeight: '700', fontSize: 16 },
    paidValue: { color: theme.colors.secondary, fontWeight: '600' },
    urgentText: { color: theme.colors.error || '#FF5722', fontWeight: '600' },
    discountText: { color: theme.colors.error || '#FF5722', fontWeight: '600' },
    serviceItem: { marginBottom: theme.spacing.sm },
    actionButtons: { flexDirection: 'row', gap: theme.spacing.sm, marginBottom: theme.spacing.xxxl, flexWrap: 'wrap' },
    actionButton: { flex: 1, minWidth: 100 },
  }), [theme]);

  if (!orderViewModel) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text>Error: No order data found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.qrSection}>
          <Text style={styles.sectionTitle}>Order QR Code</Text>
          <View style={styles.qrContainer}>
            <View style={styles.qrCodeWrapper}><QRCodeComponent value={orderQR.qrData} size={180} /></View>
            <View style={styles.qrInfo}>
              <Text style={styles.qrDisplayText}>{orderQR.displayText}</Text>
              <Text style={styles.qrInstructions}>{orderQR.instructions}</Text>
            </View>
          </View>
        </View>
        <View style={styles.successHeader}>
          <View style={styles.successIconWrapper}><PhosphorIcon name="check-circle" size={64} color={theme.colors.primary} /></View>
          <Text style={styles.successTitle}>Order Created Successfully!</Text>
          <Text style={styles.successSubtitle}>Your order has been created and is ready for processing.</Text>
        </View>
        <Surface style={styles.invoiceCard} elevation={2}>
          <View style={styles.invoiceHeader}>
            <Text style={styles.invoiceTitle}>Order Invoice</Text>
            <Text style={styles.invoiceId}>#{orderViewModel.displayId}</Text>
          </View>
          <Divider style={styles.divider} />
          <View style={styles.invoiceSection}>
            <Text style={styles.sectionTitle}>Customer Details</Text>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Name:</Text><Text style={styles.detailValue}>{orderViewModel.customerName}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Phone:</Text><Text style={styles.detailValue}>{orderViewModel.customerPhone}</Text></View>
          </View>
          <Divider style={styles.divider} />
          <View style={styles.invoiceSection}>
            <Text style={styles.sectionTitle}>Order Details</Text>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Order Type:</Text><Text style={styles.detailValue}>{orderViewModel.orderType}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Due Date:</Text><Text style={styles.detailValue}>{orderViewModel.dueDate}</Text></View>
            {orderViewModel.notes && (<View style={styles.detailRow}><Text style={styles.detailLabel}>Notes:</Text><Text style={styles.detailValue}>{orderViewModel.notes}</Text></View>)}
          </View>
          <Divider style={styles.divider} />
          {/* FIX 5: Use optional chaining on `items` for safety */}
          {orderViewModel.items?.map((item: OrderItem, index: number) => (
            <View key={item.id || index} style={styles.serviceItem}>
              <View style={styles.detailRow}><Text style={styles.detailLabel}>Service:</Text><Text style={styles.detailValue}>{item.productName || 'N/A'}</Text></View>
              <View style={styles.detailRow}><Text style={styles.detailLabel}>Quantity:</Text><Text style={styles.detailValue}>{item.quantity}</Text></View>
              <View style={styles.detailRow}><Text style={styles.detailLabel}>Price:</Text><Text style={styles.detailValue}>৳{item.price || 0}</Text></View>
              {index < (orderViewModel.items?.length ?? 0) - 1 && <Divider style={{ marginVertical: theme.spacing.sm }} />}
            </View>
          ))}
          <Divider style={styles.divider} />
          <View style={styles.invoiceSection}>
            <Text style={styles.sectionTitle}>Payment Summary</Text>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Subtotal:</Text><Text style={styles.detailValue}>৳{orderViewModel.subtotal.toFixed(2)}</Text></View>
            {(orderViewModel.discount ?? 0) > 0 && (<View style={styles.detailRow}><Text style={styles.detailLabel}>Discount ({orderViewModel.discount}%):</Text><Text style={styles.discountText}>-৳{orderViewModel.discountAmount.toFixed(2)}</Text></View>)}
            {/* FIX 5: Use optional chaining on `tax` for safety */}
            {(orderViewModel.tax ?? 0) > 0 && (<View style={styles.detailRow}><Text style={styles.detailLabel}>Tax:</Text><Text style={styles.detailValue}>৳{orderViewModel.tax?.toFixed(2)}</Text></View>)}
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Paid Amount:</Text><Text style={[styles.detailValue, styles.paidValue]}>৳{orderViewModel.paid.toFixed(2)}</Text></View>
            <View style={styles.detailRow}><Text style={styles.detailLabel}>Balance:</Text><Text style={[styles.detailValue, { color: theme.colors.primary }]}>৳{orderViewModel.balance.toFixed(2)}</Text></View>
            <Divider style={{ marginVertical: theme.spacing.sm }} />
            <View style={styles.totalRow}><Text style={styles.totalLabel}>Total Amount:</Text><Text style={styles.totalValue}>৳{orderViewModel.total.toFixed(2)}</Text></View>
          </View>
        </Surface>

        <View style={styles.actionButtons}>
          {/* FIX 6: Changed icon name to a more common one. Adjust if your icon set uses another name. */}
          <Button mode="outlined" icon={() => <PhosphorIcon name="share" size={24} color={theme.colors.onSurface} />} onPress={handleShareInvoice} style={styles.actionButton}>Share</Button>
          <Button mode="outlined" icon={() => <PhosphorIcon name="print" size={24} color={theme.colors.onSurface} />} onPress={() => setPrintActionSheetVisible(true)} style={styles.actionButton}>Print</Button>
          <Button mode="contained" icon={() => <PhosphorIcon name="plus" size={24} color={theme.colors.onPrimary} />} onPress={handleCreateAnother} style={styles.actionButton}>New Order</Button>
        </View>
      </ScrollView>
      <ActionSheet
        visible={printActionSheetVisible}
        onDismiss={() => setPrintActionSheetVisible(false)}
        title="Print Options"
        description="Choose a printing format:"
        options={[
          { text: 'PDF Invoice', style: 'primary', icon: 'file-pdf', onPress: handleGeneratePDF },
          { text: 'POS Receipt', style: 'primary', icon: 'receipt', onPress: handlePOSPrint },
        ]}
      />
    </View>
  );
};

export default OrderSuccessScreen;
