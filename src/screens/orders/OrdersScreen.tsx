import React, { useC<PERSON>back, useMemo, useState } from 'react';
import { FlatList, ListRenderItem, RefreshControl, StyleSheet, View } from 'react-native';

import { OrderCard } from '@/components/cards';
import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import ChipGroup from '@/components/ui/ChipGroup';
import EmptyState from '@/components/ui/EmptyState';
import StatCardGroup from '@/components/ui/StatCardGroup';
import { useAuth } from '@/context/AuthContext';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { useNotifications } from '@/services/notificationService';
import { withPerformanceTracking, usePerformanceTracking } from '@/services/PerformanceMonitoringService';
import { ActionSheetOption } from '@/types';
import { Order, StatCard } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIconName } from '@/utils/phosphorIconRegistry';

interface OrdersScreenProps {
  navigation?: any;
}

interface OrderStats {
  total: number;
  pending: number;
  inProgress: number;
  ready: number;
  delivered: number;
  completed: number;
  revenue: number;
}

const OrdersScreen: React.FC<OrdersScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { state: authState } = useAuth();
  const { unreadCount } = useNotifications();
  const { showSuccess, showError } = useToast();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('All');
  const invoiceBottomSheetRef = React.useRef<any>(null);
  const [invoiceOrder, setInvoiceOrder] = useState<Order | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [actionSheetVisible, setActionSheetVisible] = useState<boolean>(false);
  const [actionSheetOrder, setActionSheetOrder] = useState<Order | null>(null);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');
  
  // Performance monitoring for orders operations
  const { startTracking, endTracking } = usePerformanceTracking('orders_operations');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const filterOptions = [
    { id: 'All', label: 'All', icon: 'clipboard-text' as PhosphorIconName },
    { id: 'Pending', label: 'Pending', icon: 'clock' as PhosphorIconName },
    { id: 'In Progress', label: 'In Progress', icon: 'gear' as PhosphorIconName },
    { id: 'Ready', label: 'Ready', icon: 'check-circle' as PhosphorIconName },
    { id: 'Delivered', label: 'Delivered', icon: 'truck' as PhosphorIconName },
    { id: 'Completed', label: 'Completed', icon: 'check-circle' as PhosphorIconName },
    { id: 'Cancelled', label: 'Cancelled', icon: 'x-circle' as PhosphorIconName },
    { id: 'Recent', label: 'Recent', icon: 'calendar' as PhosphorIconName },
    { id: 'High Value', label: 'High Value', icon: 'star' as PhosphorIconName },
  ];

  const handleRefresh = useCallback(async (): Promise<void> => {
    const trackingId = startTracking('orders_refresh');
    setRefreshing(true);
    try {
      await actions.reloadData();
      LoggingService.info('Orders data refreshed', 'SCREEN');
      showSuccess('Orders refreshed successfully');
    } catch (error) {
      LoggingService.warn('Failed to refresh orders data', 'SCREEN', error as Error);
      showError('Failed to refresh orders');
    } finally {
      setRefreshing(false);
      endTracking(trackingId, 'network', { operation: 'data_refresh' });
    }
  }, [actions, showSuccess, showError, startTracking, endTracking]);

  const normalizeStatus = useCallback((s: any): string => {
    const raw = (s || '').toString().trim().toLowerCase();
    switch (raw) {
      case 'in-progress':
      case 'in progress':
      case 'in_progress':
      case 'processing':
        return 'In Progress';
      case 'completed':
      case 'complete':
        return 'Completed';
      case 'delivered':
        return 'Delivered';
      case 'ready':
        return 'Ready';
      case 'cancelled':
      case 'canceled':
        return 'Cancelled';
      case 'pending':
      default:
        return 'Pending';
    }
  }, []);

  const normalizedOrders = useMemo(() => {
    const orders = state.orders || [];
    if (!Array.isArray(orders)) {return [] as any[];}
    return orders.map((o: any) => ({ ...o, status: normalizeStatus(o.status) }));
  }, [state.orders, normalizeStatus]);

  const filteredOrders = useMemo(() => {
    const trackingId = startTracking('orders_filter_calculation');
    const orders = normalizedOrders;
    const lowercasedQuery = searchQuery.toLowerCase().trim();

    const result = orders.filter((order: any) => {
      const customerName = order?.customerName || order?.customer?.name || '';
      const orderId = order?.id ? order.id.toString() : '';
      const orderStatus = order?.status || 'Pending';

      const matchesSearch =
        customerName.toLowerCase().includes(lowercasedQuery) ||
        orderId.includes(lowercasedQuery);

      if (!matchesSearch) {return false;}

      if (selectedStatus === 'All') {return true;}

      switch (selectedStatus) {
        case 'Recent': {
          const sevenDaysAgo = new Date();
          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
          const orderDate = order?.createdAt ? new Date(order.createdAt) : new Date(0);
          return orderDate > sevenDaysAgo;
        }
        case 'High Value': {
          const orderTotal = order?.total || order?.totalAmount || 0;
          return orderTotal > 5000;
        }
        default:
          return orderStatus === selectedStatus;
      }
    });
    
    endTracking(trackingId, 'compute', { 
      filteredCount: result.length, 
      totalCount: orders.length,
      searchQuery: !!searchQuery,
      selectedStatus 
    });
    
    return result;
  }, [normalizedOrders, searchQuery, selectedStatus, startTracking, endTracking]);

  const handleAddOrder = (): void => {
    LoggingService.info('Navigating to Add Order page', 'NAVIGATION');
    navigation.navigate('AddOrder');
  };

  const handleOrderPress = useCallback((order: Order): void => {
    LoggingService.info('Order card pressed', 'NAVIGATION', { orderId: order.id });
    navigation.navigate('OrderDetails', { order });
  }, [navigation]);

  const handleDeleteOrderConfirm = useCallback(
    (order: Order): void => {
      actions.deleteOrder(order.id.toString());
      setActionSheetVisible(false);
      setActionSheetOrder(null);
    },
    [actions]
  );

  const renderOrderCard: ListRenderItem<any> = useCallback(
    ({ item: order }) => {
      return <OrderCard order={order} onPress={handleOrderPress} />;
    },
    [handleOrderPress]
  );

  const orderStats = useMemo((): OrderStats => {
    const orders = Array.isArray(normalizedOrders) ? normalizedOrders : [];
    return {
      total: orders.length,
      pending: orders.filter((o: any) => o.status === 'Pending').length,
      inProgress: orders.filter((o: any) => o.status === 'In Progress').length,
      ready: orders.filter((o: any) => o.status === 'Ready').length,
      delivered: orders.filter((o: any) => o.status === 'Delivered').length,
      completed: orders.filter((o: any) => o.status === 'Completed').length,
      revenue: orders.reduce((sum: number, order: any) => sum + (order.total || 0), 0),
    };
  }, [normalizedOrders]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Orders'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        showSearch={true}
        searchPlaceholder='Search by name or order ID...'
        // FIX: Changed `onSearchSubmit` to `onSearchChange` for live filtering
        onSearchChange={setSearchQuery}
        actions={[
          {
            icon: 'plus',
            onPress: handleAddOrder,
          },
        ]}
      />

      <View style={{ backgroundColor: theme.colors.background, paddingHorizontal: 16, zIndex: 1 }}>
        <ChipGroup
          filters={filterOptions}
          selectedFilter={selectedStatus}
          onFilterChange={filter => setSelectedStatus(String(filter))}
          showIcons={false}
          showCounts={true}
          data={normalizedOrders}
          countField='status'
          borderRadius='full-rounded'
          style={{ marginTop: 12, marginBottom: 4 }}
        />
      </View>

      <FlatList
        data={filteredOrders}
        renderItem={renderOrderCard}
        keyExtractor={item => item.id.toString()}
        style={[styles.content, { paddingTop: 4 }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <StatCardGroup
            title='Order Stats'
            cards={
              [
                {
                  key: 'total',
                  title: 'Total Orders',
                  value: orderStats.total.toString(),
                  icon: 'clipboard-text',
                  iconColor: theme.colors.primary,
                },
                {
                  key: 'revenue',
                  title: 'Revenue',
                  value: formatCurrency(orderStats.revenue, { decimals: 0 }),
                  icon: 'money',
                  iconColor: theme.colors.secondary,
                },
              ] as StatCard[]
            }
            columns={2}
            showTitle={false}
            containerStyle={{ marginBottom: 16 }}
          />
        )}
        ListEmptyComponent={() => (
          <EmptyState
            type='orders'
            searchQuery={searchQuery}
            onActionPress={handleAddOrder}
            description='No orders found'
            actionLabel='Add Order'
            iconColor={theme.colors.primary}
          />
        )}
      />

      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={`Delete Order #${actionSheetOrder?.id}`}
        description={`Are you sure you want to delete order #${actionSheetOrder?.id}? This action cannot be undone.`}
        options={[
          {
            text: 'Delete',
            onPress: () => handleDeleteOrderConfirm(actionSheetOrder as Order),
            style: 'destructive',
            isAction: true
          },
          {
            text: 'Cancel',
            onPress: () => setActionSheetVisible(false),
            style: 'cancel',
            isAction: false
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
});

export default withPerformanceTracking(OrdersScreen, 'OrdersScreen');