import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useReducer, useCallback, useRef, memo } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  TextInput as RNTextInput,
  ActivityIndicator,
} from 'react-native';
import { Text } from 'react-native-paper';

import ImagePicker from '../../components/forms/ImagePicker';
import Header from '../../components/navigation/Header';
import { Button, Dropdown, Switch, TextInput } from '../../components/ui';
import { useData } from '../../context/DataContext';
import { useTheme, ThemeContextType } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { ImageProcessingService } from '../../services/ImageProcessingService';
import LoggingService from '../../services/LoggingService';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { Product } from '../../types';

// --- Type Definitions ---
// FIX: ItemType is now correctly 'product' or 'fabric'
type ItemType = 'product' | 'fabric';

// FIX: Form data now includes color and supplier info
interface ItemFormData {
  name: string; description: string; category: string; sku: string;
  itemType: ItemType; basePrice: string; purchasePrice: string; costPrice: string;
  color: string;
  baseUnit: string; totalQuantity: string; minimumStockLevel: string;
  isActive: boolean; images: string[];
  supplierName: string; supplierPhone: string;
}

const ITEM_TYPE_OPTIONS = [
    { label: 'Product', value: 'product' as const },
    { label: 'Fabric', value: 'fabric' as const },
];
const CATEGORIES: Record<ItemType, string[]> = {
    product: ['Shirts', 'Pants', 'Dresses', 'Suits', 'Jackets'],
    fabric: ['Cotton', 'Silk', 'Wool', 'Linen', 'Polyester'],
};
const UNITS: Record<ItemType, string[]> = {
    product: ['pieces', 'sets'],
    fabric: ['meters', 'yards', 'inches'],
};

// --- State Management with Reducer ---
type FormState = { formData: ItemFormData; errors: Partial<Record<keyof ItemFormData, string>>; status: 'idle' | 'loading' | 'saving' | 'error' };
type FormAction = 
    | { type: 'LOAD_DATA'; payload: Product }
    | { type: 'SET_FIELD'; payload: { field: keyof ItemFormData; value: any } }
    | { type: 'SET_IMAGE'; payload: { index: number; uri: string } }
    | { type: 'SET_ITEM_TYPE'; payload: ItemType }
    | { type: 'SET_ERRORS'; payload: Partial<Record<keyof ItemFormData, string>> }
    | { type: 'SUBMIT' | 'SUBMIT_SUCCESS' | 'SUBMIT_FAILURE' };

const createInitialState = (itemType: ItemType): FormState => ({
    formData: {
        name: '', description: '', category: '', sku: '', itemType, basePrice: '',
        costPrice: itemType === 'product' ? '' : '0', purchasePrice: itemType === 'fabric' ? '' : '0',
        color: '', baseUnit: UNITS[itemType][0], totalQuantity: '', minimumStockLevel: '10',
        isActive: true, images: [], supplierName: '', supplierPhone: '',
    },
    errors: {}, status: 'idle',
});

const formReducer = (state: FormState, action: FormAction): FormState => {
    switch (action.type) {
        case 'LOAD_DATA':
            const item = action.payload;
            return { ...state, status: 'idle', formData: {
                ...createInitialState(item.itemType as ItemType).formData,
                name: item.name, description: item.description || '', category: item.category,
                sku: item.sku || '', itemType: item.itemType as ItemType,
                basePrice: String(item.sellingPrice),
                costPrice: String(item.costPrice || ''),
                purchasePrice: String(item.purchasePrice || ''),
                color: (item as any).color || '',
                baseUnit: item.baseUnit, totalQuantity: String(item.currentStock),
                minimumStockLevel: String(item.minimumStockLevel), isActive: item.isActive,
                images: item.images || [],
                supplierName: (item as any).supplierName || '',
                supplierPhone: (item as any).supplierPhone || '',
            }};
        case 'SET_FIELD':
            return { ...state, formData: { ...state.formData, [action.payload.field]: action.payload.value }, errors: { ...state.errors, [action.payload.field]: undefined } };
        case 'SET_IMAGE':
            const newImages = [...state.formData.images];
            newImages[action.payload.index] = action.payload.uri;
            return { ...state, formData: { ...state.formData, images: newImages } };
        case 'SET_ITEM_TYPE':
            const itemType = action.payload;
            return { ...state, formData: { ...state.formData, itemType, category: '', baseUnit: UNITS[itemType][0] }};
        case 'SET_ERRORS': return { ...state, errors: action.payload };
        case 'SUBMIT': return { ...state, status: 'saving' };
        case 'SUBMIT_SUCCESS': return { ...state, status: 'idle' };
        case 'SUBMIT_FAILURE': return { ...state, status: 'error' };
        default: return state;
    }
};

// --- Sub-Components for Readability ---
const ItemTypeSelector = memo<{ itemType: ItemType; dispatch: React.Dispatch<FormAction>; theme: ThemeContextType }>(({ itemType, dispatch, theme }) => (
    <View style={styles.section}>
        <Text style={styles.sectionTitle}>Item Type</Text>
        <View style={styles.itemTypeGrid}>
            {ITEM_TYPE_OPTIONS.map((option) => (
                <TouchableOpacity key={option.value} style={styles.radioOption} onPress={() => dispatch({ type: 'SET_ITEM_TYPE', payload: option.value })}>
                    <View style={[styles.radioCircle, { borderColor: itemType === option.value ? theme.colors.primary : theme.colors.outline }]}>
                        {itemType === option.value && <View style={[styles.radioDot, { backgroundColor: theme.colors.primary }]} />}
                    </View>
                    <Text style={[styles.radioLabel, { color: itemType === option.value ? theme.colors.primary : theme.colors.onSurface }]}>{option.label}</Text>
                </TouchableOpacity>
            ))}
        </View>
    </View>
));

// --- Main Component ---
const AddItemScreen: React.FC<{ route?: any }> = ({ route }) => {
    const theme = useTheme();
    const { state: dataState, actions } = useData();
    const { showSuccess, showError } = useToast();
    const navigation = useNavigation<any>();

    const itemId = route?.params?.itemId;
    const isEditing = !!itemId;
    const initialItemType: ItemType = route?.params?.itemType || 'product';

    const [state, dispatch] = useReducer(formReducer, createInitialState(initialItemType));
    const { formData, errors, status } = state;

    const refs = {
        colorRef: useRef<RNTextInput>(null), skuRef: useRef<RNTextInput>(null),
        sellingPriceRef: useRef<RNTextInput>(null), priceRef: useRef<RNTextInput>(null),
        quantityRef: useRef<RNTextInput>(null), minStockRef: useRef<RNTextInput>(null),
        supplierNameRef: useRef<RNTextInput>(null), supplierPhoneRef: useRef<RNTextInput>(null),
    };

    useEffect(() => {
        if (isEditing && itemId) {
            const item = dataState.products.find(p => p.id === itemId);
            if (item) {dispatch({ type: 'LOAD_DATA', payload: item });}
        }
    }, [isEditing, itemId, dataState.products]);

    const handleSave = useCallback(async () => {
        const newErrors: Partial<Record<keyof ItemFormData, string>> = {};
        if (!formData.name.trim()) {newErrors.name = 'Item name is required';}
        if (!formData.category) {newErrors.category = 'Category is required';}
        if (!formData.basePrice || parseFloat(formData.basePrice) <= 0) {newErrors.basePrice = 'Valid selling price is required';}
        if (!formData.totalQuantity.trim()) {newErrors.totalQuantity = 'Total quantity is required';}
        if (formData.itemType === 'fabric') {
            if (!formData.supplierName.trim()) {newErrors.supplierName = 'Supplier name is required';}
            if (!formData.supplierPhone.trim()) {newErrors.supplierPhone = 'Supplier phone is required';}
        }
        if (Object.keys(newErrors).length > 0) {
            dispatch({ type: 'SET_ERRORS', payload: newErrors });
            showError("Please fill all required fields.");
            return;
        }

        dispatch({ type: 'SUBMIT' });
        try {
            const newImageFiles = formData.images.filter(uri => uri?.startsWith('file://'));
            const existingImageUrls = formData.images.filter(uri => uri?.startsWith('http'));
            const uploadedUrls = newImageFiles.length > 0 ? await Promise.all(newImageFiles.map(uri => ImageProcessingService.optimizeAndUpload(uri))) : [];
            const finalImages = [...existingImageUrls, ...uploadedUrls];

            const dataToSave = {
                ...formData,
                basePrice: parseFloat(formData.basePrice),
                sellingPrice: parseFloat(formData.basePrice),
                costPrice: parseFloat(formData.costPrice || '0'),
                purchasePrice: parseFloat(formData.purchasePrice || '0'),
                currentStock: parseInt(formData.totalQuantity || '0'),
                minimumStockLevel: parseInt(formData.minimumStockLevel || '0'),
                images: finalImages,
            };

            if (isEditing) {
                const existingItem = dataState.products.find(p => p.id === itemId);
                if (!existingItem) {throw new Error("Item not found");}
                await actions.updateProduct({ ...existingItem, ...dataToSave, id: itemId });
                showSuccess('Item updated successfully!');
            } else {
                await actions.addProduct({ ...dataToSave, isService: false, availableUnits: UNITS[dataToSave.itemType] });
                showSuccess('Item added successfully!');
            }
            dispatch({ type: 'SUBMIT_SUCCESS' });
            navigation.goBack();
        } catch (error) {
            showError('Failed to save item. Please try again.');
            dispatch({ type: 'SUBMIT_FAILURE' });
        }
    }, [formData, isEditing, itemId, dataState.products, actions, showSuccess, showError, navigation]);
    
    const generateSku = useCallback(() => {
        if (!formData.name.trim() || !formData.category) {
            showError('Please enter a name and category first.');
            return;
        }
        const sku = `${formData.itemType.slice(0, 3).toUpperCase()}-${formData.category.slice(0, 3).toUpperCase()}-${Date.now().toString().slice(-4)}`;
        dispatch({ type: 'SET_FIELD', payload: { field: 'sku', value: sku } });
      }, [formData.name, formData.category, formData.itemType, showError]);

    if (status === 'loading') {return <View style={styles.loadingContainer}><ActivityIndicator /></View>;}

    return (
        <View style={styles.container}>
            <Header title={isEditing ? 'Edit Item' : 'Add Item'} showBack onBackPress={() => navigation.goBack()} />
            <KeyboardAvoidingView style={styles.flex} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
                <ScrollView style={styles.flex} contentContainerStyle={styles.scrollContent} keyboardShouldPersistTaps="handled">
                    {!isEditing && <ItemTypeSelector itemType={formData.itemType} dispatch={dispatch} theme={theme} />}

                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>Basic Information</Text>
                        <TextInput label="Name" value={formData.name} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'name', value: v } })} error={errors.name} required returnKeyType="next" onSubmitEditing={() => refs.colorRef.current?.focus()} blurOnSubmit={false} />
                        <TextInput ref={refs.colorRef} label="Color" value={formData.color} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'color', value: v } })} returnKeyType="next" onSubmitEditing={() => refs.skuRef.current?.focus()} blurOnSubmit={false} />
                        <Dropdown label="Category" value={formData.category} onValueChange={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'category', value: v } })} options={CATEGORIES[formData.itemType].map(cat => ({ label: cat, value: cat }))} error={errors.category} required />
                        <View style={styles.row}>
                            <TextInput ref={refs.skuRef} label="SKU" value={formData.sku} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'sku', value: v } })} style={styles.flex} returnKeyType="next" onSubmitEditing={() => refs.sellingPriceRef.current?.focus()} blurOnSubmit={false} />
                            <Button onPress={generateSku} variant="text">Generate</Button>
                        </View>
                    </View>

                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>Pricing (BDT)</Text>
                        <View style={styles.row}>
                            <TextInput ref={refs.sellingPriceRef} label="Selling Price" value={formData.basePrice} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'basePrice', value: v } })} keyboardType="numeric" error={errors.basePrice} required style={styles.flex} returnKeyType="next" onSubmitEditing={() => refs.priceRef.current?.focus()} blurOnSubmit={false} />
                            <TextInput ref={refs.priceRef} label={formData.itemType === 'product' ? "Cost Price" : "Purchase Price"} value={formData.itemType === 'product' ? formData.costPrice : formData.purchasePrice} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: formData.itemType === 'product' ? 'costPrice' : 'purchasePrice', value: v } })} keyboardType="numeric" style={styles.flex} returnKeyType="next" onSubmitEditing={() => refs.quantityRef.current?.focus()} blurOnSubmit={false} />
                        </View>
                    </View>

                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>Inventory</Text>
                        <View style={styles.row}>
                            <TextInput ref={refs.quantityRef} label="Total Quantity" value={formData.totalQuantity} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'totalQuantity', value: v } })} keyboardType="numeric" error={errors.totalQuantity} required style={styles.flex} returnKeyType="next" onSubmitEditing={() => refs.minStockRef.current?.focus()} blurOnSubmit={false} />
                            <TextInput ref={refs.minStockRef} label="Min. Stock" value={formData.minimumStockLevel} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'minimumStockLevel', value: v } })} keyboardType="numeric" style={styles.flex} returnKeyType="done" onSubmitEditing={formData.itemType === 'fabric' ? () => refs.supplierNameRef.current?.focus() : handleSave} />
                        </View>
                        <Dropdown label="Base Unit" value={formData.baseUnit} onValueChange={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'baseUnit', value: v } })} options={UNITS[formData.itemType].map(unit => ({ label: unit, value: unit }))} style={{ marginTop: SPACING.md }} />
                    </View>

                    {formData.itemType === 'fabric' && (
                        <View style={styles.section}>
                            <Text style={styles.sectionTitle}>Supplier Information</Text>
                            <TextInput ref={refs.supplierNameRef} label="Supplier Name" value={formData.supplierName} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'supplierName', value: v } })} error={errors.supplierName} required returnKeyType="next" onSubmitEditing={() => refs.supplierPhoneRef.current?.focus()} blurOnSubmit={false} />
                            <TextInput ref={refs.supplierPhoneRef} label="Supplier Phone" value={formData.supplierPhone} onChangeText={(v) => dispatch({ type: 'SET_FIELD', payload: { field: 'supplierPhone', value: v } })} keyboardType="phone-pad" error={errors.supplierPhone} required returnKeyType="done" onSubmitEditing={handleSave} />
                        </View>
                    )}

                    <View style={styles.section}>
                        <Text style={styles.sectionTitle}>Images</Text>
                        {formData.itemType === 'product' ? (
                            <View style={styles.imagesContainer}>
                                {[0, 1, 2].map(index => (<ImagePicker key={index} currentImage={formData.images[index]} onImageSelected={(uri) => dispatch({ type: 'SET_IMAGE', payload: { index, uri } })} />))}
                            </View>
                        ) : (
                            <ImagePicker currentImage={formData.images[0]} onImageSelected={(uri) => dispatch({ type: 'SET_IMAGE', payload: { index: 0, uri } })} />
                        )}
                    </View>

                    <Button onPress={handleSave} loading={status === 'saving'} disabled={status === 'saving'} icon="check">{isEditing ? 'Update Item' : 'Save Item'}</Button>
                </ScrollView>
            </KeyboardAvoidingView>
        </View>
    );
};

const styles = StyleSheet.create({
  container: { flex: 1 }, flex: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  scrollContent: { padding: SPACING.md, paddingBottom: SPACING.xl },
  section: { marginBottom: SPACING.lg },
  sectionTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.bold, marginBottom: SPACING.md },
  itemTypeGrid: { flexDirection: 'row', flexWrap: 'wrap', gap: SPACING.sm },
  radioOption: { flexDirection: 'row', alignItems: 'center', paddingVertical: 8, paddingHorizontal: 4, },
  radioCircle: { width: 24, height: 24, borderRadius: 12, borderWidth: 2, alignItems: 'center', justifyContent: 'center', marginRight: 8, },
  radioDot: { width: 12, height: 12, borderRadius: 6, },
  radioLabel: { fontSize: TYPOGRAPHY.fontSize.md, fontWeight: TYPOGRAPHY.fontWeight.medium, },
  row: { flexDirection: 'row', gap: SPACING.md, alignItems: 'flex-start' },
  imagesContainer: { flexDirection: 'row', justifyContent: 'space-around', gap: SPACING.sm },
});

export default AddItemScreen;