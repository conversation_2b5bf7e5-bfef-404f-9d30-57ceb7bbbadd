import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { FlatList, View, StyleSheet, RefreshControl } from 'react-native';
import { Text, FAB } from 'react-native-paper';

import { InventoryCard, InventorySearch } from '../../components/inventory';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { ActionSheetOption } from '../../types';
import { InventoryItem } from '../../types/inventory';
import { RootStackParamList } from '../../types/navigation';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';


type InventoryItemsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'InventoryItems'>;

interface InventoryItemsScreenProps {
  navigation: InventoryItemsScreenNavigationProp;
  route?: {
    params?: {
      filter?: 'active' | 'low-stock' | 'all';
      category?: string;
    };
  };
}

const InventoryItemsScreen: React.FC<InventoryItemsScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state: dataState, actions } = useData();
  const items = dataState.products;
  const isLoading = dataState.loading;
  const error = dataState.error;
  const loadItems = actions.reloadData;
  const getStockByItem = actions.getStockByItem;

  const initialFilter = route?.params?.filter || 'all';
  const initialCategory = route?.params?.category;

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [itemsWithStock, setItemsWithStock] = useState<Map<string, number>>(new Map());
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([]);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription] = useState<string>('');

  // Load items on mount
  // useEffect(() => {
  //   if (!isLoading && !error && items.length === 0) {
  //     loadItems();
  //   }
  // }, [isLoading, error, items.length, loadItems]);

  // Load stock data for items
  useEffect(() => {
    const loadStockData = async () => {
      const stockMap = new Map<string, number>();
      for (const item of items) {
        try {
          const stockData = await getStockByItem(item.id);
          const totalStock = stockData.reduce((sum: number, stock: { quantity: number }) => sum + stock.quantity, 0);
          stockMap.set(item.id, totalStock);
        } catch (err) {
          LoggingService.warn(`Failed to load stock for item ${item.id}`, 'INVENTORY_ITEMS', err as Error);
          stockMap.set(item.id, 0);
        }
      }

      if (stockMap.size !== itemsWithStock.size || ![...stockMap.entries()].every(([key, value]) => itemsWithStock.get(key) === value)) {
        setItemsWithStock(stockMap);
      }
    };

    if (items.length > 0) {
      loadStockData();
    }
  }, [items, getStockByItem, itemsWithStock]);

  // Apply initial filters
  const initialFilteredItems = useMemo(() => {
    let filtered = items;

    switch (initialFilter) {
      case 'active':
        filtered = filtered.filter(item => item.isActive);
        break;
      case 'low-stock':
        filtered = filtered.filter(item => {
          const stock = itemsWithStock.get(item.id) || 0;
          return stock <= (item.minimumStockLevel || 0) && (item.minimumStockLevel || 0) > 0;
        });
        break;
    }

    if (initialCategory) {
      filtered = filtered.filter(item => item.category === initialCategory);
    }

    return filtered;
  }, [items, initialFilter, initialCategory, itemsWithStock]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadItems();
      LoggingService.info('Inventory items refreshed', 'INVENTORY_ITEMS');
    } catch (err) {
      LoggingService.warn('Failed to refresh inventory items', 'INVENTORY_ITEMS', err as Error);
    } finally {
      setRefreshing(false);
    }
  }, [loadItems]);

  const handleItemPress = useCallback((item: InventoryItem) => {
    navigation.navigate('InventoryItemDetail', { itemId: item.id });
  }, [navigation]);

  const handleAddItem = useCallback(() => {
    navigation.navigate('AddEditInventoryItem', {});
  }, [navigation]);

  const handleEditItem = useCallback((item: InventoryItem) => {
    navigation.navigate('AddEditInventoryItem', { itemId: item.id, mode: 'edit' });
  }, [navigation]);

  const handleStockIn = useCallback((item: InventoryItem) => {
    navigation.navigate('StockOperations', { operation: 'in', itemId: item.id });
  }, [navigation]);

  const renderItem = useCallback(
    ({ item }: { item: InventoryItem }) => {
      const stockLevel = itemsWithStock.get(item.id) || 0;
      return (
        <InventoryCard
          item={item}
          stockLevel={stockLevel}
          onPress={() => handleItemPress(item)}
          showStockLevel
          showActions
          showPrices
          onEditPress={() => handleEditItem(item)}
          onStockPress={() => handleStockIn(item)}
        />
      );
    },
    [itemsWithStock, handleItemPress, handleEditItem, handleStockIn]
  );

  const getScreenTitle = (): string => {
    if (initialCategory) {return initialCategory;}
    switch (initialFilter) {
      case 'active': return 'Active Items';
      case 'low-stock': return 'Low Stock Items';
      default: return 'Inventory Items';
    }
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <PhosphorIcon name='package' size={64} color={theme.colors.onSurfaceVariant} />
      <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>
        {initialFilter === 'low-stock' ? 'No Low Stock Items' : 'No Items Found'}
      </Text>
      <Text style={[styles.emptyMessage, { color: theme.colors.onSurfaceVariant }]}>
        {initialFilter === 'low-stock'
          ? 'All items have adequate stock levels.'
          : 'Try adding your first inventory item.'}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={getScreenTitle()}
        onBackPress={() => navigation.goBack()}
        showBack
        actions={[{ icon: 'plus', onPress: handleAddItem }]}
      />

      <View style={styles.content}>
        <InventorySearch items={initialFilteredItems} onFilteredResults={setFilteredItems} />
        <FlatList
          data={filteredItems}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            styles.listContent,
            filteredItems.length === 0 && styles.emptyListContent,
          ]}
          ListEmptyComponent={renderEmptyState}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      </View>

      <FAB
        icon='plus'
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddItem}
        label='Add Item'
      />

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  listContent: {
    paddingBottom: 100, // Space for FAB
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 32,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  separator: {
    height: 8,
  }
});

export default InventoryItemsScreen;
