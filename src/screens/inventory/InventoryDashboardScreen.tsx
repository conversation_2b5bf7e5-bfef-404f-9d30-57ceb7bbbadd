import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo, useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';

import { InventoryCard } from '@/components/inventory';
import Header from '@/components/navigation/Header';
import ChipGroup from '@/components/ui/ChipGroup';
import StatCardGroup from '@/components/ui/StatCardGroup';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { withPerformanceTracking, usePerformanceTracking } from '@/services/PerformanceMonitoringService';
import { SPACING } from '@/theme/theme';
import { Product, StatCard, InventoryItemType } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

const PREVIEW_ITEM_COUNT = 5;
const DEFAULT_LOW_STOCK_THRESHOLD = 10;

interface InventoryDashboardScreenProps {
  navigation?: any;
}

// This helper function creates an object that has the right structure for the card.
// We remove the return type annotation to avoid the initial error.
const normalizeProductToInventoryItem = (item: Product) => {
  return {
    id: item.id,
    name: item.name,
    category: item.category || 'N/A',
    sku: item.sku || 'N/A',
    stock: item.stock || 0,
    baseUnit: item.baseUnit || 'pc',
    availableUnits: item.availableUnits || [],
    purchasePrice: item.purchasePrice || 0,
    sellingPrice: item.price || 0,
    lowStockThreshold: DEFAULT_LOW_STOCK_THRESHOLD,
    isActive: true,
  };
};

const InventoryDashboardScreen: React.FC<InventoryDashboardScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const { state: dataState, actions: dataActions } = useData();
  const { showSuccess, showError } = useToast();
  
  // Performance monitoring for inventory operations
  const { startTracking, endTracking } = usePerformanceTracking('inventory_operations');

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'all-products' | 'fabrics' | 'low-stock'>('all-products');

  const { products: allItems } = dataState; // Rename to avoid conflict
  const products = useMemo(() => allItems.filter(item => item.itemType === 'product'), [allItems]);
  const fabrics = useMemo(() => allItems.filter(item => item.itemType === 'fabric'), [allItems]);

  const handleRefresh = useCallback(async (): Promise<void> => {
    const trackingId = startTracking('inventory_refresh');
    setRefreshing(true);
    try {
      await dataActions.reloadData();
      showSuccess('Inventory dashboard refreshed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred.';
      LoggingService.error('Failed to refresh inventory', 'INVENTORY_DASHBOARD', { errorMessage });
      showError('Failed to refresh inventory dashboard');
    } finally {
      setRefreshing(false);
      endTracking(trackingId, 'network', { operation: 'inventory_refresh' });
    }
  }, [dataActions, showSuccess, showError, startTracking, endTracking]);

  const dashboardStats = useMemo(() => {
    const trackingId = startTracking('inventory_stats_calculation');
    const allItems = dataState.products || []; // Use allItems here
    const lowStockItems = allItems.filter(
      item => (item.stock || 0) <= DEFAULT_LOW_STOCK_THRESHOLD
    );

    const totalProducts = allItems.filter(item => item.itemType === 'product').length;
    const totalFabrics = allItems.filter(item => item.itemType === 'fabric').length;

    const totalValue = allItems.reduce(
      (sum, item) => sum + (item.purchasePrice || 0) * (item.stock || 0),
      0
    );

    const result = {
      totalItems: allItems.length,
      totalProducts,
      totalFabrics,
      lowStockItemsCount: lowStockItems.length,
      lowStockItems,
      totalValue,
      recentTransactionsCount: 0,
    };
    
    endTracking(trackingId, 'compute', {
      totalItems: result.totalItems,
      lowStockCount: result.lowStockItemsCount,
      totalValue: result.totalValue
    });
    
    return result;
  }, [dataState.products, startTracking, endTracking]);

  const dashboardCards = useMemo(
    (): StatCard[] => [
      {
        key: 'total-products', // Changed key
        title: 'Total Products',
        value: dashboardStats.totalProducts.toString(), // Use totalProducts
        icon: 'package',
        iconColor: theme.colors.primary,
        onPress: () => navigation.navigate('InventoryItems', { filter: 'products' }), // Added filter
      },
      {
        key: 'total-fabrics', // New card
        title: 'Total Fabrics',
        value: dashboardStats.totalFabrics.toString(),
        icon: 'scissors', // Assuming a suitable icon
        iconColor: theme.colors.secondary,
        onPress: () => navigation.navigate('InventoryItems', { filter: 'fabrics' }), // Added filter
      },
      {
        key: 'low-stock',
        title: 'Low Stock Alerts',
        value: dashboardStats.lowStockItemsCount.toString(),
        icon: 'warning-circle',
        iconColor:
          dashboardStats.lowStockItemsCount > 0 ? theme.colors.error : theme.colors.success,
        onPress: () => navigation.navigate('InventoryItems', { filter: 'low-stock' }),
      },
      {
        key: 'inventory-value',
        title: 'Inventory Value',
        icon: 'chart-line',
        value: formatCurrency(dashboardStats.totalValue, { decimals: 0 }),
        iconColor: theme.colors.tertiary,
        onPress: () => navigation.navigate('Reports'),
      },
      {
        key: 'recent-activity',
        title: 'Recent Activity',
        value: dashboardStats.recentTransactionsCount.toString(),
        icon: 'clock',
        iconColor: theme.colors.secondary,
        onPress: () => navigation.navigate('TransactionHistory'),
      },
    ],
    [dashboardStats, theme.colors, navigation]
  );

  const handleAddItem = useCallback(() => navigation.navigate('AddEditInventoryItem'), [navigation]);
  const handleStockOperation = useCallback(
    () => navigation.navigate('StockOperations'),
    [navigation]
  );
  const handleStockTransfer = useCallback(
    () => navigation.navigate('StockTransfer'),
    [navigation]
  );
  const handleWarehouseManagement = useCallback(
    () => navigation.navigate('WarehouseManagement'),
    [navigation]
  );

  const renderEmptyState = (icon: any, message: string) => (
    <View style={styles.emptyTabState}>
      <PhosphorIcon name={icon} size={48} color={theme.colors.onSurfaceVariant} />
      <Text style={[styles.emptyTabText, { color: theme.colors.onSurfaceVariant }]}>{message}</Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Inventory'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ icon: 'plus', onPress: handleAddItem }]}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <View style={styles.statsSection}>
            <StatCardGroup title='' cards={dashboardCards} columns={2} showTitle={false} />
          </View>

          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Quick Actions
            </Text>
            <View style={styles.quickActionsGrid}>
              {[
                {
                  label: 'Add Item',
                  icon: 'plus',
                  color: theme.colors.primary,
                  onPress: handleAddItem,
                },
                {
                  label: 'Stock In/Out',
                  icon: 'arrow-up-down',
                  color: theme.colors.secondary,
                  onPress: handleStockOperation,
                },
                {
                  label: 'Transfer',
                  icon: 'arrows-left-right',
                  color: theme.colors.tertiary,
                  onPress: handleStockTransfer,
                },
                {
                  label: 'Warehouses',
                  icon: 'warehouse',
                  color: theme.colors.primary,
                  onPress: handleWarehouseManagement,
                },
              ].map(({ label, icon, color, onPress }) => (
                <TouchableOpacity key={label} style={styles.quickActionItem} onPress={onPress}>
                  <View style={[styles.iconContainer, { backgroundColor: color }]}>
                    <PhosphorIcon name={icon as any} size={20} color={theme.colors.onPrimary} />
                  </View>
                  <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                    {label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.sectionContainer}>
            <ChipGroup
              filters={[
                { id: 'all-products', label: 'All Products' },
                { id: 'fabrics', label: 'Fabrics' },
                { id: 'low-stock', label: 'Low Stock' },
              ]}
              selectedFilter={activeTab}
              onFilterChange={filter => setActiveTab(filter as any)}
              style={styles.tabContainer}
            />

            <View style={[styles.tabContent, { backgroundColor: theme.colors.surface }]}>
              {activeTab === 'all-products' && (
                <>
                  {(products || []).length > 0
                    ? (products || [])
                        .slice(0, PREVIEW_ITEM_COUNT)
                        .map(item => (
                          <InventoryCard
                            key={item.id}
                            item={normalizeProductToInventoryItem(item) as any}
                            onPress={() =>
                              navigation.navigate('InventoryItemDetail', { itemId: item.id })
                            }
                            compact={true}
                          />
                        ))
                    : renderEmptyState('package', 'No products found.')}
                  {(products || []).length > PREVIEW_ITEM_COUNT && (
                    <TouchableOpacity
                      style={[styles.viewAllButton, { backgroundColor: theme.colors.surfaceVariant }]}
                      onPress={() => navigation.navigate('InventoryItems')}
                    >
                      <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                        View All Products
                      </Text>
                    </TouchableOpacity>
                  )}
                </>
              )}

              {activeTab === 'fabrics' && (
                <>
                  {(fabrics || []).length > 0
                    ? (fabrics || [])
                        .slice(0, PREVIEW_ITEM_COUNT)
                        .map(item => (
                          <InventoryCard
                            key={item.id}
                            item={normalizeProductToInventoryItem(item) as any}
                            onPress={() =>
                              navigation.navigate('InventoryItemDetail', { itemId: item.id })
                            }
                            compact={true}
                          />
                        ))
                    : renderEmptyState('scissors', 'No fabrics found.')}
                  {(fabrics || []).length > PREVIEW_ITEM_COUNT && (
                    <TouchableOpacity
                      style={[styles.viewAllButton, { backgroundColor: theme.colors.surfaceVariant }]}
                      onPress={() => navigation.navigate('InventoryItems', { filter: 'fabrics' })}
                    >
                      <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                        View All Fabrics
                      </Text>
                    </TouchableOpacity>
                  )}
                </>
              )}

              {activeTab === 'low-stock' && (
                <>
                  {dashboardStats.lowStockItems.length > 0
                    ? dashboardStats.lowStockItems
                        .slice(0, PREVIEW_ITEM_COUNT)
                        .map(item => (
                          <InventoryCard
                            key={item.id}
                            item={normalizeProductToInventoryItem(item) as any}
                            onPress={() =>
                              navigation.navigate('InventoryItemDetail', { itemId: item.id })
                            }
                            compact={true}
                          />
                        ))
                    : renderEmptyState('check-circle', 'No low stock items.')}
                  {dashboardStats.lowStockItems.length > PREVIEW_ITEM_COUNT && (
                    <TouchableOpacity
                      style={[styles.viewAllButton, { backgroundColor: theme.colors.surfaceVariant }]}
                      onPress={() => navigation.navigate('InventoryItems', { filter: 'low-stock' })}
                    >
                      <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
                        View All Low Stock Items
                      </Text>
                    </TouchableOpacity>
                  )}
                </>
              )}
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollContent: {
      flex: 1,
    },
    content: {
      padding: SPACING.md,
    },
    sectionContainer: {
      marginVertical: SPACING.lg,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: SPACING.md,
    },
    quickActionsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: SPACING.sm,
    },
    quickActionItem: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: SPACING.md,
      borderRadius: 12,
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    quickActionText: {
      fontSize: 12,
      fontWeight: '500',
      textAlign: 'center',
    },
    statsSection: {
      marginBottom: SPACING.md,
    },
    tabContainer: {
      marginBottom: SPACING.md,
    },
    tabContent: {
      borderRadius: 12,
      padding: SPACING.md,
      gap: SPACING.sm,
    },
    viewAllButton: {
      alignSelf: 'center',
      marginTop: SPACING.md,
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.lg,
      borderRadius: 8,
    },
    viewAllText: {
      fontSize: 14,
      fontWeight: '600',
    },
    emptyTabState: {
      alignItems: 'center',
      paddingVertical: SPACING.xl,
    },
    emptyTabText: {
      fontSize: 14,
      marginTop: SPACING.md,
      textAlign: 'center',
    },
  });
  
  export default withPerformanceTracking(InventoryDashboardScreen, 'InventoryDashboardScreen');