/**
 * Inventory Item Detail Screen
 * Detailed view of inventory item with stock breakdown and transaction history
 */

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Image, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { Button, Card, Chip, FAB, Portal, Text } from 'react-native-paper';

import { useData } from '../../context/DataContext';

import {
  ConversionPreview,
  StockLevelIndicator,
  TransactionHistoryList,
} from '@/components/inventory';
import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import { useTheme } from '@/context/ThemeContext';
import LoggingService from '@/services/LoggingService';
import { SPACING } from '@/theme/theme';
import { ActionSheetOption } from '@/types';
import { InventoryItem, InventoryStock, InventoryTransaction } from '@/types/inventory';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';
import UnitConverter from '@/utils/UnitConverter';

interface InventoryItemDetailScreenProps {
  navigation: any;
  route: {
    params: {
      itemId: string;
    };
  };
}

interface StockByWarehouse extends InventoryStock {
  warehouseName: string;
  isDefault: boolean;
}

const InventoryItemDetailScreen: React.FC<InventoryItemDetailScreenProps> = ({
  navigation,
  route,
}) => {
  const theme = useTheme();
  const { state: dataState, actions } = useData();
  const getItemById = actions.getItemById;
  const getStockByItem = actions.getStockByItem;
  const getTransactionHistory = actions.getTransactionHistory;
  const deleteItem = actions.deleteInventoryItem;
  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const { itemId } = route.params;

  const [item, setItem] = useState<InventoryItem | null>(null);
  const [stockByWarehouse, setStockByWarehouse] = useState<StockByWarehouse[]>([]);
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [showFABGroup, setShowFABGroup] = useState<boolean>(false);

  // ActionSheet state
  const [actionSheetVisible, setActionSheetVisible] = useState<boolean>(false);

  // Load item data
  const loadItemData = useCallback(async () => {
    try {
      setLoading(true);

      // Load item details
      const itemData = await getItemById(itemId);
      if (!itemData) {
        showConfirmation('Error', 'Item not found', () => navigation.goBack(), {
          confirmText: 'OK',
          type: 'warning',
        });
        return;
      }
      setItem(itemData);

      // Load stock data
      const stockData = await getStockByItem(itemId);
      const stockWithWarehouseInfo: StockByWarehouse[] = stockData.map(stock => {
        const warehouse = ([] as any[]).find((w: any) => w.id === stock.warehouseId);
        return {
          ...stock,
          warehouseName: warehouse?.name || 'Unknown Warehouse',
          isDefault: warehouse?.isDefault || false,
        };
      });
      setStockByWarehouse(stockWithWarehouseInfo);

      // Load transaction history
      const transactionData = await getTransactionHistory({ itemId });
      setTransactions(transactionData as any);

      LoggingService.info(`Loaded item details: ${itemData.name}`, 'ITEM_DETAIL');
    } catch (error) {
      LoggingService.error('Failed to load item data', 'ITEM_DETAIL', error as Error);
      showConfirmation('Error', 'Failed to load item data', () => {}, {
        confirmText: 'OK',
        type: 'warning',
      });
    } finally {
      setLoading(false);
    }
  }, [
    itemId,
    getItemById,
    getStockByItem,
    getTransactionHistory,
    [], // state.warehouses,
    navigation,
    showConfirmation,
  ]);

  // Initial load
  useEffect(() => {
    loadItemData();
  }, [loadItemData]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadItemData();
    setRefreshing(false);
  }, [loadItemData]);

  // Calculate total stock
  const totalStock = useMemo(() => {
    return stockByWarehouse.reduce((total, stock) => {
      // Convert all stock to base unit for accurate total
      try {
        const convertedQuantity = UnitConverter.convertBetweenUnits(
          stock.quantity,
          stock.unit,
          item?.baseUnit || 'meter'
        );
        return total + convertedQuantity;
      } catch (error) {
        LoggingService.warn('Failed to convert stock unit', 'ITEM_DETAIL', error as Error);
        return total + stock.quantity; // Fallback to original quantity
      }
    }, 0);
  }, [stockByWarehouse, item?.baseUnit]);

  // Calculate profit margin
  const profitMargin = useMemo(() => {
    if (!item || item.sellingPrice <= 0) {return '0';}
    return (((item.sellingPrice - item.purchasePrice) / item.sellingPrice) * 100).toFixed(1);
  }, [item]);

  // Navigation handlers
  const handleEditItem = useCallback(() => {
    navigation.navigate('AddEditInventoryItem', {
      itemId: item?.id,
      mode: 'edit',
    });
  }, [navigation, item?.id]);

  const handleStockIn = useCallback(() => {
    navigation.navigate('StockOperations', {
      operation: 'in',
      itemId: item?.id,
    });
  }, [navigation, item?.id]);

  const handleStockOut = useCallback(() => {
    navigation.navigate('StockOperations', {
      operation: 'out',
      itemId: item?.id,
    });
  }, [navigation, item?.id]);

  const handleTransferStock = useCallback(() => {
    navigation.navigate('StockTransfer', {
      itemId: item?.id,
    });
  }, [navigation, item?.id]);

  const handleDeleteItem = useCallback(() => {
    if (!item) {return;}
    setActionSheetVisible(true);
  }, [item]);

  const handleDeleteItemConfirm = useCallback(async () => {
    if (!item) {return;}

    try {
      await deleteItem(item.id);
      LoggingService.info(`Item deleted: ${item.name}`, 'ITEM_DETAIL');
      showConfirmation('Success', 'Item deleted successfully', () => navigation.goBack(), {
        confirmText: 'OK',
        type: 'info',
      });
    } catch (error) {
      LoggingService.error('Failed to delete item', 'ITEM_DETAIL', error as Error);
      showConfirmation('Error', (error as Error).message, () => {}, {
        confirmText: 'OK',
        type: 'warning',
      });
    } finally {
      setActionSheetVisible(false);
    }
  }, [item, deleteItem, navigation, showConfirmation]);

  // Get category color
  const getCategoryColor = (category: string): string => {
    const colors = [
      theme.colors.primary,
      theme.colors.secondary,
      theme.colors.tertiary,
      '#F59E0B',
      '#10B981',
      '#8B5CF6',
      '#EF4444',
      '#06B6D4',
    ];

    let hash = 0;
    for (let i = 0; i < category.length; i++) {
      hash = category.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  if (loading || !item) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title='Item Details' onBackPress={() => navigation.goBack()} showBack={true} />
        <View style={styles.loadingContainer}>
          <Text style={{ color: theme.colors.onSurfaceVariant }}>Loading...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={item.name}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            icon: 'pencil',
            onPress: () =>
              navigation.navigate('AddEditInventoryItem', { itemId: item.id, mode: 'edit' }),
          },
          {
            icon: 'trash',
            onPress: handleDeleteItem,
            color: theme.colors.error,
          },
        ]}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Item Overview */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <View style={styles.itemHeader}>
                <View style={styles.itemInfo}>
                  <Text style={[styles.itemName, { color: theme.colors.onSurface }]}>
                    {item.name}
                  </Text>

                  <View style={styles.categoryContainer}>
                    <Chip
                      mode='outlined'
                      style={[
                        styles.categoryChip,
                        { borderColor: getCategoryColor(item.category) },
                      ]}
                      textStyle={{
                        color: getCategoryColor(item.category),
                        fontSize: 12,
                      }}
                    >
                      {item.category}
                    </Chip>

                    {!item.isActive && (
                      <Chip
                        mode='outlined'
                        style={[styles.statusChip, { borderColor: theme.colors.error }]}
                        textStyle={{
                          color: theme.colors.error,
                          fontSize: 12,
                        }}
                      >
                        Inactive
                      </Chip>
                    )}
                  </View>
                </View>

                {/* Item Image */}
                {item.imageUri && (
                  <View style={styles.imageContainer}>
                    <Image
                      source={{ uri: item.imageUri }}
                      style={styles.itemImage}
                      resizeMode='cover'
                    />
                  </View>
                )}
              </View>

              {/* Stock Level Indicator */}
              <StockLevelIndicator
                currentStock={totalStock}
                minimumLevel={item.minimumStockLevel}
                unit={item.baseUnit}
                size='large'
                showProgressBar={true}
                maxLevel={item.minimumStockLevel * 3}
              />
            </Card.Content>
          </Card>

          {/* Pricing Information */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Pricing Information
              </Text>

              <View style={styles.priceGrid}>
                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Purchase Price
                  </Text>
                  <Text style={[styles.priceValue, { color: theme.colors.onSurface }]}>
                    {formatCurrency(item.purchasePrice)}
                  </Text>
                </View>

                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Selling Price
                  </Text>
                  <Text style={[styles.priceValue, { color: theme.colors.primary }]}>
                    {formatCurrency(item.sellingPrice)}
                  </Text>
                </View>

                <View style={styles.priceItem}>
                  <Text style={[styles.priceLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Profit Margin
                  </Text>
                  <Text
                    style={[
                      styles.priceValue,
                      {
                        color:
                          parseFloat(profitMargin) > 0
                            ? '#10B981'
                            : parseFloat(profitMargin) < 0
                              ? theme.colors.error
                              : theme.colors.onSurfaceVariant,
                      },
                    ]}
                  >
                    {profitMargin}%
                  </Text>
                </View>
              </View>
            </Card.Content>
          </Card>

          {/* Units Information */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Units Configuration
              </Text>

              <View style={styles.unitInfo}>
                <View style={styles.unitRow}>
                  <Text style={[styles.unitLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Base Unit:
                  </Text>
                  <Text style={[styles.unitValue, { color: theme.colors.onSurface }]}>
                    {UnitConverter.getUnitDisplayName(item.baseUnit)}
                  </Text>
                </View>

                <View style={styles.availableUnitsContainer}>
                  <Text style={[styles.unitLabel, { color: theme.colors.onSurfaceVariant }]}>
                    Available Units:
                  </Text>
                  <View style={styles.unitsRow}>
                    {item.availableUnits.map((unit, index) => (
                      <Chip
                        key={unit}
                        mode='outlined'
                        compact
                        style={styles.unitChip}
                        textStyle={styles.unitChipText}
                      >
                        {UnitConverter.getUnitDisplayName(unit)}
                      </Chip>
                    ))}
                  </View>
                </View>

                {/* Unit Conversion Example */}
                {item.availableUnits.length > 1 && (
                  <ConversionPreview
                    quantity={1}
                    fromUnit={item.baseUnit}
                    toUnit={item.availableUnits.find(u => u !== item.baseUnit) || 'cm'}
                    showBothDirections={true}
                    compact={true}
                  />
                )}
              </View>
            </Card.Content>
          </Card>

          {/* Stock by Warehouse */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                  Stock by Warehouse
                </Text>
                <Text style={[styles.totalStockText, { color: theme.colors.primary }]}>
                  Total: {UnitConverter.formatQuantity(totalStock, item.baseUnit)}
                </Text>
              </View>

              {stockByWarehouse.length > 0 ? (
                <View style={styles.stockList}>
                  {stockByWarehouse.map(stock => (
                    <View key={stock.id} style={styles.stockItem}>
                      <View style={styles.stockItemHeader}>
                        <View style={styles.warehouseInfo}>
                          <Text style={[styles.warehouseName, { color: theme.colors.onSurface }]}>
                            {stock.warehouseName}
                          </Text>
                          {stock.isDefault && (
                            <Chip
                              mode='outlined'
                              compact
                              style={styles.defaultChip}
                              textStyle={styles.defaultChipText}
                            >
                              DEFAULT
                            </Chip>
                          )}
                        </View>

                        <Text
                          style={[
                            styles.stockQuantity,
                            {
                              color:
                                stock.quantity > 0
                                  ? theme.colors.primary
                                  : theme.colors.onSurfaceVariant,
                            },
                          ]}
                        >
                          {UnitConverter.formatQuantity(stock.quantity, stock.unit)}
                        </Text>
                      </View>

                      {stock.reservedQuantity > 0 && (
                        <View style={styles.reservedStock}>
                          <Text
                            style={[styles.reservedText, { color: theme.colors.onSurfaceVariant }]}
                          >
                            Reserved:{' '}
                            {UnitConverter.formatQuantity(stock.reservedQuantity, stock.unit)}
                          </Text>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              ) : (
                <View style={styles.noStockContainer}>
                  <PhosphorIcon name='package' size={32} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.noStockText, { color: theme.colors.onSurfaceVariant }]}>
                    No stock available in any warehouse
                  </Text>
                </View>
              )}
            </Card.Content>
          </Card>

          {/* Transaction History */}
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                  Transaction History
                </Text>
                {transactions.length > 5 && (
                  <Button
                    mode='text'
                    onPress={() => navigation.navigate('TransactionHistory', { itemId })}
                    compact
                  >
                    View All
                  </Button>
                )}
              </View>

              <TransactionHistoryList
                transactions={transactions}
                maxItems={5}
                showItemName={false}
                showWarehouseName={true}
                showPerformedBy={true}
                emptyMessage='No transactions found for this item'
              />
            </Card.Content>
          </Card>
        </View>
      </ScrollView>

      {/* Floating Action Button Group */}
      <Portal>
        <FAB.Group
          open={showFABGroup}
          visible={true}
          icon={showFABGroup ? 'close' : 'cog'}
          actions={[
            {
              icon: 'pencil',
              label: 'Edit Item',
              onPress: handleEditItem,
            },
            {
              icon: 'arrow-down-bold',
              label: 'Stock In',
              onPress: handleStockIn,
            },
            {
              icon: 'arrow-up-bold',
              label: 'Stock Out',
              onPress: handleStockOut,
            },
            {
              icon: 'swap-horizontal',
              label: 'Transfer',
              onPress: handleTransferStock,
            },
            {
              icon: 'delete',
              label: 'Delete Item',
              onPress: handleDeleteItem,
              color: theme.colors.error,
            },
          ]}
          onStateChange={({ open }) => setShowFABGroup(open)}
          fabStyle={{
            backgroundColor: theme.colors.primary,
          }}
        />
      </Portal>
      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={`Delete "${item?.name}"?`}
        description={`Are you sure you want to delete "${item?.name}"? This action cannot be undone.`}
        options={[
          {
            text: 'Delete',
            onPress: handleDeleteItemConfirm,
            style: 'destructive',
            isAction: true,
          },
          {
            text: 'Cancel',
            onPress: () => setActionSheetVisible(false),
            style: 'cancel',
            isAction: false,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
    paddingBottom: 100, // Space for FAB
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionCard: {
    marginVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  itemInfo: {
    flex: 1,
    marginRight: 16,
  },
  itemName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  categoryChip: {
    alignSelf: 'flex-start',
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  imageContainer: {
    width: 80,
    height: 80,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  priceGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceItem: {
    flex: 1,
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  unitInfo: {
    gap: 16,
  },
  unitRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  unitLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  unitValue: {
    fontSize: 14,
  },
  availableUnitsContainer: {
    gap: 8,
  },
  unitsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  unitChip: {
    height: 28,
  },
  unitChipText: {
    fontSize: 11,
  },
  totalStockText: {
    fontSize: 14,
    fontWeight: '600',
  },
  stockList: {
    gap: 12,
  },
  stockItem: {
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 8,
  },
  stockItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  warehouseInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  warehouseName: {
    fontSize: 14,
    fontWeight: '500',
  },
  defaultChip: {
    height: 20,
  },
  defaultChipText: {
    fontSize: 9,
  },
  stockQuantity: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  reservedStock: {
    marginTop: 4,
  },
  reservedText: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  noStockContainer: {
    alignItems: 'center',
    paddingVertical: 24,
    gap: 8,
  },
  noStockText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default InventoryItemDetailScreen;
