import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, View, FlatList, Pressable } from 'react-native';
import { <PERSON>ton, Card, Chip, HelperText, Text, TextInput } from 'react-native-paper';

import Search from '../../components/forms/Search';
import {
  ConversionPreview,
  InventoryCard,
  UnitQuantityInput,
  WarehouseSelector,
} from '../../components/inventory';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { ActionSheetOption, Product } from '../../types';
import { InventoryItem, Warehouse } from '../../types/inventory';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface StockTransferScreenProps {
  navigation: any;
  route?: {
    params?: {
      itemId?: string;
      fromWarehouseId?: string;
      toWarehouseId?: string;
    };
  };
}

interface FormData {
  selectedItem: InventoryItem | null;
  fromWarehouse: Warehouse | null;
  toWarehouse: Warehouse | null;
  quantity: number;
  unit: string;
  note: string;
}

interface FormErrors {
  item?: string;
  fromWarehouse?: string;
  toWarehouse?: string;
  quantity?: string;
  general?: string;
}

interface StockInfo {
  fromStock: number;
  toStock: number;
}

const StockTransferScreen: React.FC<StockTransferScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state: dataState, actions } = useData();
  const transferStock = (actions as any).transferStock;
  const getStockByItem = actions.getStockByItem;

  const initialItemId = route?.params?.itemId;
  const initialFromWarehouseId = route?.params?.fromWarehouseId;
  const initialToWarehouseId = route?.params?.toWarehouseId;

  // Form state
  const [formData, setFormData] = useState<FormData>({
    selectedItem: null,
    fromWarehouse: null,
    toWarehouse: null,
    quantity: 0,
    unit: 'meter',
    note: '',
  });

  const [itemSearchQuery, setItemSearchQuery] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [stockInfo, setStockInfo] = useState<StockInfo>({ fromStock: 0, toStock: 0 });
  const [showItemSearch, setShowItemSearch] = useState<boolean>(false);

  const [showSuccessActionSheet, setShowSuccessActionSheet] = useState(false);
  const [showErrorActionSheet, setShowErrorActionSheet] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        { text: options?.confirmText || 'Confirm', onPress: () => { onConfirm(); setConfirmationActionSheetVisible(false); }, style: options?.type === 'danger' ? 'destructive' : 'primary', isAction: true },
        { text: options?.cancelText || 'Cancel', onPress: () => setConfirmationActionSheetVisible(false), style: 'cancel', isAction: false },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  useEffect(() => {
    if (initialItemId) {
      const item = dataState.products.find(i => i.id === initialItemId);
      if (item) {setFormData(prev => ({ ...prev, selectedItem: item as any, unit: item.baseUnit || '' }));}
    }
    if (initialFromWarehouseId) {
      const warehouse = dataState.warehouses.find(w => w.id === initialFromWarehouseId);
      if (warehouse) {setFormData(prev => ({ ...prev, fromWarehouse: warehouse }));}
    }
    if (initialToWarehouseId) {
      const warehouse = dataState.warehouses.find(w => w.id === initialToWarehouseId);
      if (warehouse) {setFormData(prev => ({ ...prev, toWarehouse: warehouse }));}
    }
  }, [initialItemId, initialFromWarehouseId, initialToWarehouseId, dataState.products, dataState.warehouses]);

  useEffect(() => {
    const loadStockInfo = async () => {
      if (formData.selectedItem && (formData.fromWarehouse || formData.toWarehouse)) {
        try {
          const stockData = await getStockByItem(formData.selectedItem.id);
          const fromStock = formData.fromWarehouse ? stockData.find(s => s.warehouseId === formData.fromWarehouse!.id)?.quantity || 0 : 0;
          const toStock = formData.toWarehouse ? stockData.find(s => s.warehouseId === formData.toWarehouse!.id)?.quantity || 0 : 0;
          setStockInfo({ fromStock, toStock });
        } catch (error) {
          LoggingService.error('Failed to load stock info', 'STOCK_TRANSFER', error as Error);
          setStockInfo({ fromStock: 0, toStock: 0 });
        }
      }
    };
    loadStockInfo();
  }, [formData.selectedItem, formData.fromWarehouse, formData.toWarehouse, getStockByItem]);

  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};
    if (!formData.selectedItem) {newErrors.item = 'Please select an item to transfer';}
    if (!formData.fromWarehouse) {newErrors.fromWarehouse = 'Please select source warehouse';}
    if (!formData.toWarehouse) {newErrors.toWarehouse = 'Please select destination warehouse';}
    if (formData.fromWarehouse && formData.toWarehouse && formData.fromWarehouse.id === formData.toWarehouse.id) {
      newErrors.toWarehouse = 'Source and destination warehouses must be different';
    }
    if (formData.quantity <= 0) {newErrors.quantity = 'Transfer quantity must be greater than 0';}
    if (formData.quantity > stockInfo.fromStock) {
      newErrors.quantity = `Insufficient stock. Available: ${stockInfo.fromStock} ${formData.unit}`;
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, stockInfo.fromStock]);

  const filteredItems = useMemo(() => {
    const query = itemSearchQuery.toLowerCase().trim();
    if (!query) {
      return dataState.products.slice(0, 10);
    }
    return dataState.products.filter(item =>
      item.name.toLowerCase().includes(query) ||
      item.category?.toLowerCase().includes(query)
    );
  }, [itemSearchQuery, dataState.products]);

  const handleItemSelect = useCallback((item: Product) => {
    setFormData(prev => ({ ...prev, selectedItem: item as InventoryItem, unit: item.baseUnit }));
    setShowItemSearch(false);
    setItemSearchQuery('');
    setErrors(prev => ({ ...prev, item: undefined }));
  }, []);

  const handleFromWarehouseSelect = useCallback((warehouse: Warehouse) => {
    setFormData(prev => ({ ...prev, fromWarehouse: warehouse, toWarehouse: prev.toWarehouse?.id === warehouse.id ? null : prev.toWarehouse }));
    setErrors(prev => ({ ...prev, fromWarehouse: undefined, toWarehouse: undefined }));
  }, []);

  const handleToWarehouseSelect = useCallback((warehouse: Warehouse) => {
    setFormData(prev => ({ ...prev, toWarehouse: warehouse }));
    setErrors(prev => ({ ...prev, toWarehouse: undefined }));
  }, []);

  const handleQuantityChange = useCallback((quantity: number) => {
    setFormData(prev => ({ ...prev, quantity }));
    setErrors(prev => ({ ...prev, quantity: undefined }));
  }, []);

  const handleUnitChange = useCallback((unit: string) => {
    setFormData(prev => ({ ...prev, unit }));
  }, []);

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {return;}
    try {
      setLoading(true);
      const { selectedItem, fromWarehouse, toWarehouse, quantity, unit, note } = formData;
      const result = await transferStock(selectedItem!.id, fromWarehouse!.id, toWarehouse!.id, quantity, unit, 'Current User', note || undefined);
      if (result.success) {
        setSuccessMessage(result.message);
        setShowSuccessActionSheet(true);
      } else {
        setErrorMessage(result.error || 'Transfer failed');
        setShowErrorActionSheet(true);
      }
    } catch (error) {
      setErrorMessage((error as Error).message);
      setShowErrorActionSheet(true);
    } finally {
      setLoading(false);
    }
  }, [formData, validateForm, transferStock]);

  const stockAfterTransfer = useMemo(() => {
    if (formData.quantity <= 0) {return null;}
    return {
      fromStock: stockInfo.fromStock - formData.quantity,
      toStock: stockInfo.toStock + formData.quantity,
    };
  }, [stockInfo, formData.quantity]);

  const availableToWarehouses = useMemo(
    () => dataState.warehouses.filter(w => w.id !== formData.fromWarehouse?.id),
    [dataState.warehouses, formData.fromWarehouse]
  );

  return (
    <View style={styles.container}>
      <Header
        title='Stock Transfer'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ text: 'Transfer', onPress: handleSubmit, disabled: loading, color: theme.colors.primary }]}
      />
      <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <View style={styles.transferHeader}>
                <PhosphorIcon name='arrows-left-right' size={24} color={theme.colors.primary} />
                <Text style={[styles.transferTitle, { color: theme.colors.primary }]}>Stock Transfer</Text>
              </View>
              <Text style={[styles.transferDescription, { color: theme.colors.onSurfaceVariant }]}>
                Move inventory items from one warehouse to another.
              </Text>
            </Card.Content>
          </Card>

          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Select Item to Transfer</Text>
              {formData.selectedItem ? (
                <View>
                  <InventoryCard
                    item={formData.selectedItem}
                    stockLevel={stockInfo.fromStock + stockInfo.toStock}
                    onPress={() => setShowItemSearch(true)}
                    showStockLevel={true}
                    compact={true}
                  />
                  <Button mode='outlined' onPress={() => setShowItemSearch(true)} style={styles.changeButton} icon='swap-horizontal'>
                    Change Item
                  </Button>
                </View>
              ) : (
                <Button mode='outlined' onPress={() => setShowItemSearch(true)} style={styles.selectButton} icon='magnify'>
                  Select Item
                </Button>
              )}
              {errors.item && <HelperText type='error'>{errors.item}</HelperText>}
              {/* FIX: The search UI is no longer rendered here */}
            </Card.Content>
          </Card>

          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Select Warehouses</Text>
              <View style={styles.warehouseSection}>
                <Text style={[styles.warehouseLabel, { color: theme.colors.onSurface }]}>From Warehouse (Source)</Text>
                <WarehouseSelector warehouses={dataState.warehouses} selectedWarehouseId={formData.fromWarehouse?.id || null} onWarehouseSelect={handleFromWarehouseSelect} placeholder='Select source warehouse' error={errors.fromWarehouse} compact={true} />
                {formData.fromWarehouse && formData.selectedItem && (
                  <View style={styles.stockDisplay}>
                    <Text style={[styles.stockLabel, { color: theme.colors.onSurfaceVariant }]}>Available Stock:</Text>
                    <Chip mode='outlined' style={[styles.stockChip, { borderColor: stockInfo.fromStock > 0 ? theme.colors.primary : theme.colors.error, backgroundColor: stockInfo.fromStock > 0 ? `${theme.colors.primary}15` : `${theme.colors.error}15` }]} textStyle={{ color: stockInfo.fromStock > 0 ? theme.colors.primary : theme.colors.error, fontWeight: '600' }}>
                      {stockInfo.fromStock} {formData.unit}
                    </Chip>
                  </View>
                )}
              </View>
              <View style={styles.transferArrow}>
                <PhosphorIcon name='arrow-down' size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.warehouseSection}>
                <Text style={[styles.warehouseLabel, { color: theme.colors.onSurface }]}>To Warehouse (Destination)</Text>
                <WarehouseSelector warehouses={availableToWarehouses} selectedWarehouseId={formData.toWarehouse?.id || null} onWarehouseSelect={handleToWarehouseSelect} placeholder='Select destination warehouse' error={errors.toWarehouse} compact={true} />
                {formData.toWarehouse && formData.selectedItem && (
                  <View style={styles.stockDisplay}>
                    <Text style={[styles.stockLabel, { color: theme.colors.onSurfaceVariant }]}>Current Stock:</Text>
                    <Chip mode='outlined' style={[styles.stockChip, { borderColor: theme.colors.primary, backgroundColor: `${theme.colors.primary}15` }]} textStyle={{ color: theme.colors.primary, fontWeight: '600' }}>
                      {stockInfo.toStock} {formData.unit}
                    </Chip>
                  </View>
                )}
              </View>
            </Card.Content>
          </Card>

          {formData.selectedItem && formData.fromWarehouse && formData.toWarehouse && (
            <Card style={styles.sectionCard} mode='outlined'>
              <Card.Content>
                <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Transfer Quantity</Text>
                <UnitQuantityInput value={formData.quantity} unit={formData.unit} availableUnits={formData.selectedItem.availableUnits} onQuantityChange={handleQuantityChange} onUnitChange={handleUnitChange} label='Quantity to Transfer' placeholder='Enter transfer quantity' error={errors.quantity} showConversion={true} baseUnit={formData.selectedItem.baseUnit} maxValue={stockInfo.fromStock} />
                {formData.quantity > 0 && formData.unit !== formData.selectedItem.baseUnit && (<ConversionPreview quantity={formData.quantity} fromUnit={formData.unit} toUnit={formData.selectedItem.baseUnit} showCalculation={true} />)}
                {stockAfterTransfer && (
                  <View style={styles.stockPreview}>
                    <Text style={[styles.stockPreviewTitle, { color: theme.colors.onSurface }]}>Stock Levels After Transfer:</Text>
                    <View style={styles.stockPreviewRow}>
                      <View style={styles.stockPreviewItem}>
                        <Text style={[styles.stockPreviewLabel, { color: theme.colors.onSurfaceVariant }]}>{formData.fromWarehouse.name}:</Text>
                        <Text style={[styles.stockPreviewValue, { color: stockAfterTransfer.fromStock >= 0 ? theme.colors.onSurface : '#EF4444' }]}>{stockAfterTransfer.fromStock} {formData.unit}</Text>
                      </View>
                      <View style={styles.stockPreviewItem}>
                        <Text style={[styles.stockPreviewLabel, { color: theme.colors.onSurfaceVariant }]}>{formData.toWarehouse.name}:</Text>
                        <Text style={[styles.stockPreviewValue, { color: theme.colors.primary }]}>{stockAfterTransfer.toStock} {formData.unit}</Text>
                      </View>
                    </View>
                  </View>
                )}
              </Card.Content>
            </Card>
          )}

          <Card style={styles.sectionCard} mode='outlined'>
            <Card.Content>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Transfer Notes (Optional)</Text>
              <TextInput label='Notes' value={formData.note} onChangeText={text => setFormData(prev => ({ ...prev, note: text }))} mode='outlined' multiline numberOfLines={3} style={styles.input} placeholder='Add any notes about this transfer...' />
            </Card.Content>
          </Card>

          {errors.general && (<HelperText type='error' style={styles.generalError}>{errors.general}</HelperText>)}
          <View style={styles.actionButtons}>
            <Button mode='outlined' onPress={() => navigation.goBack()} style={styles.cancelButton} disabled={loading}>Cancel</Button>
            <Button mode='contained' onPress={handleSubmit} loading={loading} disabled={loading || !formData.selectedItem || !formData.fromWarehouse || !formData.toWarehouse || formData.quantity <= 0} style={[styles.transferButton, { backgroundColor: theme.colors.primary }]} icon={() => <PhosphorIcon name='arrows-left-right' size={24} color={theme.colors.onPrimary} />}>
              Transfer Stock
            </Button>
          </View>
        </View>
      </ScrollView>

      {/* FIX: The Search UI is now rendered here, outside and on top of the ScrollView */}
      {showItemSearch && (
        <View style={styles.searchOverlay}>
          <View style={styles.searchContainer}>
             <View style={styles.searchHeader}>
                <Text style={styles.searchTitle}>Select an Item</Text>
                <Pressable onPress={() => setShowItemSearch(false)}>
                  <PhosphorIcon name="x" size={24} color={theme.colors.onSurface} />
                </Pressable>
            </View>
            <Search
              placeholder='Search inventory items...'
              onSearchChange={setItemSearchQuery}
            />
            <FlatList
              data={filteredItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <Pressable style={styles.searchResultItem} onPress={() => handleItemSelect(item)}>
                  <Text>{item.name}</Text>
                  <Text style={{ color: theme.colors.onSurfaceVariant }}>{item.category}</Text>
                </Pressable>
              )}
              style={styles.searchResultsList}
            />
          </View>
        </View>
      )}

      <ActionSheet visible={showSuccessActionSheet} onDismiss={() => setShowSuccessActionSheet(false)} title='Success' description={successMessage} options={[{ text: 'OK', onPress: () => { setShowSuccessActionSheet(false); navigation.goBack(); }, isAction: true }]} />
      <ActionSheet visible={showErrorActionSheet} onDismiss={() => setShowErrorActionSheet(false)} title='Error' description={errorMessage} options={[{ text: 'OK', onPress: () => setShowErrorActionSheet(false), isAction: true }]} />
    </View>
  );
};

// FIX: Added new styles for the search overlay
const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContent: { flex: 1 },
  content: { padding: SPACING.lg, paddingBottom: 60 },
  sectionCard: { marginVertical: 8 },
  sectionTitle: { fontSize: 16, fontWeight: '600', marginBottom: 16 },
  transferHeader: { flexDirection: 'row', alignItems: 'center', gap: 12, marginBottom: 12 },
  transferTitle: { fontSize: 18, fontWeight: 'bold' },
  transferDescription: { fontSize: 14, lineHeight: 20 },
  selectButton: { marginVertical: 8 },
  changeButton: { marginTop: 12 },
  searchOverlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0, 0, 0, 0.5)', zIndex: 10, justifyContent: 'flex-start', paddingTop: 60 },
  searchContainer: { backgroundColor: 'white', margin: SPACING.lg, borderRadius: 12, padding: SPACING.lg, maxHeight: '80%' },
  searchHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: SPACING.md },
  searchTitle: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: 'bold' },
  searchResultsList: { marginTop: 8 },
  searchResultItem: { paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: 'rgba(0,0,0,0.05)' },
  warehouseSection: { marginVertical: 12 },
  warehouseLabel: { fontSize: 14, fontWeight: '500', marginBottom: 8 },
  stockDisplay: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 8 },
  stockLabel: { fontSize: 12 },
  stockChip: { alignSelf: 'flex-start' },
  transferArrow: { alignItems: 'center', paddingVertical: 16 },
  stockPreview: { marginTop: 16, padding: 16, backgroundColor: 'rgba(0,0,0,0.02)', borderRadius: 8 },
  stockPreviewTitle: { fontSize: 14, fontWeight: '600', marginBottom: 12 },
  stockPreviewRow: { gap: 8 },
  stockPreviewItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  stockPreviewLabel: { fontSize: 13 },
  stockPreviewValue: { fontSize: 14, fontWeight: 'bold' },
  input: { marginVertical: 4 },
  generalError: { textAlign: 'center', marginVertical: 8 },
  actionButtons: { flexDirection: 'row', gap: 12, marginTop: 24, marginBottom: 32 },
  cancelButton: { flex: 1 },
  transferButton: { flex: 1 },
});

export default StockTransferScreen;