import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { ActivityIndicator, <PERSON>ton, Card, Chip, Text } from 'react-native-paper';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { ActionSheetOption } from '../../types';

// Helper function moved outside the component to prevent recreation on renders
const getStatusStyle = (status: string, theme: ReturnType<typeof useTheme>) => {
  switch (status) {
    case 'healthy':
    case 'good':
      return { color: theme.colors.primary, icon: '✓' };
    case 'warning':
      return { color: '#FFA500', icon: '⚠' };
    case 'error':
    case 'poor':
      return { color: theme.colors.error, icon: '✗' };
    default:
      return { color: theme.colors.outline, icon: '?' };
  }
};

// Define types for clarity
interface SystemStatus {
  database: { status: 'healthy' | 'warning' | 'error'; message: string };
  performance: { fps: number; memoryUsage: number; status: 'good' | 'warning' | 'poor' };
  lastUpdated: string;
}

interface ConfirmationState {
  visible: boolean;
  title: string;
  description: string;
  options: ActionSheetOption[];
}

// Memoized component for list items to optimize FlatList performance
const StatusCard = React.memo(({ title, content }: { title: string; content: React.ReactNode }) => (
  <Card style={styles.card}>
    <Card.Title title={title} />
    <Card.Content>{content}</Card.Content>
  </Card>
));

const AppStatusScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const theme = useTheme();
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Consolidated state for the action sheet
  const [confirmationState, setConfirmationState] = useState<ConfirmationState>({
    visible: false,
    title: '',
    description: '',
    options: [],
  });

  const hideConfirmation = useCallback(() => setConfirmationState(prev => ({ ...prev, visible: false })), []);

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void) => {
      setConfirmationState({
        visible: true,
        title,
        description: message,
        options: [
          { text: 'Confirm', onPress: () => { onConfirm(); hideConfirmation(); }, style: 'primary', isAction: true },
          { text: 'Cancel', onPress: hideConfirmation, style: 'cancel', isAction: false },
        ],
      });
    },
    [hideConfirmation]
  );

  const checkSystemStatus = useCallback(async () => {
    try {
      // Mock data fetching
      const newStatus: SystemStatus = {
        database: { status: 'warning', message: 'Using AsyncStorage fallback (SQLite disabled)' },
        performance: { fps: Math.floor(Math.random() * 20) + 50, memoryUsage: Math.floor(Math.random() * 20) + 40, status: 'good' },
        lastUpdated: new Date().toLocaleTimeString(),
      };
      setStatus(newStatus);
    } catch (error) {
      LoggingService.error('System status check failed', 'APP_STATUS', error as Error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    checkSystemStatus();
  }, [checkSystemStatus]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    checkSystemStatus();
  }, [checkSystemStatus]);

  // Memoized data array to prevent recreation on every render
  const statusData = useMemo(() => {
    if (!status) {return [];}

    const dbStatusStyle = getStatusStyle(status.database.status, theme);
    const perfStatusStyle = getStatusStyle(status.performance.status, theme);

    return [
      {
        title: 'Database Status',
        content: (
          <View style={styles.statusRow}>
            <Text style={[styles.statusIcon, { color: dbStatusStyle.color }]}>{dbStatusStyle.icon}</Text>
            <View style={styles.statusInfo}>
              <Text style={[styles.statusText, { color: theme.colors.onBackground }]}>{status.database.message}</Text>
              <Chip mode='outlined' style={[styles.chip, { borderColor: dbStatusStyle.color }]} textStyle={{ color: dbStatusStyle.color }}>
                AsyncStorage Mode
              </Chip>
            </View>
          </View>
        ),
      },
      {
        title: 'Performance Status',
        content: (
          <View style={styles.statusRow}>
            <Text style={[styles.statusIcon, { color: perfStatusStyle.color }]}>{perfStatusStyle.icon}</Text>
            <View style={styles.statusInfo}>
              <Text style={[styles.statusText, { color: theme.colors.onBackground }]}>
                FPS: {status.performance.fps} | Memory: {status.performance.memoryUsage}MB
              </Text>
              <Chip mode='outlined' style={[styles.chip, { borderColor: perfStatusStyle.color }]} textStyle={{ color: perfStatusStyle.color }}>
                {status.performance.status.charAt(0).toUpperCase() + status.performance.status.slice(1)}
              </Chip>
            </View>
          </View>
        ),
      },
      {
        title: 'Actions',
        content: (
          <View>
            <Text style={[styles.infoText, { color: theme.colors.outline }]}>
              Note: SQLite is disabled. The app is using AsyncStorage as a fallback.
            </Text>
            <Button mode='contained' onPress={onRefresh} style={styles.actionButton} loading={refreshing}>
              Refresh Status
            </Button>
            <Button
              mode='outlined'
              style={styles.actionButton}
              onPress={() => {
                showConfirmation(
                  'Reset Database?',
                  'This will clear all local data and cannot be undone. Are you sure?',
                  () => {
                    LoggingService.info('Database reset confirmed by user.', 'APP_STATUS');
                    // TODO: Place your actual data clearing logic here.
                    // For example: await AsyncStorage.clear();
                    onRefresh(); // Refresh the status screen after action.
                  }
                );
              }}
            >
              Reset Database
            </Button>
          </View>
        ),
      },
    ];
  }, [status, theme, refreshing, onRefresh, showConfirmation]);

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title='App Status' showBack onBackPress={() => navigation.goBack()} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>Checking system status...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='App Status' showBack onBackPress={() => navigation.goBack()} />
      <FlatList
        data={statusData}
        renderItem={({ item }) => <StatusCard title={item.title} content={item.content} />}
        keyExtractor={item => item.title}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListFooterComponent={<Text style={[styles.lastUpdated, { color: theme.colors.outline }]}>Last updated: {status?.lastUpdated}</Text>}
      />
      <ActionSheet
        visible={confirmationState.visible}
        onDismiss={hideConfirmation}
        title={confirmationState.title}
        description={confirmationState.description}
        options={confirmationState.options}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  list: { flex: 1 },
  listContent: { padding: 16 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  loadingText: { marginTop: 16, fontSize: 16 },
  card: { marginBottom: 16 },
  statusRow: { flexDirection: 'row', alignItems: 'flex-start' },
  statusIcon: { fontSize: 24, marginRight: 12, marginTop: 2 },
  statusInfo: { flex: 1 },
  statusText: { fontSize: 16, marginBottom: 8 },
  chip: { alignSelf: 'flex-start' },
  actionButton: { marginTop: 8 },
  lastUpdated: { textAlign: 'center', fontSize: 12, marginTop: 16, paddingBottom: 8 },
  infoText: { fontSize: 14, lineHeight: 20, marginBottom: 8 },
});

export default AppStatusScreen;