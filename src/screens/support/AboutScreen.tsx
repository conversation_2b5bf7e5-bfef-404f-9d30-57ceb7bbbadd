import React, { useMemo, useState, useCallback } from 'react';
import { <PERSON>ing, ScrollView, StyleSheet, View } from 'react-native';
import { Card, List, Text, Portal } from 'react-native-paper';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useTheme } from '../../context/ThemeContext';
import { commonStyles } from '../../theme/commonStyles';
import { SPACING, BORDER_RADIUS } from '../../theme/theme';
import { AboutScreenProps } from '../../types/navigation';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

const appInfo = {
  name: 'TailorZap',
  version: '1.0.0',
  build: '2024.1.0',
  description: 'Complete tailoring business management solution',
  company: 'TailorZap Inc.',
  website: 'https://TailorZap.com',
  email: '<EMAIL>',
  phone: '+****************',
};

interface ListItem {
  id: string;
  title: string;
  description: string;
  icon: PhosphorIconName;
  action?: () => void;
}

const sections: { title: string; items: ListItem[] }[] = [
  {
    title: 'Contact Us',
    items: [
      { id: 'email', title: 'Email Support', description: appInfo.email, icon: 'envelope', action: () => Linking.openURL(`mailto:${appInfo.email}`) },
      { id: 'phone', title: 'Phone Support', description: appInfo.phone, icon: 'phone', action: () => Linking.openURL(`tel:${appInfo.phone}`) },
      { id: 'website', title: 'Website', description: appInfo.website, icon: 'globe', action: () => Linking.openURL(appInfo.website) },
    ],
  },
  {
    title: 'Legal',
    items: [
      { id: 'privacy', title: 'Privacy Policy', description: 'How we protect your data', icon: 'shield' },
      { id: 'terms', title: 'Terms of Service', description: 'Our terms and conditions', icon: 'file' },
    ],
  },
];

const allItemsById = new Map<string, ListItem>(
  sections
    .reduce((acc: ListItem[], section) => acc.concat(section.items), [])
    .map((item: ListItem) => [item.id, item])
);

const AboutScreen: React.FC<AboutScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<ListItem | null>(null);

  const openSheet = useCallback((id: string) => {
    const item = allItemsById.get(id);
    if (item) {
      setCurrentItem(item);
      setActionSheetVisible(true);
    }
  }, []);

  const closeSheet = useCallback(() => {
    setActionSheetVisible(false);
    setCurrentItem(null);
  }, []);

  // Stable callback handlers
  const handleEmailAction = useCallback(async () => {
    await Linking.openURL(`mailto:${appInfo.email}`);
  }, []);

  const handlePhoneAction = useCallback(async () => {
    await Linking.openURL(`tel:${appInfo.phone}`);
  }, []);

  const handleWebsiteAction = useCallback(async () => {
    await Linking.openURL(appInfo.website);
  }, []);

  // Memoized ActionSheet config
  const actionSheetConfig = useMemo(() => {
    if (!currentItem) return null;

    const configs = {
      email: {
        title: 'Contact via Email',
        description: 'Would you like to send us an email?',
        options: [
          {
            text: 'Send Email',
            onPress: handleEmailAction,
            style: 'primary' as const,
            icon: 'envelope' as PhosphorIconName,
          }
        ]
      },
      phone: {
        title: 'Contact via Phone',
        description: 'Would you like to call us?',
        options: [
          {
            text: 'Call Now',
            onPress: handlePhoneAction,
            style: 'primary' as const,
            icon: 'phone' as PhosphorIconName,
          }
        ]
      },
      website: {
        title: 'Visit Website',
        description: 'Would you like to visit our website?',
        options: [
          {
            text: 'Open Website',
            onPress: handleWebsiteAction,
            style: 'primary' as const,
            icon: 'globe' as PhosphorIconName,
          }
        ]
      },
      privacy: {
        title: 'Privacy Policy',
        description: 'We value your privacy and protect your personal information according to industry standards. Our privacy policy outlines how we collect, use, and safeguard your data.',
        options: [
          {
            text: 'Got it',
            onPress: async () => {}, // No action needed
            style: 'primary' as const,
          }
        ]
      },
      terms: {
        title: 'Terms of Service',
        description: 'By using TailorZap, you agree to our terms of service. These terms outline your rights and responsibilities when using our application.',
        options: [
          {
            text: 'Understood',
            onPress: async () => {}, // No action needed
            style: 'primary' as const,
          }
        ]
      }
    };

    return configs[currentItem.id as keyof typeof configs] || null;
  }, [currentItem, handleEmailAction, handlePhoneAction, handleWebsiteAction]);
  
  const renderListItemLeft = useCallback(
    (iconName: PhosphorIconName) => (
      <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
        <PhosphorIcon name={iconName} size={20} color={theme.colors.primary} />
      </View>
    ),
    [theme.colors.primary]
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='About' showBack onBackPress={navigation.goBack} />
      <ScrollView style={commonStyles.flex1} showsVerticalScrollIndicator={false}>
        <Card style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.headerContent}>
            <View style={[styles.logoContainer, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name='scissors' size={48} color={theme.colors.primary} />
            </View>
            <Text variant='headlineSmall' style={[styles.appName, { color: theme.colors.onSurface }]}>
              {appInfo.name}
            </Text>
            <Text variant='bodyMedium' style={[styles.version, { color: theme.colors.onSurfaceVariant }]}>
              Version {appInfo.version} (Build {appInfo.build})
            </Text>
            <Text variant='bodyMedium' style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
              {appInfo.description}
            </Text>
          </View>
        </Card>

        {sections.map(section => (
          <Card key={section.title} style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
            <Text variant='titleMedium' style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              {section.title}
            </Text>
            {section.items.map(item => (
              <List.Item
                key={item.id}
                title={item.title}
                description={item.description}
                left={() => renderListItemLeft(item.icon)}
                onPress={() => openSheet(item.id)}
                style={styles.contactItem}
              />
            ))}
          </Card>
        ))}

        <View style={styles.footer}>
          <Text variant='bodySmall' style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
            © {new Date().getFullYear()} {appInfo.company}. All rights reserved.
          </Text>
          <Text variant='bodySmall' style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
            Made with ❤️ for tailors worldwide
          </Text>
        </View>
      </ScrollView>

      {actionSheetConfig && (
        <Portal>
          <ActionSheet
            visible={actionSheetVisible}
            onDismiss={closeSheet}
            title={actionSheetConfig.title}
            description={actionSheetConfig.description}
            options={actionSheetConfig.options}
            showCancel={true}
            cancelText="Cancel"
            closeOnBackdropPress={true}
          />
        </Portal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  headerContent: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  appName: {
    fontWeight: '700',
    marginBottom: SPACING.xs,
  },
  version: {
    marginBottom: SPACING.sm,
  },
  description: {
    textAlign: 'center',
    lineHeight: 20,
  },
  section: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.lg,
  },
  contactItem: {
    paddingVertical: SPACING.xs,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    padding: SPACING.lg,
    alignItems: 'center',
  },
  footerText: {
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
});

export default AboutScreen;