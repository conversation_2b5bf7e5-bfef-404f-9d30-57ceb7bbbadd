import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, TextInput, FlatList, Keyboard } from 'react-native';
import { Text, Card, Button, ActivityIndicator } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// Type definitions for better type safety
interface SearchResult {
  id?: string;
  name?: string;
  phone?: string;
  email?: string;
  orderNumber?: string;
  customerId?: string;
  customerName?: string;
  status?: string;
  category?: string;
  description?: string;
  title?: string;
  type: 'customers' | 'orders' | 'products' | 'history' | 'suggestion';
  query?: string;
}

const ICON_MAP: Record<string, PhosphorIconName> = {
  customers: 'users',
  orders: 'clipboard-text',
  products: 'package',
  history: 'clock',
  suggestion: 'magnifying-glass',
};

const DEBOUNCE_DELAY = 300;
const MIN_SEARCH_LENGTH = 2;
const MAX_HISTORY_ITEMS = 10;
const MAX_SUGGESTIONS = 5;

const SearchScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const { state } = useData();
  const { orders, customers, products } = state;

  // Search state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [showHistory, setShowHistory] = useState<boolean>(true);
  const [searchHistory, setSearchHistory] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<SearchResult[]>([]);

  // Refs
  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const historyCache = useRef<string[]>([]);

  // --- FILTER-RELATED CODE REMOVED ---

  // Load search history from storage
  const loadSearchHistory = useCallback(async () => {
    try {
      const history = await AsyncStorage.getItem('searchHistory');
      const historyArray = history ? JSON.parse(history) : [];
      historyCache.current = historyArray;
      setSearchHistory(historyArray.map((item: string) => ({ query: item, type: 'history' as const })));
    } catch (error)      {
      LoggingService.error('Failed to load search history', 'SEARCH_SCREEN', error as Error);
    }
  }, []);

  useEffect(() => {
    loadSearchHistory();
  }, [loadSearchHistory]);

  // Optimized search function
  const performSearch = useCallback(async (query: string) => {
    if (!query.trim()) {return;}

    setIsSearching(true);
    setShowHistory(false);

    try {
      const results: SearchResult[] = [];
      const searchTerm = query.toLowerCase();

      // --- CHANGE: The logic now always searches across all data types ---
      if (customers) {
          const customerResults = customers
              .filter(customer =>
                customer.name?.toLowerCase().includes(searchTerm) ||
                customer.phone?.includes(searchTerm) ||
                customer.email?.toLowerCase().includes(searchTerm)
              )
              .map(customer => ({ ...customer, type: 'customers' as const }));
          results.push(...customerResults);
      }

      if (orders) {
          const orderResults = orders
              .filter(order =>
                order.id?.includes(searchTerm) ||
                order.customerId?.toLowerCase().includes(searchTerm) ||
                order.status?.toLowerCase().includes(searchTerm)
              )
              .map(order => ({ ...order, type: 'orders' as const }));
          results.push(...orderResults);
      }

      if (products) {
          const productResults = products
              .filter(product =>
                product.name?.toLowerCase().includes(searchTerm) ||
                product.category?.toLowerCase().includes(searchTerm) ||
                product.description?.toLowerCase().includes(searchTerm)
              )
              .map(product => ({ ...product, type: 'products' as const }));
          results.push(...productResults);
      }

      setSearchResults(results);
    } catch (error) {
      LoggingService.error('Search failed', 'SEARCH_SCREEN', error as Error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [customers, orders, products]);

  // Get search suggestions - optimized with cache
  const getSuggestions = useCallback((query: string) => {
    const lowerQuery = query.toLowerCase();
    const suggestions = historyCache.current
      .filter(item => item.toLowerCase().includes(lowerQuery))
      .slice(0, MAX_SUGGESTIONS)
      .map(suggestion => ({ query: suggestion, type: 'suggestion' as const }));
    setSuggestions(suggestions);
  }, []);

  // Handle search query change
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    const trimmedQuery = query.trim();
    
    if (trimmedQuery.length === 0) {
      setSearchResults([]);
      setShowHistory(true);
      setSuggestions([]);
      return;
    }

    if (trimmedQuery.length < MIN_SEARCH_LENGTH) {
      setShowHistory(false);
      return;
    }

    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query);
    }, DEBOUNCE_DELAY);

    getSuggestions(query);
  }, [performSearch, getSuggestions]);
  
  // --- FILTER-RELATED CODE REMOVED ---

  // Add to search history - optimized
  const addToSearchHistory = useCallback(async (query: string) => {
    try {
      const updatedHistory = [
        query,
        ...historyCache.current.filter(item => item !== query),
      ].slice(0, MAX_HISTORY_ITEMS);
      
      historyCache.current = updatedHistory;
      await AsyncStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
      setSearchHistory(updatedHistory.map(item => ({ query: item, type: 'history' as const })));
    } catch (error) {
      LoggingService.error('Failed to add to search history', 'SEARCH_SCREEN', error as Error);
    }
  }, []);

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    Keyboard.dismiss();
    
    if (searchQuery.trim()) {
      addToSearchHistory(searchQuery);
    }

    switch (result.type) {
      case 'customers':
        if (result.id) {(navigation as any).navigate('CustomerDetails', { customerId: result.id, customer: result });}
        else {(navigation as any).navigate('Customers');}
        break;
      case 'orders':
        if (result.id) {(navigation as any).navigate('CreateOrder', { order: result, isEditing: true });}
        else {(navigation as any).navigate('Orders');}
        break;
      case 'products':
        if (result.id) {(navigation as any).navigate('InventoryItemDetail', { itemId: result.id, product: result });}
        else {(navigation as any).navigate('Products');}
        break;
      case 'history':
      case 'suggestion':
        if (result.query) {
          setSearchQuery(result.query);
          performSearch(result.query);
        }
        break;
    }
  }, [searchQuery, navigation, addToSearchHistory, performSearch]);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults([]);
    setShowHistory(true);
    setSuggestions([]);
  }, []);

  // Clear search history
  const clearHistory = useCallback(async () => {
    try {
      await AsyncStorage.removeItem('searchHistory');
      historyCache.current = [];
      setSearchHistory([]);
    } catch (error) {
      LoggingService.error('Failed to clear search history', 'SEARCH_SCREEN', error as Error);
    }
  }, []);

  // Helper functions
  const getResultIcon = useCallback((type: string): PhosphorIconName => ICON_MAP[type] || 'magnifying-glass', []);
  const getResultTitle = useCallback((item: SearchResult) => item.type === 'history' || item.type === 'suggestion' ? item.query : item.name || item.orderNumber || item.title || 'Unknown', []);
  const getResultSubtitle = useCallback((item: SearchResult) => {
    switch (item.type) {
      case 'customers': return item.email || item.phone || 'Customer';
      case 'orders': return `${item.status || 'Order'} - ${item.customerName || 'Unknown Customer'}`;
      case 'products': return item.category || item.description || 'Product';
      case 'history': return 'Recent search';
      case 'suggestion': return 'Suggested search';
      default: return item.type || 'Unknown';
    }
  }, []);

  // Render search result item
  const renderSearchResult = useCallback(({ item }: { item: SearchResult }) => (
    <TouchableOpacity onPress={() => handleResultSelect(item)} style={styles.resultItem}>
      <Card style={[styles.resultCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content style={styles.resultContent}>
          <View style={styles.resultHeader}>
            <View style={styles.resultIconContainer}>
              <PhosphorIcon name={getResultIcon(item.type)} size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.resultInfo}>
              <Text style={[styles.resultTitle, { color: theme.colors.onSurface }]}>{getResultTitle(item)}</Text>
              <Text style={[styles.resultSubtitle, { color: theme.colors.onSurfaceVariant }]}>{getResultSubtitle(item)}</Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  ), [theme.colors, handleResultSelect, getResultIcon, getResultTitle, getResultSubtitle]);

  const resultKeyExtractor = useCallback((item: SearchResult, index: number) => `${item.type}_${item.id || index}`, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <PhosphorIcon name='arrow-left' size={24} color={theme.colors.onSurface} />
          </TouchableOpacity>
          <View style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant, borderColor: theme.colors.outline }]}>
            <PhosphorIcon name='magnifying-glass' size={20} color={theme.colors.onSurfaceVariant} style={styles.searchIcon} />
            <TextInput
              placeholder='Search everything...'
              onChangeText={handleSearchChange}
              value={searchQuery}
              style={[styles.textInput, { color: theme.colors.onSurface }]}
              placeholderTextColor={theme.colors.onSurfaceVariant}
              autoFocus
              returnKeyType='search'
              onSubmitEditing={() => searchQuery.trim() && performSearch(searchQuery)}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                <PhosphorIcon name='x' size={20} color={theme.colors.onSurfaceVariant} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <View style={styles.contentArea}>
          {isSearching && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size='large' color={theme.colors.primary} />
              <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>Searching...</Text>
            </View>
          )}

          {!isSearching && searchResults.length > 0 && (
            <FlatList
              data={searchResults}
              keyExtractor={resultKeyExtractor}
              renderItem={renderSearchResult}
              contentContainerStyle={styles.resultsList}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              initialNumToRender={10}
              windowSize={10}
            />
          )}

          {!isSearching && searchQuery.trim() && searchResults.length === 0 && (
            <View style={styles.emptyContainer}>
              <PhosphorIcon name='magnifying-glass' size={64} color={theme.colors.onSurfaceVariant} />
              <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>No results found</Text>
              <Text style={[styles.emptySubtitle, { color: theme.colors.onSurfaceVariant }]}>Try adjusting your search terms</Text>
            </View>
          )}

          {showHistory && searchHistory.length > 0 && (
            <View style={styles.historyContainer}>
              <View style={styles.historyHeader}>
                <Text style={[styles.historyTitle, { color: theme.colors.onSurface }]}>Recent Searches</Text>
                <Button mode='text' onPress={clearHistory} compact>Clear</Button>
              </View>
              <FlatList
                data={searchHistory.slice(0, MAX_HISTORY_ITEMS)}
                keyExtractor={resultKeyExtractor}
                renderItem={renderSearchResult}
                showsVerticalScrollIndicator={false}
                removeClippedSubviews={true}
              />
            </View>
          )}

          {suggestions.length > 0 && searchQuery.trim() && (
            <View style={styles.suggestionsContainer}>
              <Text style={[styles.suggestionsTitle, { color: theme.colors.onSurfaceVariant }]}>Suggestions</Text>
              <FlatList
                data={suggestions}
                keyExtractor={resultKeyExtractor}
                renderItem={renderSearchResult}
                showsVerticalScrollIndicator={false}
                removeClippedSubviews={true}
              />
            </View>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, paddingHorizontal: 16, paddingTop: 16 },
  searchContainer: { flexDirection: 'row', alignItems: 'center', marginBottom: 16 },
  backButton: { marginRight: 12, padding: 4 },
  searchBar: { flex: 1, flexDirection: 'row', alignItems: 'center', borderRadius: 12, elevation: 1, height: 40, paddingHorizontal: 12 },
  searchIcon: { marginRight: 8 },
  textInput: { flex: 1, height: '100%', fontSize: 16, paddingVertical: 0 },
  clearButton: { padding: 4, marginLeft: 8 },
  contentArea: { flex: 1 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 32 },
  loadingText: { marginTop: 16, fontSize: 16 },
  resultsList: { padding: 0 },
  resultItem: { marginBottom: 8 },
  resultCard: { elevation: 1 },
  resultContent: { paddingVertical: 12 },
  resultHeader: { flexDirection: 'row', alignItems: 'center' },
  resultIconContainer: { width: 40, height: 40, borderRadius: 20, backgroundColor: 'rgba(0,0,0,0.05)', justifyContent: 'center', alignItems: 'center', marginRight: 12 },
  resultInfo: { flex: 1 },
  resultTitle: { fontSize: 16, fontWeight: '600' },
  resultSubtitle: { fontSize: 14, marginTop: 2 },
  emptyContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 32 },
  emptyTitle: { marginTop: 16, fontSize: 18, fontWeight: '600' },
  emptySubtitle: { marginTop: 8, fontSize: 16, textAlign: 'center' },
  historyContainer: { paddingTop: 16 },
  historyHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  historyTitle: { fontSize: 18, fontWeight: '600' },
  suggestionsContainer: { paddingTop: 16 },
  suggestionsTitle: { marginBottom: 8, fontSize: 16, fontWeight: '600' },
});

export default SearchScreen;