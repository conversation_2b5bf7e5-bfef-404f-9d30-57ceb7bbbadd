import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Alert, Switch } from 'react-native';
import { <PERSON>, Card, Button, Divider } from 'react-native-paper';

import Header from '../../components/navigation/Header';
import { SecurityMonitor } from '../../components/security/SecurityMonitor';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { useSecurity } from '../../hooks/useSecurity';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface SecuritySettings {
  biometricEnabled: boolean;
  autoLockEnabled: boolean;
  autoLockTimeout: number; // in minutes
  securityLoggingEnabled: boolean;
  anomalyDetectionEnabled: boolean;
  encryptSensitiveData: boolean;
  requireBiometricForSensitiveActions: boolean;
}

const SecuritySettingsScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const { showSuccess, showError, showInfo } = useToast();
  const { 
    performSecurityAudit, 
    securityScore, 
    securityIssues,
    logSecurityEvent,
    isSecurityReady,
    securityService 
  } = useSecurity({ enableAuditLogging: true });

  const [settings, setSettings] = useState<SecuritySettings>({
    biometricEnabled: false,
    autoLockEnabled: true,
    autoLockTimeout: 5,
    securityLoggingEnabled: true,
    anomalyDetectionEnabled: true,
    encryptSensitiveData: true,
    requireBiometricForSensitiveActions: false,
  });

  const [isLoading, setIsLoading] = useState(true);
  const [securityMetrics, setSecurityMetrics] = useState<any>(null);
  const [showSecurityMonitor, setShowSecurityMonitor] = useState(false);

  // Load security settings and metrics
  useEffect(() => {
    const loadSecurityData = async () => {
      try {
        if (!isSecurityReady || !securityService) {return;}

        // Get security metrics
        const metrics = securityService.getSecurityMetrics();
        setSecurityMetrics(metrics);

        // Load saved settings (in a real app, load from secure storage)
        setSettings(prev => ({
          ...prev,
          biometricEnabled: metrics.biometricSupported,
          encryptSensitiveData: metrics.encryptionEnabled,
        }));

        // Perform initial security audit
        await performSecurityAudit();

        logSecurityEvent('SECURITY_SETTINGS_VIEWED', {
          securityScore,
          biometricSupported: metrics.biometricSupported,
        });

      } catch (error) {
        LoggingService.error('Failed to load security settings', 'SECURITY_SETTINGS', error as Error);
        showError('Failed to load security settings');
      } finally {
        setIsLoading(false);
      }
    };

    loadSecurityData();
  }, [isSecurityReady, securityService, performSecurityAudit, logSecurityEvent, securityScore, showError]);

  const handleSettingChange = useCallback(async (key: keyof SecuritySettings, value: boolean | number) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);

      // Log security setting changes
      logSecurityEvent('SECURITY_SETTING_CHANGED', {
        setting: key,
        newValue: value,
        previousValue: settings[key],
      });

      // Handle specific setting changes
      switch (key) {
        case 'biometricEnabled':
          if (value && securityMetrics?.biometricSupported) {
            const authenticated = await securityService?.authenticateWithBiometrics(
              'Authenticate to enable biometric security'
            );
            if (!authenticated) {
              setSettings(prev => ({ ...prev, biometricEnabled: false }));
              showError('Biometric authentication failed');
              return;
            }
            showSuccess('Biometric authentication enabled');
          }
          break;

        case 'requireBiometricForSensitiveActions':
          if (value && !settings.biometricEnabled) {
            Alert.alert(
              'Enable Biometrics',
              'You must enable biometric authentication first',
              [{ text: 'OK' }]
            );
            setSettings(prev => ({ ...prev, requireBiometricForSensitiveActions: false }));
            return;
          }
          break;

        case 'securityLoggingEnabled':
          showInfo(value ? 'Security logging enabled' : 'Security logging disabled');
          break;
      }

      // In a real app, save settings to secure storage
      showSuccess('Security setting updated');

    } catch (error) {
      LoggingService.error('Failed to update security setting', 'SECURITY_SETTINGS', error as Error);
      showError('Failed to update security setting');
    }
  }, [settings, securityMetrics, securityService, logSecurityEvent, showSuccess, showError, showInfo]);

  const handleRunSecurityAudit = useCallback(async () => {
    try {
      setIsLoading(true);
      const auditResult = await performSecurityAudit();
      
      Alert.alert(
        'Security Audit Complete',
        `Security Score: ${auditResult.score}/100\n\n` +
        `Issues Found: ${auditResult.issues.length}\n\n` +
        (auditResult.issues.length > 0 
          ? `Issues:\n${auditResult.issues.join('\n')}`
          : 'No security issues found!'),
        [{ text: 'OK' }]
      );

      showSuccess('Security audit completed');
    } catch (error) {
      LoggingService.error('Security audit failed', 'SECURITY_SETTINGS', error as Error);
      showError('Security audit failed');
    } finally {
      setIsLoading(false);
    }
  }, [performSecurityAudit, showSuccess, showError]);

  const handleClearSecurityData = useCallback(() => {
    Alert.alert(
      'Clear Security Data',
      'This will clear all security logs and anomaly data. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              securityService?.clearSessionAnomalies();
              logSecurityEvent('SECURITY_DATA_CLEARED', {
                clearedBy: 'user',
                timestamp: new Date().toISOString(),
              });
              showSuccess('Security data cleared');
            } catch (error) {
              LoggingService.error('Failed to clear security data', 'SECURITY_SETTINGS', error as Error);
              showError('Failed to clear security data');
            }
          },
        },
      ]
    );
  }, [securityService, logSecurityEvent, showSuccess, showError]);

  const renderSecurityMetric = (label: string, value: string | boolean, icon: string, color?: string) => (
    <View style={styles.metricRow}>
      <View style={styles.metricLeft}>
        <PhosphorIcon 
          name={icon as any} 
          size={20} 
          color={color || theme.colors.onSurfaceVariant} 
        />
        <Text style={[styles.metricLabel, { color: theme.colors.onSurface }]}>
          {label}
        </Text>
      </View>
      <Text style={[styles.metricValue, { color: color || theme.colors.onSurfaceVariant }]}>
        {typeof value === 'boolean' ? (value ? 'Enabled' : 'Disabled') : value}
      </Text>
    </View>
  );

  const renderSettingRow = (
    label: string, 
    description: string, 
    value: boolean, 
    onToggle: () => void,
    icon: string,
    disabled?: boolean
  ) => (
    <View style={styles.settingRow}>
      <View style={styles.settingLeft}>
        <PhosphorIcon name={icon as any} size={20} color={theme.colors.onSurfaceVariant} />
        <View style={styles.settingText}>
          <Text style={[styles.settingLabel, { color: theme.colors.onSurface }]}>
            {label}
          </Text>
          <Text style={[styles.settingDescription, { color: theme.colors.onSurfaceVariant }]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        disabled={disabled}
        trackColor={{ false: theme.colors.outline, true: theme.colors.primary }}
        thumbColor={value ? theme.colors.onPrimary : theme.colors.onSurfaceVariant}
      />
    </View>
  );

  if (!isSecurityReady) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header
          title="Security Settings"
          onBackPress={() => navigation.goBack()}
          showBack={true}
        />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading security settings...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title="Security Settings"
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            icon: 'shield-check',
            onPress: handleRunSecurityAudit,
          },
        ]}
      />

      <ScrollView style={styles.scrollView}>
        {/* Security Score Card */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.scoreHeader}>
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Security Status
              </Text>
              <View style={styles.scoreContainer}>
                <Text style={[styles.scoreValue, { 
                  color: securityScore >= 80 ? theme.colors.success : 
                         securityScore >= 60 ? '#FFC107' : theme.colors.error 
                }]}>
                  {securityScore}/100
                </Text>
              </View>
            </View>

            {securityIssues.length > 0 && (
              <View style={styles.issuesContainer}>
                <Text style={[styles.issuesTitle, { color: theme.colors.error }]}>
                  Security Issues:
                </Text>
                {securityIssues.slice(0, 3).map((issue, index) => (
                  <Text key={index} style={[styles.issueText, { color: theme.colors.onSurfaceVariant }]}>
                    • {issue}
                  </Text>
                ))}
                {securityIssues.length > 3 && (
                  <Text style={[styles.issueText, { color: theme.colors.onSurfaceVariant }]}>
                    ...and {securityIssues.length - 3} more
                  </Text>
                )}
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Security Metrics */}
        {securityMetrics && (
          <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Security Metrics
              </Text>
              {renderSecurityMetric(
                'Encryption', 
                securityMetrics.encryptionEnabled, 
                'lock',
                securityMetrics.encryptionEnabled ? theme.colors.success : theme.colors.error
              )}
              {renderSecurityMetric(
                'Biometric Support', 
                securityMetrics.biometricSupported, 
                'fingerprint',
                securityMetrics.biometricSupported ? theme.colors.success : theme.colors.onSurfaceVariant
              )}
              {renderSecurityMetric(
                'Device Fingerprint', 
                securityMetrics.deviceFingerprint ? 'Active' : 'Inactive', 
                'device-mobile',
                securityMetrics.deviceFingerprint ? theme.colors.success : theme.colors.error
              )}
              {renderSecurityMetric(
                'Session Anomalies', 
                securityMetrics.sessionAnomalies.toString(), 
                'warning',
                securityMetrics.sessionAnomalies > 5 ? theme.colors.error : theme.colors.success
              )}
            </Card.Content>
          </Card>
        )}

        {/* Authentication Settings */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
              Authentication
            </Text>
            
            {renderSettingRow(
              'Biometric Authentication',
              'Use fingerprint or face recognition for secure access',
              settings.biometricEnabled,
              () => handleSettingChange('biometricEnabled', !settings.biometricEnabled),
              'fingerprint',
              !securityMetrics?.biometricSupported
            )}

            <Divider style={styles.divider} />

            {renderSettingRow(
              'Require Biometric for Sensitive Actions',
              'Require biometric authentication for sensitive operations',
              settings.requireBiometricForSensitiveActions,
              () => handleSettingChange('requireBiometricForSensitiveActions', !settings.requireBiometricForSensitiveActions),
              'shield-check',
              !settings.biometricEnabled
            )}

            <Divider style={styles.divider} />

            {renderSettingRow(
              'Auto Lock',
              'Automatically lock the app after inactivity',
              settings.autoLockEnabled,
              () => handleSettingChange('autoLockEnabled', !settings.autoLockEnabled),
              'lock'
            )}
          </Card.Content>
        </Card>

        {/* Privacy & Data Settings */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
              Privacy & Data Protection
            </Text>

            {renderSettingRow(
              'Encrypt Sensitive Data',
              'Encrypt sensitive information stored on device',
              settings.encryptSensitiveData,
              () => handleSettingChange('encryptSensitiveData', !settings.encryptSensitiveData),
              'lock'
            )}

            <Divider style={styles.divider} />

            {renderSettingRow(
              'Security Logging',
              'Log security events for monitoring and analysis',
              settings.securityLoggingEnabled,
              () => handleSettingChange('securityLoggingEnabled', !settings.securityLoggingEnabled),
              'file-text'
            )}

            <Divider style={styles.divider} />

            {renderSettingRow(
              'Anomaly Detection',
              'Monitor for suspicious activity and security threats',
              settings.anomalyDetectionEnabled,
              () => handleSettingChange('anomalyDetectionEnabled', !settings.anomalyDetectionEnabled),
              'shield-warning'
            )}
          </Card.Content>
        </Card>

        {/* Security Actions */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
              Security Actions
            </Text>

            <Button
              mode="outlined"
              onPress={handleRunSecurityAudit}
              style={styles.actionButton}
              icon="shield-check"
              loading={isLoading}
            >
              Run Security Audit
            </Button>

            <Button
              mode="outlined"
              onPress={() => setShowSecurityMonitor(!showSecurityMonitor)}
              style={styles.actionButton}
              icon="monitor"
            >
              {showSecurityMonitor ? 'Hide' : 'Show'} Security Monitor
            </Button>

            <Button
              mode="outlined"
              onPress={handleClearSecurityData}
              style={styles.actionButton}
              icon="trash"
              buttonColor={theme.colors.errorContainer}
              textColor={theme.colors.error}
            >
              Clear Security Data
            </Button>
          </Card.Content>
        </Card>

        {/* Security Monitor */}
        {showSecurityMonitor && (
          <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Security Monitor
              </Text>
              <SecurityMonitor
                onSecurityEvent={(event) => {
                  LoggingService.info(`Security event: ${event.type}`, 'SECURITY_SETTINGS');
                }}
                maxEventsToShow={10}
              />
            </Card.Content>
          </Card>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    padding: SPACING.md,
  },
  card: {
    marginBottom: SPACING.md,
    borderRadius: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: SPACING.md,
  },
  scoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  issuesContainer: {
    marginTop: SPACING.md,
  },
  issuesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: SPACING.xs,
  },
  issueText: {
    fontSize: 14,
    marginBottom: SPACING.xs,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  metricLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 14,
    marginLeft: SPACING.sm,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.md,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: SPACING.md,
  },
  settingText: {
    marginLeft: SPACING.sm,
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 14,
    marginTop: SPACING.xs,
  },
  divider: {
    marginVertical: SPACING.sm,
  },
  actionButton: {
    marginBottom: SPACING.sm,
  },
});

export default SecuritySettingsScreen;