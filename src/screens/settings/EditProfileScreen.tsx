import * as FileSystem from 'expo-file-system';
import React, { useEffect, useState, useCallback } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';

import ImagePicker from '../../components/forms/ImagePicker';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { ImageProcessingService } from '../../services/ImageProcessingService';
import LoggingService from '../../services/LoggingService';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { ActionSheetOption } from '../../types';

interface EditProfileScreenProps {
  route?: {
    params?: {
      profileData?: any;
    };
  };
}

interface ProfileFormData {
  storeName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
  taxRate: string;
  profileImage: string;
}

const EditProfileScreen: React.FC<EditProfileScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();
  const { showSuccess, showError } = useToast();

  const profileData = route?.params?.profileData || state.settings;

  const [formData, setFormData] = useState<ProfileFormData>({
    storeName: '',
    ownerName: '',
    email: '',
    phone: '',
    address: '',
    taxRate: '8',
    profileImage: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showValidationErrorActionSheet, setShowValidationErrorActionSheet] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  useEffect(() => {
    if (profileData) {
      setFormData({
        storeName: profileData?.storeName || '',
        ownerName: profileData?.ownerName || '',
        email: profileData?.email || '',
        phone: profileData?.phone || '',
        address: profileData?.address || '',
        taxRate: (profileData.taxRate * 100).toString() || '8',
        profileImage: profileData.profileImage || '',
      });
    }
  }, [profileData]);

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'storeName':
        return value.trim() !== '' ? '' : 'Store name is required';
      case 'ownerName':
        return value.trim() !== '' ? '' : 'Owner name is required';
      case 'email':
        return value === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
          ? ''
          : 'Invalid email address';
      case 'phone':
        return value === '' || /^\d{8,15}$/.test(value) ? '' : 'Invalid phone number';
      case 'taxRate':
        return !isNaN(Number(value)) && Number(value) >= 0 && Number(value) <= 50
          ? ''
          : 'Tax rate must be between 0 and 50';
      default:
        return '';
    }
  };

  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSave = useCallback(async () => {
    if (isSaving) return;

    const newErrors: any = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof typeof formData]);
      if (error) newErrors[key] = error;
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      setShowValidationErrorActionSheet(true);
      return;
    }

    setIsSaving(true);

    try {
      const isNewImage = formData.profileImage.startsWith('file://') && 
                        (!FileSystem.documentDirectory || !formData.profileImage.includes(FileSystem.documentDirectory));

      let finalProfileImage = formData.profileImage;

      // Process image asynchronously without blocking UI
      if (isNewImage) {
        LoggingService.info('Processing new profile image...', 'PROFILE', { 
          originalUri: formData.profileImage 
        });
        
        // Use a smaller timeout to prevent UI blocking
        try {
          const processPromise = ImageProcessingService.optimizeAndSaveLocally(formData.profileImage);
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Image processing timeout')), 5000)
          );
          
          finalProfileImage = await Promise.race([processPromise, timeoutPromise]) as string;
          LoggingService.info('Profile image processed and saved locally', 'PROFILE', { 
            savedUri: finalProfileImage 
          });
        } catch (imageError) {
          LoggingService.error('Failed to process profile image', 'PROFILE', imageError as Error);
          // Continue with original image if processing fails
          finalProfileImage = formData.profileImage;
        }
      }

      // Save profile with the processed image URI
      const profileToSave = {
        ...formData,
        profileImage: finalProfileImage,
        taxRate: parseFloat(formData.taxRate) / 100 || 0.08,
      };
      
      await actions.updateSettings(profileToSave);
      LoggingService.info('Profile updated successfully', 'PROFILE');
      
      // Show success message and navigate back immediately
      showSuccess('Profile updated successfully!');
      navigation.goBack();
      
    } catch (error) {
      LoggingService.error('Error saving profile', 'PROFILE', error as Error);
      showError('Failed to save profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [isSaving, formData, errors, actions, showSuccess, showError, navigation]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Edit Profile'
        onBackPress={handleBackPress}
        showBack={true}
        actions={[
          {
            text: isSaving ? 'Saving...' : 'Save',
            onPress: handleSave,
            color: theme.colors.primary,
            disabled: isSaving,
          },
        ]}
        
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.avatarSection}>
          <ImagePicker
            currentImage={formData.profileImage}
            onImageSelected={uri => {
              setFormData(prev => ({ ...prev, profileImage: uri }));
            }}
            placeholder={formData.profileImage ? 'Change Profile Image' : 'Upload Profile Image'}
            size='large'
          />
        </View>

        <View style={styles.formFieldsContainer}>
          <View style={styles.fieldContainer}>
            <TextInput
              label='Store Name *'
              value={formData.storeName}
              onChangeText={val => handleFieldChange('storeName', val)}
              placeholder='Enter store name'
              error={errors.storeName}
              style={styles.input}
            />
            {errors.storeName && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.storeName}</Text>}
          </View>

          <View style={styles.fieldContainer}>
            <TextInput
              label='Owner Name *'
              value={formData.ownerName}
              onChangeText={val => handleFieldChange('ownerName', val)}
              placeholder='Enter owner name'
              error={errors.ownerName}
              style={styles.input}
            />
            {errors.ownerName && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.ownerName}</Text>}
          </View>

          <View style={styles.fieldContainer}>
            <TextInput
              label='Email'
              value={formData.email}
              onChangeText={val => handleFieldChange('email', val)}
              placeholder='Enter email address'
              keyboardType='email-address'
              autoCapitalize='none'
              error={errors.email}
              style={styles.input}
            />
            {errors.email && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.email}</Text>}
          </View>

          <View style={styles.fieldContainer}>
            <TextInput
              label='Phone'
              value={formData.phone}
              onChangeText={val => handleFieldChange('phone', val)}
              placeholder='Enter phone number'
              keyboardType='phone-pad'
              error={errors.phone}
              style={styles.input}
            />
            {errors.phone && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.phone}</Text>}
          </View>

          <View style={styles.fieldContainer}>
            <TextInput
              label='Address'
              value={formData.address}
              onChangeText={val => handleFieldChange('address', val)}
              placeholder='Enter address'
              multiline
              numberOfLines={3}
              error={errors.address}
              style={styles.input}
            />
            {errors.address && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.address}</Text>}
          </View>

          <View style={styles.fieldContainer}>
            <TextInput
              label='Tax Rate (%) *'
              value={formData.taxRate}
              onChangeText={val => handleFieldChange('taxRate', val)}
              placeholder='Enter tax rate (e.g., 8.5)'
              keyboardType='decimal-pad'
              error={errors.taxRate}
              style={styles.input}
              rightAffix='%'
            />
            {errors.taxRate && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.taxRate}</Text>}
          </View>
        </View>

        <View style={styles.bottomButtonContainer}>
          <Button 
            variant='primary' 
            size='md' 
            onPress={handleSave} 
            style={styles.bottomSaveButton}
            loading={isSaving}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </View>

        <View style={{ height: 16 }} />

        <ActionSheet
          visible={showValidationErrorActionSheet}
          onDismiss={() => setShowValidationErrorActionSheet(false)}
          title='Validation Error'
          description='Please fix the errors and try again.'
          options={[{ text: 'OK', onPress: () => setShowValidationErrorActionSheet(false), icon: 'check', isAction: true }]}
          showCancel={false}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, padding: SPACING.md },
  avatarSection: { alignItems: 'center', marginBottom: SPACING.lg, marginTop: SPACING.sm },
  formFieldsContainer: { marginTop: SPACING.md },
  fieldContainer: { marginBottom: SPACING.md },
  input: { marginBottom: SPACING.xs },
  errorText: { marginTop: SPACING.xs, fontSize: TYPOGRAPHY.fontSize.sm },
  bottomButtonContainer: { marginTop: SPACING.lg, marginBottom: SPACING.md, alignItems: 'center' },
  bottomSaveButton: { width: '100%', alignSelf: 'center' },
});

export default EditProfileScreen;
