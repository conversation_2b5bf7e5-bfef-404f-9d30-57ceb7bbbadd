import { useNavigation } from '@react-navigation/native';
import React, { useState, useCallback, useMemo, memo } from 'react';
import { <PERSON>ert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Avatar, Text } from 'react-native-paper';

import Header from '../../components/navigation/Header';
import Switch from '../../components/ui/Switch';
import { useCurrentUser, useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { usePermissions } from '../../hooks';
import LoggingService from '../../services/LoggingService';
import { BORDER_RADIUS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPE DEFINITIONS ---
interface ProfileItem {
  icon: PhosphorIconName;
  label: string;
  onPress: () => void;
  rightComponent?: React.ReactNode;
  color?: string;
}

// --- SUB-COMPONENT: ProfileHeader ---
const ProfileHeader = memo(({ settings, user, onEditPress }: any) => {
  const theme = useTheme();
  const initials = typeof settings.storeName === 'string' && settings.storeName ? settings.storeName.split(' ').map((w: string) => w[0]).join('').substring(0, 2).toUpperCase() : '??';

  const storeName = typeof settings.storeName === 'string' ? settings.storeName : '';
  const phone = typeof settings.phone === 'string' ? settings.phone : 'No phone';
  const email = typeof settings.email === 'string' ? settings.email : 'No email';

  return (
    <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={styles.profileMain}>
            <View style={styles.avatarContainer}>
              {settings.profileImage ? (
                <Avatar.Image size={56} source={{ uri: settings.profileImage }} />
              ) : (
                <Avatar.Text size={56} label={initials} />
              )}
            </View>
            <View style={styles.profileInfo}>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                    <Text style={[styles.profileName, { color: theme.colors.onSurface }]}>{storeName}</Text>
                    <View style={[styles.roleBadge, { backgroundColor: `${theme.colors.primary}22` }]}>
                        <Text style={[styles.roleBadgeText, { color: theme.colors.primary }]}>{user?.role || 'Owner'}</Text>
                    </View>
                </View>
                <Text style={[styles.profileDetail, { color: theme.colors.onSurfaceVariant }]}>{phone}</Text>
                <Text style={[styles.profileDetail, { color: theme.colors.onSurfaceVariant }]}>{email}</Text>
            </View>
          </View>
          <TouchableOpacity onPress={onEditPress} style={[styles.editButton, { backgroundColor: `${theme.colors.primary}1A` }]}>
            <Text style={[styles.editButtonText, { color: theme.colors.primary }]}>Edit</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
});
ProfileHeader.displayName = 'ProfileHeader';

// --- SUB-COMPONENT: ProfileSection ---
const ProfileSection = memo(({ title, items }: { title: string; items: ProfileItem[] }) => {
  const theme = useTheme();
  return (
    <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.onSurfaceVariant }]}>{title}</Text>
      {items.map((item, idx) => (
        <TouchableOpacity
          key={item.label}
          style={[styles.sectionItem, { borderBottomColor: idx === items.length - 1 ? 'transparent' : theme.colors.outlineVariant }]}
          onPress={item.onPress}
          activeOpacity={0.7}
        >
          <PhosphorIcon name={item.icon} size={22} color={item.color || theme.colors.onSurfaceVariant} style={styles.sectionIcon} />
          <Text style={[styles.sectionLabel, { color: item.color || theme.colors.onSurface }]}>{item.label}</Text>
          {item.rightComponent ? item.rightComponent : <PhosphorIcon name='caret-right' size={20} color={theme.colors.onSurfaceVariant} />}
        </TouchableOpacity>
      ))}
    </View>
  );
});
ProfileSection.displayName = 'ProfileSection';

// --- MAIN SCREEN COMPONENT ---
const MyProfileScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const theme = useTheme();
  const { isDarkMode, toggleTheme } = theme;
  const { state: dataState, actions: dataActions } = useData();
  const { logout } = useAuth();
  const currentUser = useCurrentUser();
  const { hasRole } = usePermissions(currentUser);

  const [notifications, setNotifications] = useState(dataState.settings.notifications ?? true);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleSettingChange = (key: string, value: boolean) => {
    dataActions.updateSettings({ [key]: value });
    if (key === 'notifications') {setNotifications(value);}
  };

  const handleLogout = useCallback(async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      LoggingService.info('User logged out successfully', 'AUTH');
    } catch (error) {
      LoggingService.error('Logout failed', 'AUTH', error as Error);
      Alert.alert('Logout Failed', 'An error occurred. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  }, [logout]);

  const appSettingsItems: ProfileItem[] = useMemo(() => [
    { icon: 'moon', label: 'Dark Mode', rightComponent: <Switch value={isDarkMode} onValueChange={toggleTheme} />, onPress: toggleTheme },
    { icon: 'bell', label: 'Notifications', rightComponent: <Switch value={notifications} onValueChange={(v) => handleSettingChange('notifications', v)} />, onPress: () => handleSettingChange('notifications', !notifications)},
  ], [isDarkMode, toggleTheme, notifications]);

  const preferencesItems: ProfileItem[] = useMemo(() => [
    ...(hasRole('admin') ? [{ icon: 'credit-card' as PhosphorIconName, label: 'Payment Methods', onPress: () => navigation.navigate('PaymentMethods') }] : []),
    { icon: 't-shirt', label: 'Service Types', onPress: () => navigation.navigate('ServiceTypes') },
    { icon: 'ruler', label: 'Service Type Detail', onPress: () => navigation.navigate('AddService') },
    ...(hasRole('manager') ? [{ icon: 'users' as PhosphorIconName, label: 'Staff Management', onPress: () => navigation.navigate('StaffManagement') }] : []),
    ...(hasRole('manager') ? [{ icon: 'database' as PhosphorIconName, label: 'Data Management', onPress: () => navigation.navigate('DataManagement') }] : []),
    ...(hasRole('admin') ? [{ icon: 'clipboard-text' as PhosphorIconName, label: 'Activity Log', onPress: () => navigation.navigate('ActivityLog') }] : []),
  ], [navigation, hasRole]);

  const supportItems: ProfileItem[] = useMemo(() => [
    { icon: 'question', label: 'Help & FAQ', onPress: () => navigation.navigate('HelpFAQ') },
    { icon: 'phone', label: 'Contact Support', onPress: () => navigation.navigate('ContactSupport') },
    { icon: 'info', label: 'About', onPress: () => navigation.navigate('About') },
    ...(hasRole('admin') ? [{ icon: 'chart-line' as PhosphorIconName, label: 'App Status', onPress: () => navigation.navigate('AppStatus') }] : []),
    ...(hasRole('admin') ? [{ icon: 'chart-bar' as PhosphorIconName, label: 'View Reports', onPress: () => navigation.navigate('Reports') }] : []),
  ], [navigation, hasRole]);

  const logoutItems: ProfileItem[] = useMemo(() => [
    { icon: 'sign-out', label: 'Logout', onPress: handleLogout, color: theme.colors.error },
  ], [handleLogout, theme.colors.error]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='My Profile' showBack={true} />
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        <ProfileHeader settings={dataState.settings} user={currentUser} onEditPress={() => navigation.navigate('EditProfile', { profileData: dataState.settings })} />
        <ProfileSection title="App Preferences" items={appSettingsItems} />
        <ProfileSection title="Profile Preferences" items={preferencesItems} />
        <ProfileSection title="Support & Help" items={supportItems} />
        <ProfileSection title="Account" items={logoutItems} />
      </ScrollView>
    </View>
  );
};

// --- STYLES ---
const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContent: { paddingBottom: SPACING.lg },
  // ProfileHeader styles
  profileContent: { paddingHorizontal: SPACING.md, paddingVertical: SPACING.sm },
  profileHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  profileMain: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  avatarContainer: { marginRight: SPACING.md },
  profileInfo: { flex: 1 },
  profileName: { fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: TYPOGRAPHY.fontWeight.bold },
  profileDetail: { color: 'grey', marginTop: 2, fontSize: TYPOGRAPHY.fontSize.sm },
  roleBadge: { paddingHorizontal: 8, paddingVertical: 2, borderRadius: BORDER_RADIUS.sm, marginLeft: 4 },
  roleBadgeText: { fontWeight: '600', fontSize: 11 },
  editButton: { paddingHorizontal: SPACING.md, paddingVertical: SPACING.sm, borderRadius: BORDER_RADIUS.md },
  editButtonText: { fontWeight: '600', fontSize: TYPOGRAPHY.fontSize.sm },
  // ProfileSection styles
  section: { marginVertical: 4, marginHorizontal: SPACING.md, borderRadius: BORDER_RADIUS.lg, overflow: 'hidden' },
  sectionTitle: { fontSize: 12, fontWeight: '500', paddingHorizontal: SPACING.md, paddingTop: SPACING.md, paddingBottom: SPACING.xs, opacity: 0.7 },
  sectionItem: { flexDirection: 'row', alignItems: 'center', paddingVertical: 14, paddingHorizontal: SPACING.md, borderBottomWidth: StyleSheet.hairlineWidth },
  sectionIcon: { marginRight: SPACING.md },
  sectionLabel: { flex: 1, fontSize: 16, fontWeight: '500' },
});

export default MyProfileScreen;