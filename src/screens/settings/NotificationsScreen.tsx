import { useNavigation } from '@react-navigation/native';
import React, { useC<PERSON>back, useMemo, useState } from 'react';
import {
  FlatList,
  ListRenderItem,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Text, Portal } from 'react-native-paper';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import ChipGroup from '../../components/ui/ChipGroup';
import { useTheme, ThemeContextType } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import {
  NotificationLog as NotificationType,
  NotificationService,
  useNotifications,
} from '../../services/notificationService';
import { BORDER_RADIUS, SHADOWS, SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPES ---
type Notification = NotificationType & {
  data?: { action?: () => void; actionType?: string; actionText?: string };
};
interface NotificationCategoryConfig { id: string; label: string; types?: string[]; }

// --- 1. STATIC CONFIGURATION ---
const NOTIFICATION_CONFIG = {
  view_order: { icon: 'clipboard-text' as PhosphorIconName, title: 'Orders', color: '#4CAF50', route: 'Orders' },
  inventory_low: { icon: 'warning-circle' as PhosphorIconName, title: 'Low Stock', color: '#F44336', route: 'Products' },
  inventory_added: { icon: 'plus-circle' as PhosphorIconName, title: 'Stock Added', color: '#4CAF50', route: 'Products' },
  inventory_removed: { icon: 'minus-circle' as PhosphorIconName, title: 'Stock Removed', color: '#FF9800', route: 'Products' },
  inventory_transferred: { icon: 'arrows-left-right' as PhosphorIconName, title: 'Stock Transfer', color: '#2196F3', route: 'Products' },
  make_payment: { icon: 'money' as PhosphorIconName, title: 'Payments', color: '#FF9800', route: 'Financial' },
  view_payment: { icon: 'money' as PhosphorIconName, title: 'Payments', color: '#FF9800', route: 'Financial' },
  view_appointment: { icon: 'calendar' as PhosphorIconName, title: 'Appointments', color: '#2196F3', route: 'Measurement' },
  restock: { icon: 'package' as PhosphorIconName, title: 'Inventory', color: '#F44336', route: 'Products' },
  system_update: { icon: 'gear' as PhosphorIconName, title: 'System', color: '#9C27B0', route: 'About' },
  view_review: { icon: 'star' as PhosphorIconName, title: 'Reviews', color: '#FFC107', route: 'Customers' },
  order_supplies: { icon: 'shopping-cart' as PhosphorIconName, title: 'Supplies', color: '#795548', route: 'AddFabric' },
  test: { icon: 'bell' as PhosphorIconName, title: 'Test', color: '#607D8B', route: null },
} as const;

const NOTIFICATION_CATEGORIES: Record<string, NotificationCategoryConfig> = {
  all: { id: 'all', label: 'All' },
  inventory: { id: 'inventory', label: 'Inventory', types: ['inventory_low', 'inventory_added', 'inventory_removed', 'inventory_transferred', 'restock', 'order_supplies'] },
  orders: { id: 'orders', label: 'Orders', types: ['view_order'] },
  payments: { id: 'payments', label: 'Payments', types: ['make_payment', 'view_payment'] },
  appointments: { id: 'appointments', label: 'Appointments', types: ['view_appointment'] },
  system: { id: 'system', label: 'System', types: ['system_update'] }
};

// --- 2. CHILD UI COMPONENTS ---
const NotificationItem = React.memo(
  ({ item, onPress, onDelete, config, formatTimestamp, theme }: {
    item: Notification;
    onPress: (notification: Notification) => void;
    onDelete: (notification: Notification) => void;
    config: (typeof NOTIFICATION_CONFIG)[keyof typeof NOTIFICATION_CONFIG];
    formatTimestamp: (timestamp: string) => string;
    theme: ThemeContextType;
  }) => (
    <TouchableOpacity onPress={() => onPress(item)} activeOpacity={0.7} style={[ styles.notificationItem, { backgroundColor: theme.colors.surface, ...SHADOWS.sm } ]} >
      <View style={styles.notificationContent}>
        <View style={styles.avatarSection}>
          <View style={[styles.avatar, { backgroundColor: config.color }]}>
            <PhosphorIcon name={config.icon} size={20} color='white' />
          </View>
        </View>
        <View style={styles.contentSection}>
          <View style={styles.notificationHeader}>
            <Text style={[ styles.notificationTitle, { color: item.read ? theme.colors.onSurfaceVariant : theme.colors.onSurface, fontWeight: item.read ? '400' : '700' } ]}>
              {item.title}
            </Text>
            <View style={styles.timestampContainer}>
              <Text style={[styles.timestamp, { color: theme.colors.onSurfaceVariant }]}>{formatTimestamp(item.timestamp)}</Text>
              {onDelete && <TouchableOpacity onPress={() => onDelete(item)} style={styles.deleteButton}><PhosphorIcon name="trash" size={18} color={theme.colors.error} /></TouchableOpacity>}
              {!item.read && <View style={[styles.unreadBadge, { backgroundColor: config.color }]} />}
            </View>
          </View>
          <Text style={[ styles.notificationMessage, { color: item.read ? theme.colors.onSurfaceVariant : theme.colors.onSurface } ]}>
            {item.message}
          </Text>
          {item.data && (item.data.action || item.data.actionType) && (
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: `${theme.colors.primary}15` }]} onPress={() => onPress(item)}>
              <Text style={[styles.actionButtonText, { color: theme.colors.primary }]}>{item.data.actionText || 'View'}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  )
);

const EmptyState = React.memo(
  ({ theme, typeFilter, onAddTest }: { theme: ThemeContextType; typeFilter: string; onAddTest: () => void; }) => (
    <View style={styles.emptyStateContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: `${theme.colors.primary}10` }]}>
        <PhosphorIcon name='bell' size={48} color={theme.colors.primary} />
      </View>
      <Text style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>No notifications yet</Text>
      <Text style={[styles.emptyMessage, { color: theme.colors.onSurfaceVariant }]}>You'll see important updates about your business here.</Text>
      {typeFilter === 'all' && (
        <View style={styles.emptyActions}>
          <Button variant='primary' onPress={onAddTest} style={styles.testButton}>Add Test Notification</Button>
          
        </View>
      )}
    </View>
  )
);

// --- 3. MAIN COMPONENT ---
const NotificationsScreen = () => {
  const navigation = useNavigation<any>();
  const theme = useTheme();
  const { showSuccess, showError } = useToast();
  const { notifications, markAsRead, deleteNotification, unreadCount } = useNotifications();
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showClearAllActionSheet, setShowClearAllActionSheet] = useState(false);

  // Stable callbacks using useCallback
  const handleClearAll = useCallback(() => {
    setShowClearAllActionSheet(true);
  }, []);

  const handleDismissActionSheet = useCallback(() => {
    setShowClearAllActionSheet(false);
  }, []);

  const handleConfirmClearAll = useCallback(async () => {
    try {
      await NotificationService.clearAllNotificationData();
      showSuccess('All notifications cleared');
    } catch (error) {
      showError('Failed to clear notifications');
    }
  }, [showSuccess, showError]);

  const handleAddTest = useCallback(async () => {
    await NotificationService.logNotification({ 
      title: 'Test Notification', 
      message: 'This is a test notification.', 
      category: 'general', 
      data: { actionType: 'test' } 
    });
  }, []);

  

  const handleNotificationPress = useCallback((notification: Notification) => {
    markAsRead(notification.id);
    const actionType = notification.data?.actionType;
    const config = NOTIFICATION_CONFIG[actionType as keyof typeof NOTIFICATION_CONFIG];
    if (config?.route) {
      navigation.navigate(config.route);
    }
  }, [navigation, markAsRead]);

  const handleDelete = useCallback(async (notification: Notification) => {
    try {
      await deleteNotification(notification.id);
      showSuccess('Notification deleted');
    } catch (error) { 
      showError('Failed to delete'); 
    }
  }, [deleteNotification, showSuccess, showError]);

  const formatTimestamp = useCallback((timestamp: string): string => {
    const diffInHours = (Date.now() - new Date(timestamp).getTime()) / (1000 * 60 * 60);
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`;
    return new Date(timestamp).toLocaleDateString();
  }, []);

  // Memoized computed values
  const filteredNotifications = useMemo(() => {
    let filtered = [...notifications];
    if (typeFilter !== 'all') {
      const category = NOTIFICATION_CATEGORIES[typeFilter];
      const types = category?.types;
      if (types) {
        filtered = filtered.filter(n => types.includes(n.data?.actionType || ''));
      }
    }
    return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [notifications, typeFilter]);

  const filterOptions = useMemo(() => Object.values(NOTIFICATION_CATEGORIES), []);

  const headerActions = useMemo(() => (
    notifications.length > 0 ? [{ icon: 'trash' as PhosphorIconName, onPress: handleClearAll }] : []
  ), [notifications.length, handleClearAll]);
  
  // ActionSheet options
  const actionSheetOptions = useMemo(() => [
    { 
      text: 'Clear All', 
      onPress: handleConfirmClearAll, 
      style: 'destructive' as const,
      icon: 'trash' as PhosphorIconName
    }
  ], [handleConfirmClearAll]);

  const renderItem: ListRenderItem<Notification> = useCallback(({ item }) => (
    <NotificationItem 
        item={item} 
        onPress={handleNotificationPress} 
        onDelete={handleDelete} 
        config={NOTIFICATION_CONFIG[item.data?.actionType as keyof typeof NOTIFICATION_CONFIG] || NOTIFICATION_CONFIG.test} 
        formatTimestamp={formatTimestamp} 
        theme={theme} 
    />
  ), [handleDelete, handleNotificationPress, formatTimestamp, theme]);

  const renderEmptyState = useCallback(() => (
      <EmptyState 
        theme={theme} 
        typeFilter={typeFilter} 
        onAddTest={handleAddTest} 
      />
  ), [theme, typeFilter, handleAddTest]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title={`Notifications (${unreadCount})`} showBack={true} actions={headerActions} />
      <ChipGroup 
        filters={filterOptions} 
        selectedFilter={typeFilter} 
        onFilterChange={id => setTypeFilter(String(id))} 
        style={styles.chipGroupStyle} 
      />
      <FlatList 
          data={filteredNotifications} 
          keyExtractor={item => item.id} 
          renderItem={renderItem} 
          contentContainerStyle={styles.listContent} 
          ListEmptyComponent={renderEmptyState} 
          initialNumToRender={10} 
          maxToRenderPerBatch={10} 
          windowSize={11} 
      />
      <Portal>
        <ActionSheet 
            visible={showClearAllActionSheet} 
            onDismiss={handleDismissActionSheet}
            title="Clear All Notifications" 
            description="This action cannot be undone."
            options={actionSheetOptions}
            showCancel={true}
            cancelText="Cancel"
            closeOnBackdropPress={true}
        />
      </Portal>
    </View>
  );
};

// --- 5. STYLES ---
const styles = StyleSheet.create({
  container: { flex: 1 },
  listContent: { padding: SPACING.md, paddingBottom: 100 },
  chipGroupStyle: { paddingHorizontal: SPACING.md, paddingVertical: SPACING.sm },
  notificationItem: { paddingVertical: 16, borderRadius: BORDER_RADIUS.lg, marginBottom: SPACING.sm },
  notificationContent: { flexDirection: 'row', alignItems: 'flex-start' },
  avatarSection: { marginLeft: 16, marginRight: 4 },
  avatar: { width: 36, height: 36, borderRadius: 18, justifyContent: 'center', alignItems: 'center' },
  contentSection: { flex: 1, paddingHorizontal: 12, marginRight: 4 },
  notificationHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 6 },
  notificationTitle: { fontSize: 16, flex: 1, marginRight: 12 },
  timestampContainer: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  timestamp: { fontSize: 12, fontWeight: '400' },
  deleteButton: { padding: 2 },
  unreadBadge: { width: 8, height: 8, borderRadius: 4 },
  notificationMessage: { fontSize: 14, lineHeight: 20, marginBottom: 10 },
  actionButton: { alignSelf: 'flex-start', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20, marginTop: 6 },
  actionButtonText: { fontSize: 14, fontWeight: '500' },
  emptyStateContainer: { alignItems: 'center', marginTop: SPACING.xl, paddingHorizontal: SPACING.lg },
  emptyIconContainer: { width: 80, height: 80, borderRadius: 40, justifyContent: 'center', alignItems: 'center' },
  emptyTitle: { textAlign: 'center', fontSize: TYPOGRAPHY.fontSize.lg, fontWeight: '600', marginTop: SPACING.lg, marginBottom: SPACING.sm },
  emptyMessage: { textAlign: 'center', fontSize: TYPOGRAPHY.fontSize.md, lineHeight: 22, marginBottom: SPACING.xl },
  emptyActions: { gap: SPACING.sm, width: '100%' },
  testButton: { marginBottom: SPACING.sm },
});

export default NotificationsScreen;