import { useNavigation } from '@react-navigation/native';
import React, { useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { BarChart } from 'react-native-chart-kit';
import { Card, DataTable, Text, useTheme } from 'react-native-paper';

import MetricCard from '@/components/reports/MetricCard';
import { SPACING } from '@/theme/theme';
import { RootStackNavigationProp } from '@/types/navigation';
import { formatCurrency } from '@/utils/currency';

const { width: screenWidth } = Dimensions.get('window');

interface ProductsReportProps {
  data: any;
  chartData: any;
}

const ProductsReport: React.FC<ProductsReportProps> = ({ data, chartData }) => {
  const theme = useTheme();
  const navigation = useNavigation<RootStackNavigationProp>();

  const chartConfig = useMemo(() => ({
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(220, 38, 38, ${opacity})`,
    labelColor: () => theme.colors.onSurface,
    style: { borderRadius: 16 },
  }), [theme]);

  return (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title='Total Products'
          value={data.totalProducts.toString()}
          subtitle={`${chartData.categories.length} categories`}
          icon="package"
        />
        <MetricCard
          title='Avg Price'
          value={formatCurrency(data.avgPrice)}
          subtitle='Average product price'
          icon="cash"
        />
        <MetricCard
          title='Inventory Value'
          value={formatCurrency(data.totalInventoryValue, { decimals: 0 })}
          subtitle='Total stock value'
          icon="package"
        />
        <MetricCard
          title='Low Stock'
          value={data.lowStockProducts.length.toString()}
          subtitle='Items need restocking'
          icon="warning-circle"
        />
      </View>

      {chartData.topProducts.length > 0 ? (
        <Card style={styles.chartCard} mode='outlined'>
          <Card.Content>
            <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
              Top Selling Products
            </Text>
            <BarChart
              data={{
                labels: chartData.topProducts.slice(0, 5).map((p: any) => p.name.substring(0, 8)),
                datasets: [{ data: chartData.topProducts.slice(0, 5).map((p: any) => p.sales) }],
              }}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
              showValuesOnTopOfBars
              yAxisLabel=""
              yAxisSuffix=""
            />
          </Card.Content>
        </Card>
      ) : (
        <Text style={[styles.noDataText, { color: theme.colors.onSurfaceVariant }]}>No product sales data available.</Text>
      )}
      
      {chartData.topProducts.length > 0 && (
        <Card style={styles.tableCard} mode='outlined'>
            <Card.Content>
                <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>Product Performance</Text>
                <DataTable>
                    <DataTable.Header>
                        <DataTable.Title>Product</DataTable.Title>
                        <DataTable.Title numeric>Sales</DataTable.Title>
                        <DataTable.Title numeric>Revenue</DataTable.Title>
                    </DataTable.Header>
                    {chartData.topProducts.map((product: any) => (
                        <DataTable.Row key={product.name} onPress={() => navigation.navigate('InventoryItems')}>
                            <DataTable.Cell>{product.name}</DataTable.Cell>
                            <DataTable.Cell numeric>{product.sales}</DataTable.Cell>
                            <DataTable.Cell numeric>{formatCurrency(product.revenue)}</DataTable.Cell>
                        </DataTable.Row>
                    ))}
                </DataTable>
            </Card.Content>
        </Card>
      )}

      {data.lowStockProducts.length > 0 && (
        <Card style={styles.tableCard} mode='outlined'>
          <Card.Content>
            <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.error }]}>
              Low Stock Alert
            </Text>
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Product</DataTable.Title>
                <DataTable.Title numeric>Stock</DataTable.Title>
              </DataTable.Header>
              {data.lowStockProducts.slice(0, 5).map((product: any) => (
                <DataTable.Row key={product.id} onPress={() => navigation.navigate('InventoryItems')}>
                  <DataTable.Cell>{product.name}</DataTable.Cell>
                  <DataTable.Cell numeric>
                    <Text style={{ color: theme.colors.error, fontWeight: '600' }}>{product.stock || 0}</Text>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', marginHorizontal: -SPACING.xs },
  chartCard: { marginBottom: SPACING.lg, borderRadius: 12 },
  tableCard: { marginBottom: SPACING.lg, borderRadius: 12 },
  chartTitle: { fontWeight: '600', marginBottom: SPACING.md },
  chart: { marginVertical: SPACING.sm, borderRadius: 16 },
  noDataText: { textAlign: 'center', padding: SPACING.xl },
});

export default React.memo(ProductsReport);