import React, { useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Card, Text, useTheme } from 'react-native-paper';

import MetricCard from '@/components/reports/MetricCard';
import { SPACING, TYPOGRAPHY } from '@/theme/theme';
import { formatCurrency } from '@/utils/currency';

const { width: screenWidth } = Dimensions.get('window');

interface OverviewReportProps {
  data: any;
  customersData: any;
  chartData: any;
}

const OverviewReport: React.FC<OverviewReportProps> = ({ data, customersData, chartData }) => {
  const theme = useTheme();

  const chartConfig = useMemo(() => ({
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(37, 99, 235, ${opacity})`,
    labelColor: () => theme.colors.onSurface,
    style: { borderRadius: 16 },
    propsForDots: { r: '4', strokeWidth: '2', stroke: theme.colors.primary },
  }), [theme]);

  return (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title='Total Revenue'
          value={formatCurrency(data.totalRevenue)}
          subtitle={`${data.totalOrders} orders`}
          growth={`${data.revenueGrowth.toFixed(1)}%`}
          icon="chart-line"
        />
        <MetricCard
          title='Avg Order Value'
          value={formatCurrency(data.avgOrderValue)}
          subtitle={`${data.completedOrders || 0} completed`}
          growth={`${data.ordersGrowth.toFixed(1)}%`}
          icon="money"
        />
        <MetricCard
          title='Top Product'
          value={chartData.topProducts[0]?.name || 'N/A'}
          subtitle={`${chartData.topProducts[0]?.sales || 0} sold`}
          growth={
            chartData.topProducts[0]?.revenue && data.totalRevenue
              ? `${((chartData.topProducts[0].revenue / data.totalRevenue) * 100).toFixed(1)}%`
              : '0%'
          }
          icon="package"
        />
        <MetricCard
          title='Total Customers'
          value={data.totalCustomers.toString()}
          subtitle={`${customersData.newCustomers} new`}
          growth={data.totalCustomers > 0 ? `${((customersData.newCustomers / data.totalCustomers) * 100).toFixed(1)}%` : '0%'}
          icon="users"
        />
      </View>

      <Card style={styles.chartCard} mode='outlined'>
        <Card.Content>
          <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Daily Sales Trend
          </Text>
          {chartData.dailySales.length > 1 ? (
            <LineChart
              data={{
                labels: chartData.dailySales.slice(-7).map((item: any) => new Date(item.date).toLocaleDateString('en-US', { day: 'numeric' })),
                datasets: [{ data: chartData.dailySales.slice(-7).map((item: any) => item.sales) }],
              }}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          ) : (
            <Text style={[styles.noDataText, {color: theme.colors.onSurfaceVariant}]}>Not enough data to display trend.</Text>
          )}
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  metricsGrid: { 
    flexDirection: 'row', 
    flexWrap: 'wrap',
    marginHorizontal: -SPACING.xs
  },
  chartCard: { 
    marginBottom: SPACING.lg, 
    borderRadius: 12 
  },
  chartTitle: { 
    fontWeight: '600', 
    marginBottom: SPACING.md
  },
  chart: { 
    marginVertical: SPACING.sm, 
    borderRadius: 16 
  },
  noDataText: { 
    textAlign: 'center', 
    padding: SPACING.xl, 
  },
});

export default React.memo(OverviewReport);