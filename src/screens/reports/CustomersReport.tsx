import { useNavigation } from '@react-navigation/native';
import React, { useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'react-native-chart-kit';
import { Card, DataTable, Text, useTheme } from 'react-native-paper';

import MetricCard from '@/components/reports/MetricCard';
import { SPACING } from '@/theme/theme';
import { RootStackNavigationProp } from '@/types/navigation';
import { formatCurrency } from '@/utils/currency';

const { width: screenWidth } = Dimensions.get('window');
const SEGMENT_COLORS = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];

interface CustomersReportProps {
  data: any;
  chartData: any;
}

const CustomersReport: React.FC<CustomersReportProps> = ({ data, chartData }) => {
  const theme = useTheme();
  const navigation = useNavigation<RootStackNavigationProp>();

  const chartConfig = useMemo(() => ({
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    labelColor: () => theme.colors.onSurface,
    style: { borderRadius: 16 },
  }), [theme]);

  const customerSegments = useMemo(() => {
    return Object.entries(data.segments).map(([segment, count], index) => ({
      name: segment,
      population: count as number,
      color: SEGMENT_COLORS[index % SEGMENT_COLORS.length],
      legendFontColor: theme.colors.onSurface,
      legendFontSize: 14,
    }));
  }, [data.segments, theme.colors.onSurface]);
  
  const totalSegments = useMemo(() => Object.values(data.segments).reduce((sum: number, val: any) => sum + val, 0), [data.segments]);

  return (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title='Total Customers'
          value={data.totalCustomers.toString()}
          subtitle={`${data.newCustomers} new`}
          icon="users"
        />
        <MetricCard
          title='Avg Customer Value'
          value={formatCurrency(data.avgCustomerValue)}
          subtitle='Avg. lifetime value'
          icon="user"
        />
        <MetricCard
          title='Loyalty Rate'
          value={`${data.loyaltyRate.toFixed(1)}%`}
          subtitle='Customer retention'
          icon="heart"
        />
        <MetricCard
          title='Churn Rate'
          value={`${data.churnRate.toFixed(1)}%`}
          subtitle='Customer churn'
          icon="user"
        />
      </View>

      {customerSegments.length > 0 && (
        <Card style={styles.chartCard} mode='outlined'>
          <Card.Content>
            <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
              Customer Segments
            </Text>
            <PieChart
              data={customerSegments}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              accessor='population'
              backgroundColor='transparent'
              paddingLeft='15'
              absolute
            />
            <View style={styles.chartLegend}>
              {customerSegments.map((item) => {
                const percentage = totalSegments > 0 ? ((item.population / totalSegments) * 100).toFixed(1) : 0;
                return (
                  <View key={item.name} style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: item.color }]} />
                    <Text variant='bodyMedium' style={{ color: theme.colors.onSurface, flex: 1 }}>{item.name}</Text>
                    <Text variant='bodyMedium' style={{ color: theme.colors.onSurface, fontWeight: '600' }}>{item.population} ({percentage}%)</Text>
                  </View>
                );
              })}
            </View>
          </Card.Content>
        </Card>
      )}

      {chartData.topCustomers.length > 0 && (
        <Card style={styles.tableCard} mode='outlined'>
          <Card.Content>
            <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
              Top Customers
            </Text>
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Customer</DataTable.Title>
                <DataTable.Title numeric>Total Spent</DataTable.Title>
                <DataTable.Title numeric>Orders</DataTable.Title>
              </DataTable.Header>
              {chartData.topCustomers.map((customer: any) => (
                <DataTable.Row key={customer.id} onPress={() => navigation.navigate('Customers')}>
                  <DataTable.Cell>{customer.name}</DataTable.Cell>
                  <DataTable.Cell numeric>{formatCurrency(customer.totalSpent)}</DataTable.Cell>
                  <DataTable.Cell numeric>{customer.totalOrders}</DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', marginHorizontal: -SPACING.xs },
  chartCard: { marginBottom: SPACING.lg, borderRadius: 12 },
  tableCard: { marginBottom: SPACING.lg, borderRadius: 12 },
  chartTitle: { fontWeight: '600', marginBottom: SPACING.md },
  chartLegend: { marginTop: SPACING.md },
  legendItem: { flexDirection: 'row', alignItems: 'center', paddingVertical: SPACING.xs },
  legendColor: { width: 12, height: 12, borderRadius: 6, marginRight: SPACING.sm },
});

export default React.memo(CustomersReport);