import React, { useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { PieChart } from 'react-native-chart-kit';
import { Card, DataTable, Text, useTheme } from 'react-native-paper';

import MetricCard from '@/components/reports/MetricCard';
import { SPACING } from '@/theme/theme';
import { formatCurrency } from '@/utils/currency';

const { width: screenWidth } = Dimensions.get('window');
const PIE_CHART_COLORS = ['#3B82F6', '#10B981', '#F97316', '#EF4444', '#6366F1'];

interface SalesReportProps {
  data: any;
  ordersData: any;
  chartData: any;
}

// FIX: Define a clear type for the data used in the Pie Chart and its legend.
interface PieChartDataItem {
  name: string;
  population: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

const SalesReport: React.FC<SalesReportProps> = ({ data, ordersData, chartData }) => {
  const theme = useTheme();

  const chartConfig = useMemo(() => ({
    color: (opacity = 1) => theme.colors.onSurface,
    labelColor: (opacity = 1) => theme.colors.onSurface,
  }), [theme]);
  
  // FIX: Add the explicit return type `PieChartDataItem[]` to the useMemo hook.
  const pieChartData = useMemo((): PieChartDataItem[] => 
    chartData.categories.map((category: any, index: number) => ({
      name: category.name,
      population: category.revenue,
      color: PIE_CHART_COLORS[index % PIE_CHART_COLORS.length],
      legendFontColor: theme.colors.onSurface,
      legendFontSize: 14,
    })), [chartData.categories, theme.colors.onSurface]);

  return (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title="Today's Sales"
          value={formatCurrency(data.todaysSales)}
          subtitle={`${ordersData.ordersToday} orders`}
          growth={`${data.growthRate.toFixed(1)}%`}
          icon="sun"
        />
        <MetricCard
          title='Week Sales'
          value={formatCurrency(data.weekSales)}
          subtitle={`${ordersData.ordersWeek} orders`}
          icon="calendar"
        />
        <MetricCard
          title='Month Sales'
          value={formatCurrency(data.monthSales)}
          subtitle={`${data.completionRate.toFixed(1)}% completed`}
          icon="calendar"
        />
        <MetricCard
          title='Pending Orders'
          value={ordersData.pending.toString()}
          subtitle='Awaiting completion'
          icon="clock"
        />
      </View>

      {chartData.categories.length > 0 ? (
        <Card style={styles.chartCard} mode='outlined'>
          <Card.Content>
            <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
              Sales by Category
            </Text>
             <PieChart
                data={pieChartData}
                width={screenWidth - 64}
                height={220}
                chartConfig={chartConfig}
                accessor='population'
                backgroundColor='transparent'
                paddingLeft='15'
                absolute
              />
              <View style={styles.chartLegend}>
                {/* FIX: Add the specific type `PieChartDataItem` to the `item` parameter. */}
                {pieChartData.map((item: PieChartDataItem) => (
                  <View key={item.name} style={styles.legendItem}>
                    <View style={[styles.legendColor, { backgroundColor: item.color }]} />
                    <Text variant='bodyMedium' style={{ color: theme.colors.onSurface, flex: 1 }}>{item.name}</Text>
                    <Text variant='bodyMedium' style={{ color: theme.colors.onSurface, fontWeight: '600' }}>{formatCurrency(item.population)}</Text>
                  </View>
                ))}
              </View>
          </Card.Content>
        </Card>
      ) : (
        <Text style={[styles.noDataText, { color: theme.colors.onSurfaceVariant }]}>
          No category data available.
        </Text>
      )}

      {chartData.categories.length > 0 && (
        <Card style={styles.tableCard} mode='outlined'>
          <Card.Content>
            <Text variant='titleLarge' style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
              Category Performance
            </Text>
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Category</DataTable.Title>
                <DataTable.Title numeric>Sales</DataTable.Title>
                <DataTable.Title numeric>Revenue</DataTable.Title>
              </DataTable.Header>
              {chartData.categories.slice(0, 5).map((category: any) => (
                <DataTable.Row key={category.name}>
                  <DataTable.Cell>{category.name}</DataTable.Cell>
                  <DataTable.Cell numeric>{category.sales}</DataTable.Cell>
                  <DataTable.Cell numeric>{formatCurrency(category.revenue)}</DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', marginHorizontal: -SPACING.xs },
  chartCard: { marginBottom: SPACING.lg, borderRadius: 12 },
  tableCard: { marginBottom: SPACING.lg, borderRadius: 12 },
  chartTitle: { fontWeight: '600', marginBottom: SPACING.md },
  noDataText: { textAlign: 'center', padding: SPACING.xl },
  chartLegend: { marginTop: SPACING.md },
  legendItem: { flexDirection: 'row', alignItems: 'center', paddingVertical: SPACING.xs },
  legendColor: { width: 12, height: 12, borderRadius: 6, marginRight: SPACING.sm },
});

export default React.memo(SalesReport);