import { useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Camera } from 'expo-camera';
import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Linking, AppState, ActivityIndicator } from 'react-native';
import { Text, But<PERSON>, Card } from 'react-native-paper';

import Header from '../components/navigation/Header';
import QRScanner from '../components/scanner/QRScanner';
import { useData } from '../context/DataContext'; // Added
import { useTheme } from '../context/ThemeContext';
import { useToast } from '../context/ToastContext';
import QRCodeService, { EntityType, ParseResult } from '../services/QRCodeService';
import { RootStackParamList } from '../types/navigation';
import { PhosphorIcon } from '../utils/phosphorIconRegistry';

// --- Type Definitions ---
type QRScannerScreenNavigationProp = StackNavigationProp<RootStackParamList, 'QRScanner'>;
interface Props { navigation: QRScannerScreenNavigationProp; }
type ScreenStatus = 'initializing' | 'permission_denied' | 'scanning' | 'result';


// --- Child Component for Permission Denied State ---
const PermissionDeniedView: React.FC<{
  onOpenSettings: () => void;
  onGoBack: () => void;
}> = ({ onOpenSettings, onGoBack }) => {
  const { theme } = useTheme();
  return (
    <View style={permissionStyles.centerContent}>
      <PhosphorIcon name="camera-slash" size={64} color={theme.colors.onSurfaceVariant} />
      <Text style={[permissionStyles.permissionText, { color: theme.colors.onSurface }]}>
        Camera Permission Required
      </Text>
      <Text style={[permissionStyles.permissionSubtext, { color: theme.colors.onSurfaceVariant }]}>
        To scan QR codes, please grant camera permission in your device settings.
      </Text>
      <Button mode="contained" onPress={onOpenSettings} style={permissionStyles.actionButton}>
        Open Settings
      </Button>
      <Button mode="outlined" onPress={onGoBack} style={{ marginTop: 10 }}>
        Go Back
      </Button>
    </View>
  );
};

const permissionStyles = StyleSheet.create({
  centerContent: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24 },
  permissionText: { fontSize: 18, fontWeight: '500', marginTop: 16, textAlign: 'center' },
  permissionSubtext: { textAlign: 'center', marginVertical: 8, lineHeight: 22 },
  actionButton: { marginTop: 24, width: '80%' },
});


// --- Child Component for Result State ---
const ScannerResultView: React.FC<{
  result: ParseResult;
  onReset: () => void;
  onDone: () => void;
}> = ({ result, onReset, onDone }) => {
  const { theme } = useTheme();
  return (
    <View style={resultStyles.centerContent}>
      <Card style={[resultStyles.resultCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Title
          title={result.success ? 'Scan Successful' : 'Scan Failed'}
          subtitle={result.data?.type ? `Type: ${result.data.type}` : result.error}
        />
        <Card.Content>
          <Text variant="bodyMedium">
            {result.success
              ? `Data: ${JSON.stringify(result.data)}`
              : `Raw Data: ${result.rawData || 'N/A'}`}
          </Text>
        </Card.Content>
        <Card.Actions>
          <Button onPress={onReset}>Scan Again</Button>
          <Button mode="contained" onPress={onDone}>Done</Button>
        </Card.Actions>
      </Card>
    </View>
  );
};

const resultStyles = StyleSheet.create({
  centerContent: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 },
  resultCard: { width: '100%' },
});


// --- Main Screen Component ---
const QRScannerScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const { showError, showToast } = useToast();
  const { state } = useData(); // Added
  const [status, setStatus] = useState<ScreenStatus>('initializing');
  const [scannedResult, setScannedResult] = useState<ParseResult | null>(null);

  const requestCameraPermission = useCallback(async () => {
    const { status } = await Camera.requestCameraPermissionsAsync();
    setStatus(status === 'granted' ? 'scanning' : 'permission_denied');
  }, []);

  useFocusEffect(
    useCallback(() => {
      requestCameraPermission();
      const subscription = AppState.addEventListener('change', nextAppState => {
        if (nextAppState === 'active') {
          requestCameraPermission();
        }
      });
      return () => subscription.remove();
    }, [requestCameraPermission])
  );

  const openAppSettings = () => Linking.openSettings();

  const handleBarCodeScanned = (data: string) => {
    if (status !== 'scanning') {return;}

    const result = QRCodeService.parseQRString(data);
    setScannedResult(result); // Keep this for the result view if needed for other types

    if (result.success && result.data) {
      switch (result.data.type) {
        case EntityType.Order:
          const orderId = result.data.id;
          const foundOrder = state.orders.find(order => order.id === orderId); // Access state.orders
          if (foundOrder) {
            navigation.navigate('OrderDetails', { order: foundOrder }); // Pass the entire order object
          } else {
            showError(`Order with ID ${orderId} not found.`);
            setStatus('result'); // Show result card for error
          }
          break;
        case EntityType.Customer:
          showToast(`Customer QR Scanned: ${result.data.name}`);
          setStatus('result'); // Still show result card for other types
          break;
        case EntityType.Product:
          showToast(`Product QR Scanned: ${result.data.name}`);
          setStatus('result'); // Still show result card for other types
          break;
        default:
          showError('Scanned an unknown QR code type.');
          setStatus('result'); // Still show result card for unknown types
      }
    } else {
      showError(result.error || 'Invalid QR Code');
      setStatus('result'); // Still show result card for invalid QR codes
    }
  };

  const resetScanner = () => {
    setScannedResult(null);
    setStatus('scanning');
  };

  const renderContent = () => {
    switch (status) {
      case 'initializing':
        return (
          <View style={styles.centerContent}>
            <ActivityIndicator />
            <Text style={{ marginTop: 16, color: theme.colors.onSurface }}>Initializing Camera...</Text>
          </View>
        );
      case 'permission_denied':
        return <PermissionDeniedView onOpenSettings={openAppSettings} onGoBack={navigation.goBack} />;
      case 'scanning':
        return <QRScanner onScan={handleBarCodeScanned} isVisible={true} />;
      case 'result':
        if (!scannedResult) {return null;}
        return <ScannerResultView result={scannedResult} onReset={resetScanner} onDone={navigation.goBack} />;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title='QR Scanner' showBack={true} onBackPress={navigation.goBack} />
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  centerContent: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24 },
});

export default QRScannerScreen;