import { useNavigation } from '@react-navigation/native';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { ActivityIndicator, FlatList, ListRenderItem, RefreshControl, StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { DataCard } from '@/components/cards';
import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import { Button } from '@/components/ui';
import ChipGroup from '@/components/ui/ChipGroup';
import EmptyState from '@/components/ui/EmptyState';
import StatCardGroup from '@/components/ui/StatCardGroup';
import { EnhancedCustomer, useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import { useDebounce } from '@/hooks/useDebounce';
import { StatCard } from '@/types';
import { PhosphorIconName } from '@/utils/phosphorIconRegistry';

// --- Constants ---
const VIP_SPEND_THRESHOLD = 1000;
const RECENT_TIMESPAN_DAYS = 30;
const RECENT_TIMESPAN_MS = RECENT_TIMESPAN_DAYS * 24 * 60 * 60 * 1000;

const filterOptions: { id: string; label: string; icon: PhosphorIconName }[] = [
  { id: 'All', label: 'All', icon: 'users' },
  { id: 'Recent', label: 'Recent', icon: 'clock' },
  { id: 'Active', label: 'Active', icon: 'check-circle' },
  { id: 'VIP', label: 'VIP', icon: 'crown' },
];

interface CustomerStats {
  total: number;
  vip: number;
  active: number;
  recent: number;
}

const SelectionHeader = memo<{
  count: number;
  onCancel: () => void;
  onDelete: () => void;
}>(({ count, onCancel, onDelete }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.selectionHeader, { paddingTop: insets.top, backgroundColor: theme.colors.surface }]}>
        <Button icon="x" onPress={onCancel} variant="ghost" />
        <Text style={[styles.selectionTitle, { color: theme.colors.onSurface }]}>{count} Selected</Text>
        <Button icon="trash" onPress={onDelete} variant="ghost" textColor={theme.colors.error} />
    </View>
  );
});

const CustomersScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const { state, actions } = useData();
  const { enhancedCustomers, isDataLoaded } = state;
  const { showSuccess, showError } = useToast();
  
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300); // Debounce search query
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [refreshing, setRefreshing] = useState(false);
  
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedCustomers, setSelectedCustomers] = useState<Set<string>>(new Set());
  const [actionSheetVisible, setActionSheetVisible] = useState(false);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await actions.reloadData();
      showSuccess('Customers refreshed');
    } catch (error) {
      showError('Failed to refresh customers');
    } finally {
      setRefreshing(false);
    }
  }, [actions, showSuccess, showError]);

  const filteredCustomers = useMemo(() => {
    let filtered = enhancedCustomers || [];

    if (selectedFilter === 'Recent') {
      const timeAgo = Date.now() - RECENT_TIMESPAN_MS;
      filtered = filtered.filter(c => c.lastOrderDate && c.lastOrderDate.getTime() > timeAgo);
    } else if (selectedFilter === 'Active') {
      filtered = filtered.filter(c => c.activeOrders > 0);
    } else if (selectedFilter === 'VIP') {
      filtered = filtered.filter(c => c.totalSpent >= VIP_SPEND_THRESHOLD);
    }

    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter(
        c => c.name?.toLowerCase().includes(query) || c.phone?.includes(query) || c.email?.toLowerCase().includes(query)
      );
    }
    
    // PERF_NOTE: localeCompare can be slow on very large lists.
    // If performance issues persist, consider a simpler sort or server-side sorting/filtering.
    return filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
  }, [enhancedCustomers, debouncedSearchQuery, selectedFilter]);

  const customerStats = useMemo((): CustomerStats => {
    const customers = enhancedCustomers || [];
    const timeAgo = Date.now() - RECENT_TIMESPAN_MS;

    if (!customers.length) {
      return { total: 0, vip: 0, active: 0, recent: 0 };
    }

    return customers.reduce(
      (stats, c) => {
        stats.total++;
        if (c.totalSpent >= VIP_SPEND_THRESHOLD) stats.vip++;
        if (c.activeOrders > 0) stats.active++;
        if (c.lastOrderDate && c.lastOrderDate.getTime() > timeAgo) stats.recent++;
        return stats;
      },
      { total: 0, vip: 0, active: 0, recent: 0 }
    );
  }, [enhancedCustomers]);

  const handleAddCustomer = () => navigation.navigate('AddCustomer');

  const handleToggleSelection = useCallback((customerId: string) => {
    setSelectedCustomers(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(customerId)) newSelected.delete(customerId);
      else newSelected.add(customerId);
      if (newSelected.size === 0) setIsSelectionMode(false);
      return newSelected;
    });
  }, []);

  const handleCustomerPress = useCallback((customer: EnhancedCustomer) => {
    if (isSelectionMode) {
      handleToggleSelection(customer.id);
    } else {
      navigation.navigate('CustomerDetails', { customerId: customer.id });
    }
  }, [isSelectionMode, navigation, handleToggleSelection]);

  const handleLongPress = useCallback((customer: EnhancedCustomer) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedCustomers(new Set([customer.id]));
    }
  }, [isSelectionMode]);
  
  const handleCancelSelection = useCallback(() => {
    setSelectedCustomers(new Set());
    setIsSelectionMode(false);
  }, []);
  
  const handleDeleteConfirm = useCallback(async () => {
    setActionSheetVisible(false);
    const idsToDelete = Array.from(selectedCustomers);
    try {
      await Promise.all(idsToDelete.map(id => actions.deleteCustomer(id)));
      showSuccess(`Deleted ${idsToDelete.length} customer(s)`);
      handleCancelSelection();
    } catch {
      showError('Failed to delete customers');
    }
  }, [selectedCustomers, actions, showSuccess, showError, handleCancelSelection]);

  const renderCustomerItem: ListRenderItem<EnhancedCustomer> = useCallback(
    ({ item }) => (
      <DataCard
        cardType="customer"
        id={item.id}
        name={item.name}
        phone={item.phone}
        totalSpent={item.totalSpent}
        totalOrders={item.totalOrders}
        lastOrderDate={item.lastOrderDate}
        onPress={() => handleCustomerPress(item)}
        onLongPress={() => handleLongPress(item)}
        onCheckboxPress={() => handleToggleSelection(item.id)}
        selected={selectedCustomers.has(item.id)}
        showSelectionIndicator={isSelectionMode}
      />
    ),
    [handleCustomerPress, handleLongPress, handleToggleSelection, isSelectionMode, selectedCustomers]
  );
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {isSelectionMode ? (
        <SelectionHeader
          count={selectedCustomers.size}
          onCancel={handleCancelSelection}
          onDelete={() => setActionSheetVisible(true)}
        />
      ) : (
        <Header
          title='Customers'
          showBack={navigation.canGoBack()}
          onBackPress={navigation.goBack}
          showSearch={true}
          searchPlaceholder='Search by name, phone, or email...'
          onSearchChange={setSearchQuery}
          actions={[{ icon: 'plus', onPress: handleAddCustomer }]}
        />
      )}

      {!isSelectionMode && (
        <View style={{ backgroundColor: theme.colors.background, paddingHorizontal: 16, zIndex: 1 }}>
          <ChipGroup
            filters={filterOptions}
            selectedFilter={selectedFilter}
            onFilterChange={filter => setSelectedFilter(String(filter))}
            showCounts={true}
            data={enhancedCustomers}
            borderRadius='full-rounded'
            style={{ marginTop: 12, marginBottom: 4 }}
          />
        </View>
      )}

      {/* --- FIXED: Simplified loading condition --- */}
      {!isDataLoaded ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredCustomers}
          renderItem={renderCustomerItem}
          keyExtractor={item => item.id}
          style={[styles.content, { paddingTop: 4 }]}
          contentContainerStyle={{ paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh}
              colors={[theme.colors.primary]} tintColor={theme.colors.primary}
            />
          }
          ListHeaderComponent={!isSelectionMode ? () => (
            <StatCardGroup
              cards={
                [
                  { key: 'total', title: 'Total Customers', value: customerStats.total.toString(), icon: 'users' },
                  { key: 'vip', title: 'VIP Clients', value: customerStats.vip.toString(), icon: 'crown' },
                ] as StatCard[]
              }
              columns={2} showTitle={false} containerStyle={{ marginBottom: 16 }}
            />
          ) : null}
          ListEmptyComponent={() => (
            // This component will now correctly show when the list is empty for any reason
            <EmptyState
              type='custom' icon='users' title='No Customers Found'
              description={searchQuery ? 'Try a different search term.' : 'Add your first customer to get started.'}
              actionLabel='Add Customer' onActionPress={handleAddCustomer}
            />
          )}
        />
      )}
      
      <ActionSheet
        visible={actionSheetVisible}
        onDismiss={() => setActionSheetVisible(false)}
        title={`Delete ${selectedCustomers.size} customer(s)?`}
        description="This action cannot be undone."
        options={[
          { text: 'Delete', onPress: handleDeleteConfirm, style: 'destructive' },
        ]}
        cancelText="Cancel"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    height: 56,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  selectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default CustomersScreen;