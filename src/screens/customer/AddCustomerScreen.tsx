import React, { useReducer, useCallback, useMemo, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  TextInput as RNTextInput,
} from 'react-native';
import { Text } from 'react-native-paper';
import { useSafeAreaInsets, EdgeInsets } from 'react-native-safe-area-context';
import Header from '../../components/navigation/Header';
import Button from '../../components/ui/Button';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme, ThemeContextType } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { useSecurity } from '../../hooks/useSecurity';
import LoggingService from '../../services/LoggingService';
import {
  formatPhoneNumberForDisplay,
  isValidPhoneNumber,
  preparePhoneNumberForStorage,
} from '../../utils/phoneUtils';

// --- TYPES ---
type FormData = {
  name: string;
  email: string;
  phone: string;
  address: string;
  gender: string;
};

type FormState = {
  data: FormData;
  errors: Partial<Record<keyof FormData, string>>;
  securityWarnings: Partial<Record<keyof FormData, string>>;
};

type FormAction =
  | { type: 'UPDATE_FIELD'; field: keyof FormData; value: string }
  | { type: 'SET_ERRORS'; errors: Partial<Record<keyof FormData, string>> }
  | { type: 'SET_FIELD_ERROR'; field: keyof FormData; error?: string }
  | { type: 'SET_SECURITY_WARNING'; field: keyof FormData; warning?: string };

const WALK_IN_CUSTOMER_PLACEHOLDER = 'Walk In';

// --- REDUCER (Centralized State Logic) ---
const formReducer = (state: FormState, action: FormAction): FormState => {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return { ...state, data: { ...state.data, [action.field]: action.value } };
    case 'SET_ERRORS':
      return { ...state, errors: action.errors };
    case 'SET_FIELD_ERROR': {
      const newErrors = { ...state.errors };
      if (action.error) newErrors[action.field] = action.error;
      else delete newErrors[action.field];
      return { ...state, errors: newErrors };
    }
    case 'SET_SECURITY_WARNING': {
        const newWarnings = { ...state.securityWarnings };
        if (action.warning) newWarnings[action.field] = action.warning;
        else delete newWarnings[action.field];
        return { ...state, securityWarnings: newWarnings };
    }
    default:
      return state;
  }
};

// --- CUSTOM HOOK (Encapsulated Form Logic) ---
const useCustomerForm = (isEditing: boolean, initialData: any, navigation: any) => {
  const { actions } = useData();
  const { showSuccess, showError, showWarning } = useToast();
  const { validateSecureInput } = useSecurity({
    enableRealTimeValidation: true,
  });

  const initialState: FormState = {
    data: {
      name: initialData.name === WALK_IN_CUSTOMER_PLACEHOLDER ? '' : initialData.name || '',
      email: initialData.email || '',
      phone: initialData.phone ? formatPhoneNumberForDisplay(initialData.phone) : '',
      address: initialData.address || '',
      gender: initialData.gender || 'male',
    },
    errors: {},
    securityWarnings: {},
  };

  const [state, dispatch] = useReducer(formReducer, initialState);

  const validateField = useCallback(async (field: keyof FormData, value: string): Promise<string | undefined> => {
    let error: string | undefined;
    const trimmedValue = value.trim();

    // Only phone is required. All other fields are optional.
    switch (field) {
        case 'phone':
            if (!trimmedValue) {
                error = 'Phone number is required';
            } else if (!isValidPhoneNumber(trimmedValue)) {
                error = 'Invalid phone number';
            }
            break;
        case 'email':
            // Validate email format only if a value is entered.
            if (trimmedValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmedValue)) {
                error = 'Invalid email format';
            }
            break;
        default:
            // All other fields (name, address, gender) are optional.
            break;
    }

    if (error) return error;

    // Skip security validation for empty optional fields
    if (!trimmedValue && field !== 'phone') {
      // Clear any existing security warnings for empty optional fields
      dispatch({ type: 'SET_SECURITY_WARNING', field, warning: undefined });
      return undefined;
    }

    // Security validation (if basic checks pass and field has content)
    try {
        const securityResult = await validateSecureInput(value, field === 'email' ? 'email' : field === 'phone' ? 'phone' : 'text');
        if (securityResult.sanitizedValue !== value) {
            dispatch({ type: 'UPDATE_FIELD', field, value: securityResult.sanitizedValue });
            dispatch({ type: 'SET_SECURITY_WARNING', field, warning: 'Input was sanitized for security' });
            showWarning('Input was sanitized for security');
        } else {
            dispatch({ type: 'SET_SECURITY_WARNING', field, warning: undefined });
        }
        if (!securityResult.isValid) {
            return securityResult.errors[0] || 'Security validation failed';
        }
    } catch (e) {
        LoggingService.error(`Security validation failed for ${field}`, 'CUSTOMER_FORM', e as Error);
        // Don't return error for optional fields if security validation fails
        if (field !== 'phone' && !trimmedValue) {
          return undefined;
        }
    }
    return undefined; // No error
  }, [showWarning, validateSecureInput]);
  
  const handleFieldChange = useCallback(async (field: keyof FormData, value: string) => {
    dispatch({ type: 'UPDATE_FIELD', field, value });
    const error = await validateField(field, value);
    dispatch({ type: 'SET_FIELD_ERROR', field, error });
  }, [validateField]);
  
  const handleSave = useCallback(async () => {
    // Run validation on all fields before saving
    const validationPromises = Object.entries(state.data).map(([key, value]) =>
        validateField(key as keyof FormData, value)
    );
    const results = await Promise.all(validationPromises);
    const newErrors: Partial<Record<keyof FormData, string>> = {};
    results.forEach((error, index) => {
        if (error) {
            const field = Object.keys(state.data)[index] as keyof FormData;
            newErrors[field] = error;
        }
    });

    if (Object.keys(newErrors).length > 0) {
        dispatch({ type: 'SET_ERRORS', errors: newErrors });
        showError('Please fix the errors before saving.');
        return;
    }
    
    try {
      const customerData = {
        ...state.data,
        phone: preparePhoneNumberForStorage(state.data.phone),
        name: state.data.name.trim() === '' ? WALK_IN_CUSTOMER_PLACEHOLDER : state.data.name,
        isActive: true,
      };

      if (isEditing) {
        await actions.updateCustomer({ ...initialData, ...customerData });
        showSuccess('Customer updated successfully!');
      } else {
        await actions.addCustomer(customerData);
        showSuccess('Customer added successfully!');
      }
      navigation.goBack();
    } catch (error) {
      LoggingService.error("Failed to save customer", 'ADD_CUSTOMER', error as Error);
      showError('Failed to save customer. Please try again.');
    }
  }, [state.data, isEditing, initialData, navigation, actions, showError, showSuccess, validateField]);

  return { state, handleFieldChange, handleSave };
};

// --- SELF-CONTAINED GENDER COMPONENT ---
const GenderSelector = React.memo(({ selectedGender, onSelect }: { selectedGender: string; onSelect: (field: keyof FormData, value: string) => void; }) => {
    const theme = useTheme();
    const styles = useMemo(() => createGenderSelectorStyles(theme), [theme]);

    return (
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>Gender (Optional)</Text>
        <View style={styles.genderContainer}>
          {['male', 'female', 'other'].map(gender => (
            <TouchableOpacity key={gender} style={styles.radioOption} onPress={() => onSelect('gender', gender)}>
              <View style={[styles.radioCircle, { borderColor: selectedGender === gender ? theme.colors.primary : theme.colors.outline }]}>
                {selectedGender === gender && <View style={[styles.radioDot, { backgroundColor: theme.colors.primary }]} />}
              </View>
              <Text style={[styles.radioLabel, { color: selectedGender === gender ? theme.colors.primary : theme.colors.onSurface }]}>
                {gender.charAt(0).toUpperCase() + gender.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
});

// --- UI COMPONENT (Cleaner) ---
const AddCustomerScreen: React.FC<{ navigation: any; route?: { params?: { isEditing?: boolean; customerData?: any } } }> = ({ navigation, route }) => {
  const isEditing = route?.params?.isEditing || false;
  const initialData = route?.params?.customerData || {};
  
  const { state, handleFieldChange, handleSave } = useCustomerForm(isEditing, initialData, navigation);
  
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const styles = useMemo(() => createScreenStyles(theme, insets), [theme, insets]);

  const emailInputRef = useRef<RNTextInput>(null);
  const phoneInputRef = useRef<RNTextInput>(null);
  const addressInputRef = useRef<RNTextInput>(null);

  return (
    <View style={styles.container}>
      <Header title={isEditing ? 'Edit Customer' : 'Add New Customer'} onBackPress={() => navigation.goBack()} showBack={true} />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer} keyboardShouldPersistTaps="handled">
        
        <TextInput label='Name (Optional)' value={state.data.name} onChangeText={value => handleFieldChange('name', value)} error={state.errors.name} returnKeyType="next" onSubmitEditing={() => phoneInputRef.current?.focus()} blurOnSubmit={false} style={{ marginBottom: 8 }}/>
        
        <TextInput ref={phoneInputRef} label='Phone Number *' value={state.data.phone} onChangeText={value => handleFieldChange('phone', value)} keyboardType='phone-pad' error={state.errors.phone} returnKeyType="next" onSubmitEditing={() => emailInputRef.current?.focus()} blurOnSubmit={false} style={{ marginBottom: 8 }} />
        
        <TextInput ref={emailInputRef} label='Email (Optional)' value={state.data.email} onChangeText={value => handleFieldChange('email', value)} keyboardType='email-address' autoCapitalize='none' error={state.errors.email} returnKeyType="next" onSubmitEditing={() => addressInputRef.current?.focus()} blurOnSubmit={false} style={{ marginBottom: 8 }} />

        <TextInput ref={addressInputRef} label='Address (Optional)' value={state.data.address} onChangeText={value => handleFieldChange('address', value)} multiline returnKeyType="done" onSubmitEditing={handleSave} style={styles.addressInput}/>
        
        <GenderSelector selectedGender={state.data.gender} onSelect={handleFieldChange} />

        <View style={styles.buttonContainer}>
            <Button variant='outline' onPress={() => navigation.goBack()} style={styles.button}>Cancel</Button>
            <Button variant='primary' onPress={handleSave} style={styles.button}>
              {isEditing ? 'Update' : 'Create'}
            </Button>
        </View>
      </ScrollView>
    </View>
  );
};

// --- STYLES ---
const createScreenStyles = (theme: ThemeContextType, insets: EdgeInsets) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.colors.background },
  scrollView: { flex: 1 },
  contentContainer: { paddingHorizontal: 16, paddingVertical: 16 },
  addressInput: { minHeight: 80, textAlignVertical: 'top' },
  buttonContainer: { flexDirection: 'row', gap: 12, marginTop: 24 },
  button: { flex: 1 },
});

const createGenderSelectorStyles = (theme: ThemeContextType) => StyleSheet.create({
    fieldContainer: { marginTop: 0, marginBottom: 4 },
    fieldLabel: { fontSize: 14, fontWeight: '500', color: theme.colors.onSurface, marginBottom: 8, paddingHorizontal: 4 },
    genderContainer: { flexDirection: 'row', gap: 8 },
    radioOption: { flexDirection: 'row', alignItems: 'center', paddingVertical: 8, paddingHorizontal: 8 },
    radioCircle: { width: 20, height: 20, borderRadius: 12, borderWidth: 2, alignItems: 'center', justifyContent: 'center', marginRight: 8 },
    radioDot: { width: 12, height: 12, borderRadius: 6 },
    radioLabel: { fontSize: 16, fontWeight: '400' },
});

export default AddCustomerScreen;