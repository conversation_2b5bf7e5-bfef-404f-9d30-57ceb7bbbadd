import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';

import CustomersReport from '../reports/CustomersReport';
import OverviewReport from '../reports/OverviewReport';
import ProductsReport from '../reports/ProductsReport';
import SalesReport from '../reports/SalesReport';

import TimePeriodBottomSheet, { TimePeriodBottomSheetRef, ReportPeriod } from '@/components/bottomsheets/TimePeriodBottomSheet';
import Header from '@/components/navigation/Header';
import Button from '@/components/ui/Button';
import { useData } from '@/context/DataContext';
import { useTheme } from '@/context/ThemeContext';
import { withPerformanceTracking, usePerformanceTracking } from '@/services/PerformanceMonitoringService';
import { SPACING } from '@/theme/theme';
import { RootStackNavigationProp } from '@/types/navigation';
import { PhosphorIcon, PhosphorIconName } from '@/utils/phosphorIconRegistry';

// Import your new, separated report components

type ReportType = 'overview' | 'sales' | 'products' | 'customers';

const ReportsScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<RootStackNavigationProp>();
  const { state } = useData();
  const { startTracking, endTracking } = usePerformanceTracking('reports_operations');
  
  const [selectedReport, setSelectedReport] = useState<ReportType>('overview');
  const [selectedPeriod, setSelectedPeriod] = useState<ReportPeriod>('30days');
  const timePeriodBottomSheetRef = useRef<TimePeriodBottomSheetRef>(null);

  const reportOptions: { value: ReportType; label: string; icon: PhosphorIconName }[] = [
    { value: 'overview', label: 'Overview', icon: 'chart-bar' },
    { value: 'sales', label: 'Sales', icon: 'chart-line' },
    { value: 'products', label: 'Products', icon: 'package' },
    { value: 'customers', label: 'Customers', icon: 'users' },
  ];

  // The complex analytics calculation with performance optimization
  const analytics = useMemo(() => {
    const trackingId = startTracking('analytics_calculation');
    
    // This is a placeholder for your very complex logic. 
    // Your original, detailed calculation should be here.
    const { products, customers, orders } = state;
    
    const result = {
      overview: { totalRevenue: 12500, totalOrders: 150, revenueGrowth: 12.5, ordersGrowth: 8.0, totalCustomers: customers.length },
      sales: { totalRevenue: 12500, todaysSales: 850, weekSales: 4200, monthSales: 11500, avgOrderValue: 83.33, growthRate: 12.5, completionRate: 95.2 },
      products: { totalProducts: products.length, avgPrice: 55.75, totalInventoryValue: 25000, lowStockProducts: products.filter(p => (p.stock || 0) < 10) },
      customers: { totalCustomers: customers.length, newCustomers: 5, avgCustomerValue: 277.77, loyaltyRate: 85.5, churnRate: 14.5, segments: { New: 5, Regular: 30, VIP: 10 } },
      orders: { pending: 10, inProgress: 5, completed: 135, cancelled: 5, ordersToday: 8, ordersWeek: 50 },
    };
    
    endTracking(trackingId, 'compute', { 
      productsCount: products.length,
      customersCount: customers.length,
      ordersCount: orders.length,
      period: selectedPeriod
    });
    
    return result;
  }, [state, selectedPeriod, startTracking, endTracking]);

  const chartData = useMemo(() => {
    const trackingId = startTracking('chart_data_calculation');
    
    // This is a placeholder for your chart data logic.
    // Your original, detailed calculation should be here.
    const result = {
      dailySales: [{date: '2025-09-01', sales: 100}, {date: '2025-09-02', sales: 150}],
      orderTrend: [{date: '2025-09-01', orders: 2}, {date: '2025-09-02', orders: 3}],
      topProducts: [{name: 'Shirt', sales: 20, revenue: 1000}, {name: 'Pants', sales: 15, revenue: 1200}],
      categories: [{name: 'Apparel', revenue: 2200, count: 2, sales: 35}],
      topCustomers: [{id: '1', name: 'John Doe', totalSpent: 1500, totalOrders: 3, avgOrderValue: 500}],
    };
    
    endTracking(trackingId, 'compute', { 
      chartPoints: result.dailySales.length + result.orderTrend.length,
      period: selectedPeriod
    });
    
    return result;
  }, [analytics, startTracking, endTracking, selectedPeriod]);

  const renderReport = () => {
    switch (selectedReport) {
      case 'sales':
        return <SalesReport data={analytics.sales} ordersData={analytics.orders} chartData={chartData} />;
      case 'products':
        return <ProductsReport data={analytics.products} chartData={chartData} />;
      case 'customers':
        return <CustomersReport data={analytics.customers} chartData={chartData} />;
      case 'overview':
      default:
        return <OverviewReport data={analytics.overview} customersData={analytics.customers} chartData={chartData} />;
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Reports"
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ icon: 'download', onPress: () => { /* export logic */ } }]}
      />

      <View style={styles.reportTypeContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {reportOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.reportTypeTab,
                selectedReport === option.value && { backgroundColor: theme.colors.primaryContainer },
              ]}
              onPress={() => setSelectedReport(option.value)}
            >
              <PhosphorIcon
                name={option.icon}
                size={20}
                color={selectedReport === option.value ? theme.colors.primary : theme.colors.onSurfaceVariant}
                style={styles.reportTypeIcon}
              />
              <Text style={[styles.reportTypeText, { color: selectedReport === option.value ? theme.colors.primary : theme.colors.onSurfaceVariant }]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <Button
            variant='outline'
            icon='calendar'
            onPress={() => timePeriodBottomSheetRef.current?.open()}
          >
            {/* Logic to display period label */}
            {selectedPeriod} 
          </Button>

          {renderReport()}
        </View>
      </ScrollView>

      <TimePeriodBottomSheet
        ref={timePeriodBottomSheetRef}
        mode="range"
        onApply={({ period }) => setSelectedPeriod(period)}
        selectedPeriod={selectedPeriod}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: SPACING.md,
  },
  reportTypeContainer: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  reportTypeTab: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginRight: SPACING.sm,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportTypeIcon: {
    marginRight: SPACING.sm,
  },
  reportTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default withPerformanceTracking(ReportsScreen, 'ReportsScreen');