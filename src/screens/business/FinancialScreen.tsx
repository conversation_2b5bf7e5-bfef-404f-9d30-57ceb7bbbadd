
import React, { useCallback, useEffect, useState, useRef } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';

import CashReconciliationBottomSheet, { CashReconciliationBottomSheetRef } from '../../components/bottomsheets/CashReconciliationBottomSheet';
import TimePeriodBottomSheet, { TimePeriodBottomSheetRef } from '../../components/bottomsheets/TimePeriodBottomSheet';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
// import { useFinancial } from '../../context/FinancialContext';
import { useTheme } from '../../context/ThemeContext';
import { useToast } from '../../context/ToastContext';
import { usePermissions } from '../../hooks/usePermissions';
import { FinancialService } from '../../services/financialService';
import LoggingService from '../../services/LoggingService';
import { withPerformanceTracking, usePerformanceTracking } from '../../services/PerformanceMonitoringService';
import { SPACING } from '../../theme/theme';
import { FinancialScreenNavigationProp } from '../../types/navigation';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

type ReportPeriod = 'today' | '7days' | '30days' | '90days' | '6months' | '1year' | 'custom';

interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  totalProfit: number;
  profitMargin: number;
  
  averageOrderValue: number;
}



const FinancialScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<FinancialScreenNavigationProp>();
  const { showSuccess, showError } = useToast();
  const { state } = useAuth();
  const permissions = usePermissions(state.user);
  const { hasRole } = permissions;
  // const selectedOutlet = null; // TODO: Fix auth context
  const { state: dataState } = useData();
  
  // Check admin permission
  if (!hasRole('admin')) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header
          title='Financial Dashboard'
          onBackPress={() => navigation.goBack()}
          showBack={true}
        />
        <View style={styles.unauthorizedContainer}>
          <PhosphorIcon name="lock" size={48} color={theme.colors.onSurfaceVariant} />
          <Text style={[styles.unauthorizedTitle, { color: theme.colors.onSurface }]}>Access Restricted</Text>
          <Text style={[styles.unauthorizedMessage, { color: theme.colors.onSurfaceVariant }]}>Financial data is only accessible to administrators.</Text>
        </View>
      </View>
    );
  }
  
  // Performance monitoring for financial operations
  const { startTracking, endTracking } = usePerformanceTracking('financial_operations');
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<ReportPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  
  
  // Bottom sheet refs
  const timePeriodBottomSheetRef = useRef<TimePeriodBottomSheetRef>(null);
  const cashReconciliationBottomSheetRef = useRef<CashReconciliationBottomSheetRef>(null);
  
  // ActionSheet states
  const [showExportActionSheet, setShowExportActionSheet] = useState(false);
  const [showExpenseManagerActionSheet, setShowExpenseManagerActionSheet] = useState(false);

  // Confirmation ActionSheet logic retained for future use
  // const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  // const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  // const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  // const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');
  // const showConfirmation = useCallback(
  //   (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
  //     setConfirmationActionSheetTitle(title);
  //     setConfirmationActionSheetDescription(message);
  //     setConfirmationActionSheetOptions([
  //       {
  //         text: options?.confirmText || 'Confirm',
  //         onPress: () => {
  //           onConfirm();
  //           setConfirmationActionSheetVisible(false);
  //         },
  //         style: options?.type === 'danger' ? 'destructive' : 'primary',
  //         isAction: true,
  //       },
  //       {
  //         text: options?.cancelText || 'Cancel',
  //         onPress: () => setConfirmationActionSheetVisible(false),
  //         style: 'cancel',
  //         isAction: false,
  //       },
  //     ]);
  //     setConfirmationActionSheetVisible(true);
  //   },
  //   []
  // );
  
  const [metrics, setMetrics] = useState<FinancialMetrics>({
    totalRevenue: 0,
    totalExpenses: 0,
    totalProfit: 0,
    profitMargin: 0,
    averageOrderValue: 0,
  });
  

  const getPeriodLabel = (period: ReportPeriod): string => {
    switch (period) {
      case 'today': return 'Today';
      case '7days': return 'Last 7 Days';
      case '30days': return 'Last 30 Days';
      case '90days': return 'Last 90 Days';
      case '6months': return 'Last 6 Months';
      case '1year': return 'Last Year';
      case 'custom': return 'Custom Range';
      default: return 'Last 30 Days';
    }
  };

  // PRODUCTION FIX: Add component cleanup
  useEffect(() => {
    return () => {
      LoggingService.info('FinancialScreen: Component unmounting, cleaning up resources', 'CLEANUP');
      // Cleanup any pending timeouts or intervals
    };
  }, []);

  // PRODUCTION FIX: Load financial data asynchronously without blocking UI
  useEffect(() => {
    // Defer loading to next frame to prevent blocking UI
    const loadDataDeferred = () => {
      setTimeout(() => {
        loadFinancialData().catch(error => {
          LoggingService.error('Deferred financial data load failed', 'FINANCIAL_SCREEN', error);
        });
      }, 100); // Small delay to let UI render first
    };
    
    loadDataDeferred();
  }, [selectedPeriod, dataState.orders]);

  // PRODUCTION FIX: Enhanced data loading with timeout protection
  const loadFinancialData = useCallback(async () => {
    const trackingId = startTracking('financial_data_load');
    try {
      setLoading(true);
      
      // PRODUCTION FIX: Add timeout protection to prevent hanging
      const loadTimeout = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Financial data load timeout')), 15000); // 15 second timeout
      });

      const loadPromise = loadFinancialDataInternal();
      
      await Promise.race([loadPromise, loadTimeout]);
      
      endTracking(trackingId, 'compute', {
        period: selectedPeriod,
        revenue: metrics.totalRevenue,
        expenses: metrics.totalExpenses,
        profit: metrics.totalProfit
      });
    } catch (error) {
      // PRODUCTION FIX: Handle timeout errors gracefully
      if ((error as Error).message.includes('timeout')) {
        LoggingService.warn('Financial data load timeout - using cached data', 'FINANCIAL_SCREEN');
        showError('Data loading timeout - some information may be outdated');
      } else {
        LoggingService.error('Financial data load failed', 'FINANCIAL_SCREEN', error as Error);
        showError('Failed to load financial data');
      }
      
      endTracking(trackingId, 'compute', { error: true, period: selectedPeriod });
    } finally {
      setLoading(false);
    }
  }, [selectedPeriod, dataState.orders, startTracking, endTracking, showError]);

  // PRODUCTION FIX: Internal financial data loading logic
  const loadFinancialDataInternal = async () => {
    const { startDate, endDate } = getDateRange(selectedPeriod);

    // Load consolidated financial report with timeout protection
    const report = await Promise.race([
      FinancialService.generateProfitLossStatement(startDate, endDate),
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Financial service timeout')), 10000);
      })
    ]);

    // Update metrics
    setMetrics({
      totalRevenue: report.revenue,
      totalExpenses: report.expenses,
      totalProfit: report.netProfit,
      profitMargin: report.profitMargin,
      averageOrderValue: 0, // This needs to be calculated separately
    });
  };

  // PRODUCTION FIX: Enhanced refresh with timeout protection
  const onRefresh = async () => {
    const trackingId = startTracking('financial_refresh');
    setRefreshing(true);
    try {
      // Add timeout protection for refresh operations
      const refreshTimeout = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Refresh timeout')), 12000); // 12 second timeout
      });

      const refreshPromise = loadFinancialData();
      
      await Promise.race([refreshPromise, refreshTimeout]);
      
      showSuccess('Financial data refreshed successfully');
    } catch (error) {
      if ((error as Error).message.includes('timeout')) {
        showError('Refresh timeout - please try again');
      } else {
        showError('Failed to refresh financial data');
      }
    } finally {
      setRefreshing(false);
      endTracking(trackingId, 'network', { operation: 'refresh' });
    }
  };

  const handleRefresh = () => {
    onRefresh();
  };

  const handleExport = () => {
    setShowExportActionSheet(true);
  };

  const handlePeriodSelect = () => {
    timePeriodBottomSheetRef.current?.open();
  };

  const handlePeriodApply = (data: { period: ReportPeriod; startDate: Date; endDate: Date }) => {
    setSelectedPeriod(data.period);
    if (data.period === 'custom') {
      setCustomStartDate(data.startDate);
      setCustomEndDate(data.endDate);
    }
  };

  

  const handleCashReconciliation = () => {
    console.log('handleCashReconciliation called');
    console.log('cashReconciliationBottomSheetRef.current:', cashReconciliationBottomSheetRef.current);
    cashReconciliationBottomSheetRef.current?.present();
  };

  // PRODUCTION FIX: Enhanced period date range calculation with error handling
  const getDateRange = (period: ReportPeriod): { startDate: string; endDate: string } => {
    try {
      const now = new Date();
      const endDate = now.toISOString();
      
      if (period === 'custom' && customStartDate && customEndDate) {
        return {
          startDate: customStartDate.toISOString(),
          endDate: customEndDate.toISOString(),
        };
      }
      
      let startDate: Date;
      switch (period) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case '7days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case '6months':
          startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
          break;
        case '1year':
          startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }
      
      return {
        startDate: startDate.toISOString(),
        endDate,
      };
    } catch (error) {
      LoggingService.error('Date range calculation failed', 'FINANCIAL_SCREEN', error as Error);
      // Fallback to 30 days
      const now = new Date();
      return {
        startDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: now.toISOString(),
      };
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('BDT', '৳');
  };

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  const getProfitColor = (profit: number): string => {
    return profit >= 0 ? '#4CAF50' : theme.colors.error;
  };

  

  

  const statCards = [
    {
      title: 'Total Revenue',
      value: formatCurrency(metrics.totalRevenue),
      subtitle: `${selectedPeriod} period`,
      icon: '💰',
      color: theme.colors.primary,
    },
    {
      title: 'Total Expenses',
      value: formatCurrency(metrics.totalExpenses),
      subtitle: `${selectedPeriod} period`,
      icon: '💸',
      color: '#FF9800',
    },
    {
      title: 'Net Profit',
      value: formatCurrency(metrics.totalProfit),
      subtitle: `${formatPercentage(metrics.profitMargin)} margin`,
      icon: '📈',
      color: getProfitColor(metrics.totalProfit),
    },
    {
      title: 'Avg Order Value',
      value: formatCurrency(metrics.averageOrderValue),
      subtitle: `Avg per order`,
      icon: '🛍️',
      color: theme.colors.primary,
    },
  ];

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header
          title='Financial Dashboard'
          onBackPress={() => navigation.goBack()}
          showBack={true}
          actions={[
            {
              icon: 'refresh',
              onPress: handleRefresh,
            },
          ]}
        />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading financial data...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Financial Dashboard'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          {
            icon: 'refresh',
            onPress: handleRefresh,
          },
          {
            icon: 'download',
            onPress: handleExport,
          },
        ]}
      />

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Period Selector */}
        <TouchableOpacity
          style={[styles.periodSelector, { backgroundColor: theme.colors.surface }]}
          onPress={handlePeriodSelect}
        >
          <View style={styles.periodSelectorContent}>
            <Text style={[styles.periodLabel, { color: theme.colors.onSurfaceVariant }]}>
              Time Period
            </Text>
            <Text style={[styles.periodValue, { color: theme.colors.onSurface }]}>
              {getPeriodLabel(selectedPeriod)}
            </Text>
          </View>
          <View style={[styles.periodIcon, { backgroundColor: theme.colors.primaryContainer }]}>
            <Text style={[styles.periodIconText, { color: theme.colors.primary }]}>📅</Text>
          </View>
        </TouchableOpacity>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          {statCards.map((card, index) => (
            <View key={index} style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>{card.value}</Text>
              <Text style={[styles.statTitle, { color: theme.colors.onSurfaceVariant }]}>
                {card.title}
              </Text>
              <Text style={[styles.statSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                {card.subtitle}
              </Text>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Quick Actions
          </Text>
          <View style={styles.quickActionsGrid}>
            {[
              {
                label: 'Cash Reconciliation',
                icon: 'calculator',
                color: '#FF9800',
                onPress: handleCashReconciliation,
              },
              {
                label: 'P&L Report',
                icon: 'chart-line',
                color: '#4CAF50',
                onPress: () => navigation.navigate('ProfitLoss'),
              },
              {
                label: 'Tax Summary',
                icon: 'receipt',
                color: theme.colors.secondary,
                onPress: () => navigation.navigate('TaxSummary'),
              },
              {
                label: 'Transaction History',
                icon: 'clock',
                color: theme.colors.tertiary,
                onPress: () => navigation.navigate('TransactionHistory'),
              },
            ].map(({ label, icon, color, onPress }) => (
              <TouchableOpacity key={label} style={styles.quickActionItem} onPress={onPress}>
                <View style={[styles.iconContainer, { backgroundColor: color }]}>
                  <PhosphorIcon name={icon as any} size={20} color={theme.colors.onPrimary} />
                </View>
                <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                  {label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        
      </ScrollView>

      {/* ActionSheets */}
      <ActionSheet
        visible={showExportActionSheet}
        onDismiss={() => setShowExportActionSheet(false)}
        title='Export Financial Data'
        description='Choose export format for your financial data'
        options={[
          {
            text: 'Export as PDF',
            onPress: () => {
              setShowExportActionSheet(false);
              showSuccess('PDF export feature coming soon');
            },
            icon: 'file-pdf',
            style: 'primary',
            isAction: true,
          },
          {
            text: 'Export as Excel',
            onPress: () => {
              setShowExportActionSheet(false);
              showSuccess('Excel export feature coming soon');
            },
            icon: 'microsoft-excel-logo',
            style: 'default',
            isAction: true,
          },
          {
            text: 'Cancel',
            onPress: () => setShowExportActionSheet(false),
            icon: 'x',
            style: 'cancel',
            isAction: false,
          },
        ]}
      />

      <TimePeriodBottomSheet
        ref={timePeriodBottomSheetRef}
        selectedPeriod={selectedPeriod}
        onApply={handlePeriodApply}
        mode="range"
      />

      <CashReconciliationBottomSheet
        ref={cashReconciliationBottomSheetRef}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  unauthorizedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  unauthorizedTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  unauthorizedMessage: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  periodSelector: {
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
  },
  periodSelectorContent: {
    flexDirection: 'column',
  },
  periodLabel: {
    fontSize: 12,
  },
  periodValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  periodIcon: {
    padding: 8,
    borderRadius: 8,
  },
  periodIconText: {
    fontSize: 24,
  },
  period: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: 12,
  },
  section: {
    marginBottom: 24,
  },
  sectionContainer: {
    marginVertical: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: SPACING.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.sm,
    flexWrap: 'wrap',
  },
  quickActionItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderRadius: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  action: {
    flex: 1,
    minWidth: '45%',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  filter: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  outletCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  outletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  outletName: {
    fontSize: 16,
    fontWeight: '600',
  },
  outlet: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  outletText: {
    fontSize: 12,
    fontWeight: '500',
  },
  outletMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default withPerformanceTracking(FinancialScreen, 'FinancialScreen');