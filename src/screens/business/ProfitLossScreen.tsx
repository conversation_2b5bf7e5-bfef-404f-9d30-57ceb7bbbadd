import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View, FlatList } from 'react-native';
import { ActivityIndicator, Card, Chip, DataTable, Divider, Text } from 'react-native-paper';

import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import { useTheme } from '@/context/ThemeContext';
import { FinancialService } from '@/services/financialService';
import { RootStackNavigationProp } from '@/types/navigation';
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';

interface ProfitLossData {
  revenue: number;
  expenses: number;
  grossProfit: number;
  netProfit: number;
  profitMargin: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

// Moved outside the component as it's a pure function
const getStartDate = (period: string): string => {
  const now = new Date();
  switch (period) {
    case 'week':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    case 'month':
      return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    case 'quarter': {
      const quarter = Math.floor(now.getMonth() / 3);
      return new Date(now.getFullYear(), quarter * 3, 1).toISOString();
    }
    case 'year':
      return new Date(now.getFullYear(), 0, 1).toISOString();
    default:
      return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
  }
};

const periods = [
  { id: 'week', label: 'This Week' },
  { id: 'month', label: 'This Month' },
  { id: 'quarter', label: 'This Quarter' },
  { id: 'year', label: 'This Year' },
];

const ProfitLossScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<RootStackNavigationProp>();
  
  const [initialLoading, setInitialLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [profitLossData, setProfitLossData] = useState<ProfitLossData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Consolidated ActionSheet state
  type VisibleSheet = 'error' | 'success' | 'share' | null;
  const [visibleSheet, setVisibleSheet] = useState<VisibleSheet>(null);
  const [message, setMessage] = useState('');

  const getProfitColor = (profit: number): string => {
    return profit >= 0 ? theme.colors.success : theme.colors.error;
  };

  const loadProfitLossData = useCallback(async () => {
    if (!initialLoading) {
        setLoading(true);
    }
    try {
      const startDate = getStartDate(selectedPeriod);
      const endDate = new Date().toISOString();
      const data = await FinancialService.generateProfitLossStatement(startDate, endDate);
      setProfitLossData(data);
    } catch (error) {
      setMessage('Failed to load profit & loss data.');
      setVisibleSheet('error');
    } finally {
      setLoading(false);
      setInitialLoading(false);
    }
  }, [selectedPeriod, initialLoading]);

  useEffect(() => {
    loadProfitLossData();
  }, [loadProfitLossData]);

  const onRefresh = useCallback(async () => {
    await loadProfitLossData();
  }, [loadProfitLossData]);

  const exportProfitLossReport = () => {
    // ... export logic
    setMessage('Profit & Loss report exported successfully!');
    setVisibleSheet('success');
  };
  
  const shareReport = () => {
      setMessage('Share this profit & loss report with stakeholders?');
      setVisibleSheet('share');
  };

  if (initialLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size='large' color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>
          Loading Profit & Loss Data...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title='Profit & Loss'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[
          { icon: 'share', onPress: shareReport },
          { icon: 'download', onPress: exportProfitLossReport },
        ]}
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor={theme.colors.primary} />}
      >
        <View style={styles.periodSelector}>
          <FlatList
            horizontal
            data={periods}
            keyExtractor={(item) => item.id}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.period,
                  { backgroundColor: selectedPeriod === item.id ? theme.colors.primary : theme.colors.surfaceVariant },
                ]}
                onPress={() => setSelectedPeriod(item.id)}
              >
                <Text style={[styles.periodText, { color: selectedPeriod === item.id ? theme.colors.onPrimary : theme.colors.onSurfaceVariant }]}>
                  {item.label}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>

        {profitLossData && (
          <>
            <View style={styles.summaryCards}>
              <Card style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
                <Card.Content>
                  <View style={styles.cardHeader}>
                    <PhosphorIcon name='trend-up' size={24} color={theme.colors.primary} />
                    <Text variant='titleMedium' style={{ color: theme.colors.onSurface, marginLeft: 8 }}>Revenue</Text>
                  </View>
                  <Text variant='headlineMedium' style={{ color: theme.colors.primary, fontWeight: '700' }}>
                    {formatCurrency(profitLossData.revenue)}
                  </Text>
                </Card.Content>
              </Card>

              <Card style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
                <Card.Content>
                  <View style={styles.cardHeader}>
                    <PhosphorIcon name='trend-down' size={24} color={theme.colors.error} />
                    <Text variant='titleMedium' style={{ color: theme.colors.onSurface, marginLeft: 8 }}>Expenses</Text>
                  </View>
                  <Text variant='headlineMedium' style={{ color: theme.colors.error, fontWeight: '700' }}>
                    {formatCurrency(profitLossData.expenses)}
                  </Text>
                </Card.Content>
              </Card>

              <Card style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
                <Card.Content>
                  <View style={styles.cardHeader}>
                    <PhosphorIcon name='chart-line' size={24} color={getProfitColor(profitLossData.netProfit)} />
                    <Text variant='titleMedium' style={{ color: theme.colors.onSurface, marginLeft: 8 }}>Net Profit</Text>
                  </View>
                  <Text variant='headlineMedium' style={{ color: getProfitColor(profitLossData.netProfit), fontWeight: '700' }}>
                    {formatCurrency(profitLossData.netProfit)}
                  </Text>
                  <Text variant='bodyMedium' style={{ color: theme.colors.onSurfaceVariant }}>
                    {`${profitLossData.profitMargin.toFixed(1)}% margin`}
                  </Text>
                </Card.Content>
              </Card>
            </View>

            <Card style={[styles.detailCard, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
                <Text variant='titleLarge' style={{ color: theme.colors.onSurface, marginBottom: 16 }}>Detailed Breakdown</Text>
                
                {/* FIX: Restored the DataTable content below */}
                <DataTable>
                  <DataTable.Header>
                    <DataTable.Title>Item</DataTable.Title>
                    <DataTable.Title numeric>Amount</DataTable.Title>
                  </DataTable.Header>

                  <DataTable.Row>
                    <DataTable.Cell>Total Revenue</DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>
                        {formatCurrency(profitLossData.revenue)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row>
                    <DataTable.Cell>Total Expenses</DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text style={{ color: theme.colors.error, fontWeight: '600' }}>
                        {formatCurrency(profitLossData.expenses)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <Divider style={{ marginVertical: 8 }} />

                  <DataTable.Row>
                    <DataTable.Cell>Gross Profit</DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text style={{ color: getProfitColor(profitLossData.grossProfit), fontWeight: '600' }}>
                        {formatCurrency(profitLossData.grossProfit)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row>
                    <DataTable.Cell>Net Profit</DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text style={{ color: getProfitColor(profitLossData.netProfit), fontWeight: '700' }}>
                        {formatCurrency(profitLossData.netProfit)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                </DataTable>
              </Card.Content>
            </Card>
          </>
        )}
      </ScrollView>

      <ActionSheet
        visible={visibleSheet === 'error' || visibleSheet === 'success'}
        onDismiss={() => setVisibleSheet(null)}
        title={visibleSheet === 'error' ? 'Error' : 'Success'}
        description={message}
        options={[{ text: 'OK', onPress: () => setVisibleSheet(null), isAction: true }]}
      />

      <ActionSheet
        visible={visibleSheet === 'share'}
        onDismiss={() => setVisibleSheet(null)}
        title='Share Report'
        description={message}
        options={[
          { text: 'Share', onPress: () => { /* share logic */ setVisibleSheet(null); }, isAction: true },
          { text: 'Cancel', style: 'cancel', onPress: () => setVisibleSheet(null) },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  periodSelector: {
    marginVertical: 16,
  },
  period: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryCards: {
    marginBottom: 12,
  },
  summaryCard: {
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
});

export default ProfitLossScreen;