import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator, Card, Chip, DataTable, Divider, Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import Button from '../../components/ui/Button';
import { FINANCIAL_CONFIG } from '../../config/constants';
import { useTheme } from '../../context/ThemeContext';
import { FinancialService } from '../../services/financialService';
import LoggingService from '../../services/LoggingService';
import { ActionSheetOption } from '../../types';
import { RootStackNavigationProp } from '../../types/navigation';
import { formatCurrency } from '../../utils/currency';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPE DEFINITIONS ---
interface TaxSummaryData {
  period: { startDate: string; endDate: string };
  totalTaxLiability: number;
  salesTax: { taxableAmount: number; rate: number; amount: number };
  incomeTax: { taxableAmount: number; rate: number; amount: number };
}

interface Period {
  id: 'week' | 'month' | 'quarter' | 'year';
  label: string;
}

interface TaxStatus {
  status: string;
  color: string;
  icon: PhosphorIconName;
}

interface PeriodChipProps {
  period: Period;
  isSelected: boolean;
  onPress: (id: Period['id']) => void;
}

// --- CONSTANTS ---
const periods: Period[] = [
  { id: 'week', label: 'This Week' },
  { id: 'month', label: 'This Month' },
  { id: 'quarter', label: 'This Quarter' },
  { id: 'year', label: 'This Year' },
];

// --- MEMOIZED COMPONENTS ---
const PeriodChip: React.FC<PeriodChipProps> = React.memo(({ period, isSelected, onPress }) => {
  const theme = useTheme();
  return (
    <TouchableOpacity
      style={[
        styles.periodChip,
        { backgroundColor: isSelected ? theme.colors.primary : theme.colors.surfaceVariant },
      ]}
      onPress={() => onPress(period.id)}
    >
      <Text
        style={[
          styles.periodText,
          { color: isSelected ? theme.colors.onPrimary : theme.colors.onSurfaceVariant },
        ]}
      >
        {period.label}
      </Text>
    </TouchableOpacity>
  );
});

// --- MAIN COMPONENT ---
const TaxSummaryScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<RootStackNavigationProp>();

  // --- STATE MANAGEMENT ---
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [taxData, setTaxData] = useState<TaxSummaryData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<Period['id']>('month');

  // ActionSheet State
  const [actionSheet, setActionSheet] = useState({
    visible: false,
    title: '',
    description: '',
    options: [] as ActionSheetOption[],
  });

  // --- DATA FETCHING & SIDE EFFECTS ---
  const getStartDate = (period: Period['id']): string => {
    const now = new Date();
    switch (period) {
      case 'week':
        return new Date(now.setDate(now.getDate() - 7)).toISOString();
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      case 'quarter': {
        const quarter = Math.floor(now.getMonth() / 3);
        return new Date(now.getFullYear(), quarter * 3, 1).toISOString();
      }
      case 'year':
        return new Date(now.getFullYear(), 0, 1).toISOString();
    }
  };

  const loadTaxData = useCallback(async () => {
    try {
      if (!refreshing) {setLoading(true);}
      const startDate = getStartDate(selectedPeriod);
      const endDate = new Date().toISOString();

      const taxSummary = await FinancialService.getTaxSummary(startDate, endDate);
      
      // NOTE: This is placeholder logic for income tax. Replace with actual calculations.
      const incomeTaxAmount = taxSummary.totalTaxCollected * 0.3;
      const totalLiability = taxSummary.totalTaxCollected + incomeTaxAmount;

      const data: TaxSummaryData = {
        period: { startDate, endDate },
        totalTaxLiability: totalLiability,
        salesTax: {
          taxableAmount: taxSummary.totalTaxableAmount,
          rate: taxSummary.taxRate,
          amount: taxSummary.totalTaxCollected,
        },
        incomeTax: {
          taxableAmount: taxSummary.totalTaxableAmount * 0.8, // Placeholder
          rate: 0.25, // Placeholder
          amount: incomeTaxAmount, // Placeholder
        },
      };

      setTaxData(data);
    } catch (error) {
      LoggingService.error('Failed to load tax data', 'TAX_SUMMARY', error as Error);
      setActionSheet({
        visible: true,
        title: 'Error',
        description: 'Failed to load tax data. Please try again.',
        options: [{ text: 'OK', onPress: () => setActionSheet({ ...actionSheet, visible: false }) }],
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedPeriod, refreshing]);

  useEffect(() => {
    loadTaxData();
  }, [loadTaxData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // The useEffect listening to `refreshing` will trigger `loadTaxData`
  }, []);

  // --- MEMOIZED VALUES ---
  const taxStatus: TaxStatus | null = useMemo(() => {
    if (!taxData) {return null;}
    const amount = taxData.totalTaxLiability;
    if (amount <= 0) {
      return { status: 'No Tax Due', color: theme.colors.tertiary, icon: 'check-circle' };
    }
    if (amount < 1000) {
      return { status: 'Low Tax Liability', color: theme.colors.warning, icon: 'warning-circle' };
    }
    return { status: 'High Tax Liability', color: theme.colors.error, icon: 'warning' };
  }, [taxData, theme.colors]);

  // --- UTILITY & HELPER FUNCTIONS ---
  

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const handleExport = () => {
    setActionSheet({
      visible: true,
      title: 'Success',
      description: 'Tax report exported successfully!',
      options: [{ text: 'OK', onPress: () => setActionSheet({ ...actionSheet, visible: false }) }],
    });
  };

  const handleSetReminder = () => {
    setActionSheet({
      visible: true,
      title: 'Schedule Tax Reminder',
      description: 'Set a reminder for the tax filing deadline?',
      options: [
        {
          text: 'Schedule',
          onPress: () => {
            setActionSheet({
              visible: true,
              title: 'Success',
              description: 'Tax reminder scheduled successfully!',
              options: [{ text: 'OK', onPress: () => setActionSheet({ ...actionSheet, visible: false }) }],
            });
          },
          isAction: true,
        },
        { text: 'Cancel', style: 'cancel', onPress: () => setActionSheet({ ...actionSheet, visible: false }) },
      ],
    });
  };

  // --- RENDER LOGIC ---
  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>
          Loading Tax Summary...
        </Text>
      </View>
    );
  }
  
  const renderTaxDetailsCard = (
    title: string,
    icon: PhosphorIconName,
    iconColor: string,
    taxableAmount: number,
    rate: number,
    taxAmount: number
  ) => (
    <Card style={styles.detailCard}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <PhosphorIcon name={icon} size={24} color={iconColor} />
          <Text variant="titleMedium" style={styles.cardTitle}>
            {title}
          </Text>
        </View>
        <DataTable>
          <DataTable.Row>
            <DataTable.Cell>Taxable Amount</DataTable.Cell>
            <DataTable.Cell numeric>{formatCurrency(taxableAmount)}</DataTable.Cell>
          </DataTable.Row>
          <DataTable.Row>
            <DataTable.Cell>Tax Rate</DataTable.Cell>
            <DataTable.Cell numeric>{formatPercentage(rate)}</DataTable.Cell>
          </DataTable.Row>
          <DataTable.Row>
            <DataTable.Cell>Tax Amount</DataTable.Cell>
            <DataTable.Cell numeric>
              <Text style={{ color: iconColor, fontWeight: '600' }}>
                {formatCurrency(taxAmount)}
              </Text>
            </DataTable.Cell>
          </DataTable.Row>
        </DataTable>
      </Card.Content>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title="Tax Summary"
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ icon: 'download', onPress: handleExport }]}
      />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={styles.periodSelector}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {periods.map(period => (
              <PeriodChip
                key={period.id}
                period={period}
                isSelected={selectedPeriod === period.id}
                onPress={setSelectedPeriod}
              />
            ))}
          </ScrollView>
        </View>

        {taxData && taxStatus && (
          <>
            <Card style={styles.statusCard}>
              <Card.Content>
                <View style={styles.statusHeader}>
                  <PhosphorIcon name={taxStatus.icon} size={32} color={taxStatus.color} />
                  <View style={styles.statusInfo}>
                    <Text variant="titleLarge" style={styles.statusAmount}>
                      {formatCurrency(taxData.totalTaxLiability)}
                    </Text>
                    <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                      Total Tax Liability
                    </Text>
                  </View>
                </View>
                <Chip
                  icon={taxStatus.icon}
                  style={{
                    backgroundColor: `${taxStatus.color}20`,
                    alignSelf: 'flex-start',
                    marginTop: 12,
                  }}
                  textStyle={{ color: taxStatus.color }}
                >
                  {taxStatus.status}
                </Chip>
              </Card.Content>
            </Card>

            {renderTaxDetailsCard(
              'Sales Tax', 'shopping-cart', theme.colors.primary,
              taxData.salesTax.taxableAmount, taxData.salesTax.rate, taxData.salesTax.amount
            )}

            {renderTaxDetailsCard(
              'Income Tax (Est.)', 'chart-line', theme.colors.tertiary,
              taxData.incomeTax.taxableAmount, taxData.incomeTax.rate, taxData.incomeTax.amount
            )}

            <Card style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}>
              <Card.Content>
                <Text variant="titleMedium" style={[styles.summaryTitle, { color: theme.colors.onPrimaryContainer }]}>
                  Total Summary
                </Text>
                <View style={styles.summaryRow}>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>Sales Tax</Text>
                  <Text style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}>
                    {formatCurrency(taxData.salesTax.amount)}
                  </Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>Income Tax (Est.)</Text>
                  <Text style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}>
                    {formatCurrency(taxData.incomeTax.amount)}
                  </Text>
                </View>
                <Divider style={[ styles.divider, { backgroundColor: `${theme.colors.onPrimaryContainer}30` }]}/>
                <View style={styles.summaryRow}>
                  <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                    Total Liability
                  </Text>
                  <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                    {formatCurrency(taxData.totalTaxLiability)}
                  </Text>
                </View>
              </Card.Content>
            </Card>
          </>
        )}
        <View style={{ height: insets.bottom + 80 }} />
      </ScrollView>

      <View style={[ styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outlineVariant }]}>
        <Button
          variant="outline"
          onPress={handleSetReminder}
          icon="bell"
          style={styles.actionButton}
        >
          Set Reminders
        </Button>
        <Button
          variant="primary"
          onPress={handleExport}
          icon="download"
          style={styles.actionButton}
        >
          Export Report
        </Button>
      </View>

      <ActionSheet
        visible={actionSheet.visible}
        onDismiss={() => setActionSheet({ ...actionSheet, visible: false })}
        title={actionSheet.title}
        description={actionSheet.description}
        options={actionSheet.options}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  periodSelector: {
    marginVertical: 16,
  },
  periodChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    marginLeft: 16,
    flex: 1,
  },
  statusAmount: {
    fontWeight: '700',
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    marginLeft: 8,
  },
  summaryCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  summaryTitle: {
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  divider: {
    marginVertical: 8,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Or a theme color for light/dark mode
  },
  actionButton: {
    flex: 1,
  },
});

export default TaxSummaryScreen;