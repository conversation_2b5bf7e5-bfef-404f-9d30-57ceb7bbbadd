import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';

import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import UIOrganicCard from '@/components/ui/Card';
import EmptyState from '@/components/ui/EmptyState';
import { useTheme } from '@/context/ThemeContext';
import { ServiceTypeService, Service } from '@/services/ServiceService';
import { ActionSheetOption } from '@/types';
import { formatCurrency } from '@/utils/currency';

const ServiceScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [items, setItems] = useState<Service[]>([]);

  const [confirmationActionSheetVisible, setConfirmationActionSheetVisible] = useState<boolean>(false);
  const [confirmationActionSheetOptions, setConfirmationActionSheetOptions] = useState<ActionSheetOption[]>([]);
  const [confirmationActionSheetTitle, setConfirmationActionSheetTitle] = useState<string>('');
  const [confirmationActionSheetDescription, setConfirmationActionSheetDescription] = useState<string>('');

  const showConfirmation = useCallback(
    (title: string, message: string, onConfirm: () => void, options?: { confirmText?: string; cancelText?: string; type?: 'danger' | 'warning' | 'info' }) => {
      setConfirmationActionSheetTitle(title);
      setConfirmationActionSheetDescription(message);
      setConfirmationActionSheetOptions([
        {
          text: options?.confirmText || 'Confirm',
          onPress: () => {
            onConfirm();
            setConfirmationActionSheetVisible(false);
          },
          style: options?.type === 'danger' ? 'destructive' : 'primary',
          isAction: true,
        },
        {
          text: options?.cancelText || 'Cancel',
          onPress: () => setConfirmationActionSheetVisible(false),
          style: 'cancel',
          isAction: false,
        },
      ]);
      setConfirmationActionSheetVisible(true);
    },
    []
  );

  const load = useCallback(async () => {
    await ServiceTypeService.seedIfEmpty();
    const list = await ServiceTypeService.list();
    setItems(list);
  }, []);

  useEffect(() => {
    void load();
  }, [load]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await load();
    } finally {
      setRefreshing(false);
    }
  }, [load]);

  const filtered = useMemo(() => {
    const q = searchQuery.trim().toLowerCase();
    if (!q) {return items;}
    return items.filter(
      i =>
        i.name.toLowerCase().includes(q) ||
        (Array.isArray(i.measurementFields) &&
          i.measurementFields.join(' ').toLowerCase().includes(q))
    );
  }, [searchQuery, items]);

  const handleAdd = useCallback(() => {
    navigation.navigate('AddService');
  }, [navigation]);

  const handlePress = useCallback((item: Service) => {
    navigation.navigate('AddService', { id: item.id });
  }, [navigation]);

  const renderItem = useCallback(
    ({ item }: { item: Service }) => {
      const rightPrice = formatCurrency(item.price, { decimals: 0 });
      return (
        <UIOrganicCard
          title={item.name}
          subtitle={`Measurements: ${item.measurementFields?.length || 0}`}
          description={undefined}
          price={undefined}
          image={undefined}
          icon={undefined}
          iconColor={undefined}
          iconBackgroundColor={undefined}
          status={rightPrice}
          statusColor={theme.colors.onSurface}
          statusBackgroundColor='transparent'
          badge={undefined}
          badgeColor={undefined}
          onPress={() => handlePress(item)}
          onLongPress={undefined}
          actions={[]}
          primaryAction={undefined}
          secondaryAction={undefined}
          menuItems={[]}
          menuVisible={false}
          onMenuToggle={undefined}
          onMenuDismiss={undefined}
          style={StyleSheet.flatten([styles.card, { borderWidth: 0, borderColor: 'transparent' }])}
          contentStyle={undefined}
          disabled={false}
          showImage={false}
          showIcon={false}
          showActions={true}
          layout='compact'
        />
      );
    },
    [handlePress, theme.colors.onSurface]
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Service Types'
        onBackPress={() => navigation.goBack()}
        showBack={true}
        showSearch={true}
        searchPlaceholder='Search services...'
        // FIX: Changed `onSearchSubmit` to `onSearchChange` for live filtering
        onSearchChange={setSearchQuery}
        actions={[{ icon: 'plus', onPress: handleAdd }]}
      />

      <FlatList
        data={filtered}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        style={styles.list}
        contentContainerStyle={filtered.length === 0 ? styles.listEmpty : undefined}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={() => (
          <EmptyState
            type='custom'
            icon='tshirt'
            title='No Service Types Found'
            searchQuery={searchQuery}
            onActionPress={handleAdd}
            description='No service types found'
            actionLabel='Add Type'
            style={{}}
            iconColor={theme.colors.primary}
          />
        )}
      />

      <ActionSheet
        visible={confirmationActionSheetVisible}
        onDismiss={() => setConfirmationActionSheetVisible(false)}
        title={confirmationActionSheetTitle}
        description={confirmationActionSheetDescription}
        options={confirmationActionSheetOptions}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  list: { flex: 1, paddingHorizontal: 16, paddingTop: 8 },
  listEmpty: { flexGrow: 1, justifyContent: 'center' },
  card: { marginBottom: 12 },
});

export default ServiceScreen;