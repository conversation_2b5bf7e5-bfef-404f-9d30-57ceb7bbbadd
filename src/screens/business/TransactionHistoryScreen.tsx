import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { FlatList, View, StyleSheet, RefreshControl } from 'react-native';
import { Text, Chip, FAB, ActivityIndicator } from 'react-native-paper';

import { TransactionHistoryList } from '../../components/inventory';
import Header from '../../components/navigation/Header';
import ActionSheet from '../../components/ui/ActionSheet';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING } from '../../theme/theme';
import { ActionSheetOption } from '../../types';
import { InventoryTransaction } from '../../types/inventory';
import { PhosphorIcon, PhosphorIconName } from '../../utils/phosphorIconRegistry';

// --- TYPE DEFINITIONS ---
type ScreenRouteProp = RouteProp<{
  params?: {
    filter?: 'all' | 'in' | 'out' | 'transfer' | 'adjustment';
    itemId?: string;
    warehouseId?: string;
  };
}, 'params'>;

interface FilterOption {
  key: string;
  label: string;
  icon: PhosphorIconName;
}

// --- CONSTANTS ---
// FIX: Corrected the structure for 'transfer' and 'adjustment' to include the 'label' and 'icon' properties.
const filterOptions: FilterOption[] = [
  { key: 'all', label: 'All', icon: 'list' },
  { key: 'in', label: 'Stock In', icon: 'arrow-down' },
  { key: 'out', label: 'Stock Out', icon: 'arrow-up' },
  { key: 'transfer', label: 'Transfer', icon: 'arrows-left-right' },
  { key: 'adjustment', label: 'Adjustment', icon: 'wrench' },
];

// --- MEMOIZED COMPONENTS ---
const MemoizedTransactionItem = React.memo(
  ({
    transaction,
    onTransactionPress,
  }: {
    transaction: InventoryTransaction;
    onTransactionPress: (transaction: InventoryTransaction) => void;
  }) => (
    <TransactionHistoryList
      transactions={[transaction]}
      showItemName={true}
      showWarehouseName={true}
      showPerformedBy={true}
      onTransactionPress={onTransactionPress}
    />
  )
);

// --- MAIN SCREEN COMPONENT ---
const TransactionHistoryScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const route = useRoute<ScreenRouteProp>();
  const { state: dataState, actions: dataActions } = useData();

  // --- STATE MANAGEMENT ---
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const initialFilter = route.params?.filter || 'all';
  const [selectedFilter, setSelectedFilter] = useState<string>(initialFilter);
  
  const [actionSheet, setActionSheet] = useState({
      visible: false,
      title: '',
      description: '',
      options: [] as ActionSheetOption[],
  });

  // --- DATA FETCHING ---
  const loadTransactions = useCallback(async () => {
    if (!refreshing) {setIsLoading(true);}
    setError(null);
    try {
      const fetchedTransactions = await dataActions.getTransactionHistory();
      setTransactions(fetchedTransactions);
    } catch (err) {
      setError('Failed to load transaction history.');
      LoggingService.error('Failed to load transactions', 'TRANSACTION_HISTORY', err as Error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [dataActions, refreshing]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    // The useEffect will re-trigger loadTransactions when refreshing becomes true
  }, []);

  // --- MEMOIZED DERIVED DATA ---
  const filteredTransactions = useMemo(() => {
    let filtered = [...transactions];
    const { itemId, warehouseId } = route.params || {};

    if (selectedFilter !== 'all') {
      // Ensure case-insensitive comparison for robustness
      filtered = filtered.filter(t => t.type.toLowerCase() === selectedFilter.toLowerCase());
    }

    if (itemId) {
      filtered = filtered.filter(t => t.itemId === itemId);
    }
    if (warehouseId) {
      filtered = filtered.filter(t => t.warehouseId === warehouseId);
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [transactions, selectedFilter, route.params]);

  const screenTitle = useMemo(() => {
    const { itemId, warehouseId } = route.params || {};
    if (itemId) {
      const item = dataState.products.find(i => i.id === itemId);
      return item ? `${item.name} History` : 'Item History';
    }
    if (warehouseId) {
      const warehouse = dataState.warehouses?.find(w => w.id === warehouseId);
      return warehouse ? `${warehouse.name} History` : 'Warehouse History';
    }
    return 'Transaction History';
  }, [route.params, dataState.products, dataState.warehouses]);

  // --- CALLBACKS ---
  const handleTransactionPress = useCallback((transaction: InventoryTransaction) => {
    if (transaction.itemId) {
      navigation.navigate('InventoryItemDetail', { itemId: transaction.itemId });
    }
  }, [navigation]);

  const renderItem = useCallback(
    ({ item }: { item: InventoryTransaction }) => (
      <MemoizedTransactionItem transaction={item} onTransactionPress={handleTransactionPress} />
    ),
    [handleTransactionPress]
  );
  
  // --- UI RENDER FUNCTIONS ---
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      {error ? (
          <PhosphorIcon name="warning" size={64} color={theme.colors.error} />
      ) : (
          <PhosphorIcon name="clock" size={64} color={theme.colors.onSurfaceVariant} />
      )}
      <Text variant="titleMedium" style={[styles.emptyTitle, { color: theme.colors.onSurface }]}>
        {error || 'No Transactions Found'}
      </Text>
      <Text style={[styles.emptySubtitle, { color: theme.colors.onSurfaceVariant }]}>
        {error ? 'Please pull to refresh and try again.' : 'When you have transactions, they will appear here.'}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={screenTitle}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ icon: 'magnifying-glass', onPress: () => {
          // Open search interface for transactions
          navigation.navigate('Search', { initialQuery: '', searchType: 'transactions' } as never);
        } }]}
      />
      
      <View style={[styles.filterContainer, { borderBottomColor: theme.colors.outlineVariant }]}>
        <FlatList
          data={filterOptions}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          keyExtractor={item => item.key}
          renderItem={({ item }) => (
            <Chip
              selected={selectedFilter === item.key}
              onPress={() => setSelectedFilter(item.key)}
              style={styles.filterChip}
              icon={item.icon}
            >
              {item.label}
            </Chip>
          )}
        />
      </View>

      {isLoading && !refreshing ? (
          <View style={styles.emptyContainer}>
              <ActivityIndicator size="large" />
              <Text style={[styles.emptySubtitle, { color: theme.colors.onSurfaceVariant }]}>Loading transactions...</Text>
          </View>
      ) : (
          <FlatList
            data={filteredTransactions}
            keyExtractor={item => item.id}
            renderItem={renderItem}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]} tintColor={theme.colors.primary} />
            }
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={renderEmptyState}
          />
      )}

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('StockOperations')}
      />
      
      <ActionSheet
          visible={actionSheet.visible}
          onDismiss={() => setActionSheet(prev => ({ ...prev, visible: false }))}
          title={actionSheet.title}
          description={actionSheet.description}
          options={actionSheet.options}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filterContainer: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
  },
  filterList: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  filterChip: {},
  listContainer: {
    flexGrow: 1,
    padding: SPACING.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
    gap: SPACING.md,
  },
  emptyTitle: {
    fontWeight: '600',
  },
  emptySubtitle: {
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: SPACING.lg,
    right: 0,
    bottom: 0,
  },
});

export default TransactionHistoryScreen;