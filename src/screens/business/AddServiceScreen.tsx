import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView, StyleSheet, View, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';

import Header from '@/components/navigation/Header';
import ActionSheet from '@/components/ui/ActionSheet';
import Button from '@/components/ui/Button';
import Chip from '@/components/ui/Chip';
import TextInput from '@/components/ui/TextInput';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { ServiceTypeService } from '@/services/ServiceService';
import { RootStackParamList } from '@/types/navigation';

type AddServiceScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AddService'>;
type AddServiceScreenRouteProp = RouteProp<RootStackParamList, 'AddService'>;

interface Props {
  navigation: AddServiceScreenNavigationProp;
  route: AddServiceScreenRouteProp;
}

// --- Helper Functions ---
const parseNumber = (value: string): number => {
  const n = Number(value.replace(/[^0-9.]/g, ''));
  return Number.isFinite(n) ? n : 0;
};

const COMMON_MEASUREMENTS = [ 'chest', 'waist', 'hips', 'shoulder', 'sleeve', 'neck', 'inseam', 'length' ];

interface DialogState {
  visible: boolean;
  title: string;
  description: string;
  onConfirm: () => void;
}

const AddServiceScreen: React.FC<Props> = ({ navigation, route }) => {
  const theme = useTheme();
  const { showToast } = useToast();
  const id = route?.params?.id;
  const isEditing = Boolean(id);

  const [loading, setLoading] = useState(isEditing);
  // ✅ FIX: Removed extra '=' sign which was a syntax error.
  const [saving, setSaving] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [price, setPrice] = useState('');
  const [measurementFields, setMeasurementFields] = useState<string[]>([]);
  const [newMeasurement, setNewMeasurement] = useState('');

  const [dialogState, setDialogState] = useState<DialogState>({
    visible: false,
    title: '',
    description: '',
    onConfirm: () => {},
  });

  useEffect(() => {
    if (!isEditing || !id) {return;}
    
    (async () => {
      try {
        setLoading(true);
        const item = await ServiceTypeService.getById(id);
        if (item) {
          setName(item.name || '');
          setPrice(String(item.price ?? ''));
          setMeasurementFields(Array.isArray(item.measurementFields) ? item.measurementFields : []);
        }
      } catch (error) {
        LoggingService.error('Failed to fetch service type', 'SERVICE_TYPES', error as Error);
        // ✅ FIX: Changed showToast argument from an object to a string to match its type definition.
        showToast('Failed to load service data.');
      } finally {
        setLoading(false);
      }
    })();
  }, [id, isEditing, showToast]);

  const addMeasurement = useCallback(() => {
    const label = newMeasurement.trim().toLowerCase();
    if (!label) {return;}
    if (label.length > 30) {
      showToast('Measurement name is too long.');
      return;
    }
    if (measurementFields.map(m => m.toLowerCase()).includes(label)) {
      setNewMeasurement('');
      return;
    }
    setMeasurementFields(prev => [...prev, label]);
    setNewMeasurement('');
  }, [newMeasurement, measurementFields, showToast]);

  const removeMeasurement = useCallback((label: string) => {
    setMeasurementFields(prev => prev.filter(m => m.toLowerCase() !== label.toLowerCase()));
  }, []);

  const handleSave = useCallback(async () => {
    if (!name.trim()) {
      showToast('Service Type name is required.');
      return;
    }

    setSaving(true);
    try {
      const data = {
        name: name.trim(),
        price: parseNumber(price),
        measurementFields: measurementFields.map(s => s.trim()).filter(Boolean),
      };

      if (isEditing && id) {
                await ServiceTypeService.update(id, data);
      } else {
        await ServiceTypeService.create(data);
      }
      showToast(`Service Type ${isEditing ? 'updated' : 'created'} successfully!`);
      navigation.goBack();
    } catch (error) {
      LoggingService.error('Failed to save service type', 'SERVICE_TYPES', error as Error);
      showToast('Failed to save service type.');
    } finally {
      setSaving(false);
    }
  }, [name, price, measurementFields, isEditing, id, navigation, showToast]);

  const handleDelete = useCallback(() => {
    if (!id) {return;}
    setDialogState({
      visible: true,
      title: 'Delete Service Type?',
      description: `Are you sure you want to delete "${name}"? This action cannot be undone.`,
      onConfirm: async () => {
        try {
          await ServiceTypeService.remove(id);
          showToast(`"${name}" was deleted.`);
          navigation.goBack();
        } catch (error) {
          LoggingService.error('Failed to delete service type', 'SERVICE_TYPES', error as Error);
          showToast('Failed to delete service.');
        }
      },
    });
  }, [id, name, navigation, showToast]);

  if (loading) {
    return (
      <View style={[styles.container, styles.center, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title={isEditing ? 'Edit Service' : 'Add Service'}
        onBackPress={() => navigation.goBack()}
        showBack={true}
        actions={[{ text: 'Save', onPress: handleSave, color: theme.colors.primary, disabled: saving }]}
      />

      <ScrollView contentContainerStyle={styles.content} keyboardShouldPersistTaps='handled'>
        <TextInput label='Name *' value={name} onChangeText={setName} style={styles.input} />
        <TextInput label='Price' value={price} onChangeText={setPrice} keyboardType='numeric' style={styles.input} />

        <View>
          <Text style={[styles.sectionLabel, { color: theme.colors.onSurfaceVariant }]}>Measurements</Text>
          <View style={styles.row}>
            <View style={{ flex: 1 }}>
              <TextInput
                label='Add a measurement (e.g., chest)'
                value={newMeasurement}
                onChangeText={setNewMeasurement}
                onSubmitEditing={addMeasurement}
                returnKeyType='done'
                style={{ marginRight: 8 }}
              />
            </View>
            <Button variant='primary' size='md' onPress={addMeasurement} disabled={!newMeasurement.trim()}>Add</Button>
          </View>
          <View style={styles.chipsContainer}>
            {measurementFields.map(m => (
              <Chip key={m} label={m} rightIcon='x' variant='outlined' size='small' onPress={() => removeMeasurement(m)} style={styles.chip} />
            ))}
          </View>
          <Text style={[styles.sublabel, { color: theme.colors.onSurfaceVariant }]}>Quick add</Text>
          <View style={styles.chipsContainer}>
            {COMMON_MEASUREMENTS.map(s => {
              const exists = measurementFields.map(m => m.toLowerCase()).includes(s.toLowerCase());
              return (
                <Chip
                  key={s} label={s} variant={exists ? 'solid' : 'outlined'}
                  size='small' selected={exists}
                  onPress={() => !exists && setMeasurementFields(prev => [...prev, s])}
                  style={styles.chip}
                />
              );
            })}
          </View>
        </View>

        <View style={styles.buttonRow}>
          <Button variant='primary' size='md' onPress={handleSave} loading={saving} disabled={saving} style={{ flex: 1 }}>
            {isEditing ? 'Update' : 'Create'}
          </Button>
          {isEditing && (
            // ✅ FIX: The 'destructive' variant is not supported. Switched to 'ghost' and used the theme's error color for styling.
            <Button variant='ghost' size='md' onPress={handleDelete} disabled={saving} textColor={theme.colors.error}>
              Delete
            </Button>
          )}
        </View>
      </ScrollView>

      <ActionSheet
        visible={dialogState.visible}
        onDismiss={() => setDialogState(prev => ({ ...prev, visible: false }))}
        title={dialogState.title}
        description={dialogState.description}
        options={[
          { text: 'Confirm', onPress: dialogState.onConfirm, style: 'destructive', isAction: true },
          { text: 'Cancel', onPress: () => setDialogState(prev => ({ ...prev, visible: false })), style: 'cancel' },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  center: { justifyContent: 'center', alignItems: 'center' },
  content: { paddingHorizontal: 16, paddingVertical: 12, paddingBottom: 40 },
  input: { marginBottom: 12 },
  row: { flexDirection: 'row', alignItems: 'center', marginBottom: 8 },
  sectionLabel: { marginTop: 8, marginBottom: 12, fontSize: 16, fontWeight: '600' },
  sublabel: { marginTop: 12, marginBottom: 8, fontSize: 12 },
  chipsContainer: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },
  chip: { marginRight: 4, marginBottom: 4 },
  buttonRow: { flexDirection: 'row', gap: 12, marginTop: 24 },
});

export default AddServiceScreen;