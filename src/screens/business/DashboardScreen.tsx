import React, { useCallback, useMemo, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';

import Header from '@/components/navigation/Header';
import StatCardGroup from '@/components/ui/StatCardGroup';
import { useAuth } from '@/context/AuthContext';
import { useData } from '@/context/DataContext';
import { useFinancial } from '@/context/FinancialContext';
import { useTheme } from '@/context/ThemeContext';
import { useToast } from '@/context/ToastContext';
import LoggingService from '@/services/LoggingService';
import { useNotifications } from '@/services/notificationService';
import { withPerformanceTracking, usePerformanceTracking } from '@/services/PerformanceMonitoringService';
import { SPACING } from '@/theme/theme';
import { Order, StatCard } from '@/types'; // Assuming Order type is available
import { formatCurrency } from '@/utils/currency';
import { PhosphorIcon } from '@/utils/phosphorIconRegistry';


interface DashboardScreenProps {
  navigation: any;
  navigateToTab: (tabName: string) => void;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation, navigateToTab }) => {
  const theme = useTheme();
  const { state } = useData();
  const { state: financialState } = useFinancial();
  const { profitLossData } = financialState;
  const { unreadCount } = useNotifications();
  const { showSuccess } = useToast();
  const [refreshing, setRefreshing] = useState<boolean>(false);
  
  // Performance monitoring for dashboard operations
  const { startTracking, endTracking } = usePerformanceTracking('dashboard_operations');

  const handleRefresh = useCallback(async (): Promise<void> => {
    const trackingId = startTracking('dashboard_refresh');
    setRefreshing(true);
    try {
      // In a real app, you would refetch data here.
      // e.g., await actions.reloadData();
      await new Promise(resolve => setTimeout(resolve, 500));
      showSuccess('Dashboard refreshed');
    } catch (error) {
      LoggingService.warn('Failed to refresh dashboard', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
      endTracking(trackingId, 'network', { operation: 'refresh' });
    }
  }, [showSuccess, startTracking, endTracking]);

  // ✅ MAINTAINABILITY: Replaced the long switch statement with a cleaner navigation map.
  const navigationMap: Record<string, () => void> = useMemo(() => ({
    sales: () => navigation.navigate('Reports'),
    orders: () => navigateToTab('Orders'),
    products: () => navigation.navigate('InventoryItems'),
    customers: () => navigation.navigate('Customers'),
    financial: () => navigation.navigate('Financial'),
    reports: () => navigation.navigate('Reports'),
  }), [navigation, navigateToTab]);

  const handleStatCardPress = useCallback((type: string): void => {
    const trackingId = startTracking(`dashboard_navigation_${type}`);
    const navigateAction = navigationMap[type];
    if (navigateAction) {
      try {
        LoggingService.info(`Navigating to ${type}`, 'NAVIGATION');
        navigateAction();
        endTracking(trackingId, 'compute', { destination: type });
      } catch (error) {
        LoggingService.error(`Failed to navigate to ${type}`, 'NAVIGATION', error as Error);
        endTracking(trackingId, 'compute', { destination: type, error: true });
      }
    }
  }, [navigationMap, startTracking, endTracking]);

  const dashboardStats = useMemo(() => {
    const orders = state.orders || [];
    const products = state.products || [];
    const customers = state.customers || [];
    const todayStr = new Date().toISOString().split('T')[0];

    // ✅ FIX: Correctly filters orders from today by comparing the date part of the timestamp.
    const todaysSales = orders
      .filter((order: Order) => order.createdAt && order.createdAt.startsWith(todayStr) && order.status === 'Completed')
      .reduce((sum: number, order: Order) => sum + (order.total || 0), 0);

    return {
      todaysSales,
      totalOrders: orders.length,
      totalProducts: products.length,
      totalCustomers: customers.length,
    };
  }, [state.orders, state.products, state.customers]);

  // ✅ TYPE-SAFE: Data is now typed as StatCard[] from the start, removing the need for casting.
  const dashboardCards = useMemo((): StatCard[] => [
    {
      key: 'sales',
      title: "Today's Sales",
      value: formatCurrency(dashboardStats.todaysSales, { decimals: 0 }),
      icon: 'money',
      iconColor: theme.colors.primary,
      onPress: () => handleStatCardPress('sales'),
    },
    {
      key: 'orders',
      title: 'Total Orders',
      value: dashboardStats.totalOrders.toString(),
      icon: 'clipboard-text',
      iconColor: theme.colors.secondary,
      onPress: () => handleStatCardPress('orders'),
    },
    {
      key: 'products',
      title: 'Total Products',
      value: dashboardStats.totalProducts.toString(),
      icon: 'package',
      iconColor: theme.colors.tertiary,
      onPress: () => handleStatCardPress('products'),
    },
    {
      key: 'customers',
      title: 'Total Customers',
      value: dashboardStats.totalCustomers.toString(),
      icon: 'users',
      iconColor: theme.colors.primary,
      onPress: () => handleStatCardPress('customers'),
    },
    {
      key: 'profit',
      title: 'Net Profit',
      value: formatCurrency(profitLossData?.netProfit ?? 0, { decimals: 0 }),
      icon: 'chart-line',
      iconColor: (profitLossData?.netProfit ?? 0) >= 0 ? '#10B981' : '#EF4444',
      onPress: () => handleStatCardPress('financial'),
    },
    {
      key: 'expenses',
      title: 'Total Expenses',
      value: formatCurrency(profitLossData?.expenses ?? 0, { decimals: 0 }),
      icon: 'receipt',
      iconColor: '#F59E0B',
      onPress: () => handleStatCardPress('financial'),
    },
    
  ], [dashboardStats, theme.colors, profitLossData, handleStatCardPress]);

  if (!state.isDataLoaded) {
    return (
      <View style={[styles.container, styles.center, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size='large' color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onSurfaceVariant }]}>
          Loading dashboard...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header
        title='Dashboard'
        showBack={false}
        showSearch={false}
        actions={[
            { icon: 'qr-code', onPress: () => navigation.navigate('QRScanner') },
            { icon: 'magnifying-glass', onPress: () => navigation.navigate('Search') }
        ]}
        showNotifications={true}
        notificationCount={unreadCount}
        onNotificationPress={() => navigation.navigate('Notifications')}
      />
      <FlatList
        data={[{ key: 'dashboard-content' }]}
        renderItem={() => (
          <View style={styles.content}>
            <StatCardGroup cards={dashboardCards} columns={2} showTitle={false} />
            <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Quick Actions
            </Text>
            <View style={styles.quickActionsGrid}>
              {[
                {
                  label: 'Add Service',
                  icon: 'plus',
                  color: theme.colors.primary,
                  onPress: () => navigation.navigate('AddService'),
                },
                {
                  label: 'Scan QR',
                  icon: 'qr-code',
                  color: theme.colors.secondary,
                  onPress: () => navigation.navigate('QRScanner'),
                },
                {
                  label: 'Add Customer',
                  icon: 'user-plus',
                  color: theme.colors.tertiary,
                  onPress: () => navigation.navigate('AddCustomer'),
                },
                {
                  label: 'Add Item',
                  icon: 'package',
                  color: theme.colors.primary,
                  onPress: () => navigation.navigate('AddItem'),
                },
              ].map(({ label, icon, color, onPress }) => (
                <TouchableOpacity key={label} style={styles.quickActionItem} onPress={onPress}>
                  <View style={[styles.iconContainer, { backgroundColor: color }]}>
                    <PhosphorIcon name={icon as any} size={20} color={theme.colors.onPrimary} />
                  </View>
                  <Text style={[styles.quickActionText, { color: theme.colors.onSurface }]}>
                    {label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          </View>
        )}
        keyExtractor={item => item.key}
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: SPACING.md,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.md,
  },
  sectionContainer: {
    marginVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: SPACING.md,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.sm,
  },
  quickActionItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderRadius: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default withPerformanceTracking(DashboardScreen, 'DashboardScreen');