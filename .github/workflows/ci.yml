name: TailorZa Master CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  quality-checks:
    name: Code Quality & Testing
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript type checking
      run: npm run type-check
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run tests with coverage
      run: npm run test:coverage
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        fail_ci_if_error: false
        
    - name: Check bundle size
      run: npm run bundle:analyze
      
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level=moderate
      
    - name: Run npm audit fix
      run: npm audit fix --dry-run
      
  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: [quality-checks]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      
    - name: Build Android APK
      run: npm run android:build-debug
      
    - name: Upload APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: android-apk-${{ matrix.node-version }}
        path: android/app/build/outputs/apk/debug/
        
  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: [quality-checks]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.0'
        bundler-cache: true
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install CocoaPods dependencies
      run: cd ios && pod install
      
    - name: Build iOS app
      run: npm run ios:build || echo "iOS build not configured yet"
      
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [quality-checks]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run performance tests
      run: npm run test -- --testNamePattern="performance|Performance"
      
    - name: Bundle size analysis
      run: npm run build:analyze || echo "Bundle analysis not configured"
      
  deployment:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-checks, security-scan, build-android]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Deploy to Expo
      run: echo "Expo deployment would go here"
      # Add actual Expo deployment commands when ready
      
  notification:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [quality-checks, security-scan, build-android, build-ios, performance-tests]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ needs.quality-checks.result == 'success' && needs.security-scan.result == 'success' }}
      run: echo "✅ All quality checks passed!"
      
    - name: Notify on failure
      if: ${{ needs.quality-checks.result == 'failure' || needs.security-scan.result == 'failure' }}
      run: echo "❌ Quality checks failed. Please review the errors."