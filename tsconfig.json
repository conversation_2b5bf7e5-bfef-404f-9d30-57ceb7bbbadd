{"extends": "./node_modules/@tsconfig/react-native/tsconfig.json", "compilerOptions": {"strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "noEmit": true, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-native", "target": "es2017", "downlevelIteration": true, "baseUrl": ".", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "types": ["jest", "node"], "lib": ["es2017", "dom"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "src/scripts/**/*"]}